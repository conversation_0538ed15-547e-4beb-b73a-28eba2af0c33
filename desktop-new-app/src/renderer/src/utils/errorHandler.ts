import { AxiosError } from 'axios';

export interface ErrorState {
  id: string;
  message: string;
  type: 'network' | 'subscription' | 'validation' | 'server' | 'general' | 'session_conflict';
  timestamp: number;
  retryable: boolean;
  cooldownUntil?: number;
}

export interface UserFriendlyError {
  message: string;
  type: 'network' | 'subscription' | 'validation' | 'server' | 'general' | 'session_conflict';
  retryable: boolean;
  duration?: number;
}

class ErrorManager {
  private errorHistory: Map<string, ErrorState> = new Map();
  private readonly COOLDOWN_DURATION = 30000; // 30 seconds

  /**
   * Convert technical errors to user-friendly messages
   */
  public getUserFriendlyError(error: unknown): UserFriendlyError {
    // Handle Axios errors
    if (this.isAxiosError(error)) {
      return this.handleAxiosError(error);
    }

    // Handle subscription errors
    if (this.isSubscriptionError(error)) {
      return {
        message: 'Your subscription needs attention. Please check your subscription status.',
        type: 'subscription',
        retryable: false,
        duration: 15000 // Auto-dismiss after 15 seconds
      };
    }

    // Handle validation errors
    if (this.isValidationError(error)) {
      return {
        message: error.message || 'Please check your input and try again.',
        type: 'validation',
        retryable: false,
        duration: 8000
      };
    }

    // Handle network errors
    if (this.isNetworkError(error)) {
      return {
        message: 'Connection issue. Please check your internet and try again.',
        type: 'network',
        retryable: true,
        duration: 10000
      };
    }

    // Default error
    return {
      message: 'Something went wrong. Please try again or contact support if this persists.',
      type: 'general',
      retryable: true,
      duration: 8000
    };
  }

  /**
   * Check if an error should be displayed based on cooldown and frequency
   */
  public shouldDisplayError(error: unknown): boolean {
    const errorKey = this.getErrorKey(error);
    const now = Date.now();
    const existingError = this.errorHistory.get(errorKey);

    if (existingError) {
      // Check if we're still in cooldown period
      if (existingError.cooldownUntil && now < existingError.cooldownUntil) {
        return false;
      }

      // Check if we've shown this error too many times recently
      const timeSinceLastError = now - existingError.timestamp;
      if (timeSinceLastError < this.COOLDOWN_DURATION) {
        return false;
      }
    }

    // Update error history
    this.errorHistory.set(errorKey, {
      id: errorKey,
      message: this.getUserFriendlyError(error).message,
      type: this.getUserFriendlyError(error).type,
      timestamp: now,
      retryable: this.getUserFriendlyError(error).retryable,
      cooldownUntil: now + this.COOLDOWN_DURATION
    });

    return true;
  }

  /**
   * Clear error history for a specific error type
   */
  public clearErrorHistory(errorType?: string) {
    if (errorType) {
      for (const [key, error] of this.errorHistory.entries()) {
        if (error.type === errorType) {
          this.errorHistory.delete(key);
        }
      }
    } else {
      this.errorHistory.clear();
    }
  }

  private handleAxiosError(error: AxiosError): UserFriendlyError {
    const status = error.response?.status;
    const data = error.response?.data as { detail?: any; code?: string; message?: string };

    switch (status) {
      case 401:
        return {
          message: 'Your session has expired. Please log in again.',
          type: 'general',
          retryable: false,
          duration: 8000
        };

      case 403:
        if (data?.detail?.includes('subscription')) {
          return {
            message: 'Your subscription has expired. Please renew to continue.',
            type: 'subscription',
            retryable: false,
            duration: 15000
          };
        }
        return {
          message: 'You don\'t have permission to perform this action.',
          type: 'general',
          retryable: false,
          duration: 8000
        };

      case 404:
        return {
          message: 'The requested resource was not found.',
          type: 'general',
          retryable: false,
          duration: 6000
        };

      case 408:
      case 504:
        return {
          message: 'Request timed out. Please try again.',
          type: 'network',
          retryable: true,
          duration: 8000
        };

      case 409:
        // Handle session conflicts specifically - check both data.code and data.detail.code
        if (data?.code === 'SESSION_CONFLICT' || data?.detail?.code === 'SESSION_CONFLICT') {
          const conflictData = data?.code === 'SESSION_CONFLICT' ? data : data?.detail;
          if (conflictData?.conflict_type === 'DIFFERENT_BROWSER_LOGIN') {
            return {
              message: '⚠️ Session Conflict: Another session is active on a different browser/device. Please logout from the other device first or contact support.',
              type: 'session_conflict',
              retryable: false,
              duration: 15000
            };
          } else {
            return {
              message: '⚠️ Session Conflict: Another session is active on a different device. Please try again or contact support.',
              type: 'session_conflict',
              retryable: false,
              duration: 12000
            };
          }
        }
        return {
          message: typeof data?.detail === 'string' ? data.detail : (data?.message || 'A conflict occurred. Please try again.'),
          type: 'general',
          retryable: false,
          duration: 8000
        };

      case 429:
        return {
          message: 'Too many requests. Please wait a moment and try again.',
          type: 'general',
          retryable: true,
          duration: 10000
        };

      case 500:
      case 502:
      case 503:
        return {
          message: 'Server is temporarily unavailable. Please try again in a few moments.',
          type: 'server',
          retryable: true,
          duration: 10000
        };

      default:
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          return {
            message: 'Request timed out. Please check your connection and try again.',
            type: 'network',
            retryable: true,
            duration: 8000
          };
        }

        // Handle complex error objects properly
        let errorMessage = 'An unexpected error occurred. Please try again.';
        if (typeof data?.detail === 'string') {
          errorMessage = data.detail;
        } else if (typeof data?.message === 'string') {
          errorMessage = data.message;
        } else if (typeof data?.detail === 'object' && data?.detail?.message) {
          errorMessage = data.detail.message;
        }

        return {
          message: errorMessage,
          type: 'general',
          retryable: true,
          duration: 8000
        };
    }
  }

  private isAxiosError(error: unknown): error is AxiosError {
    return (error as AxiosError)?.isAxiosError === true || (error as AxiosError)?.response !== undefined;
  }

  private isSubscriptionError(error: unknown): boolean {
    const axiosError = error as AxiosError;
    const message = (axiosError?.message || (axiosError?.response?.data as any)?.detail || '') as string;
    return message.toLowerCase().includes('subscription') ||
           message.toLowerCase().includes('expired') ||
           axiosError?.response?.status === 403;
  }

  private isValidationError(error: unknown): boolean {
    const axiosError = error as AxiosError;
    return axiosError?.response?.status === 400 ||
           (error as Error)?.name === 'ValidationError' ||
           (error as any)?.type === 'validation';
  }

  private isNetworkError(error: unknown): boolean {
    const axiosError = error as AxiosError;
    return axiosError?.code === 'NETWORK_ERROR' ||
           axiosError?.code === 'ECONNABORTED' ||
           axiosError?.message?.includes('Network Error') ||
           !axiosError?.response;
  }

  private getErrorKey(error: unknown): string {
    // Create a unique key for the error to track duplicates
    const axiosError = error as AxiosError;
    const status = axiosError?.response?.status || 'unknown';
    const message = (axiosError?.message || (axiosError?.response?.data as any)?.detail || 'unknown') as string;
    const url = axiosError?.config?.url || 'unknown';

    return `${status}-${url}-${message}`.substring(0, 100);
  }
}

// Export singleton instance
export const errorManager = new ErrorManager();

// Helper functions for common use cases
export const getUserFriendlyError = (error: unknown): UserFriendlyError => {
  return errorManager.getUserFriendlyError(error);
};

export const shouldDisplayError = (error: unknown): boolean => {
  return errorManager.shouldDisplayError(error);
};

export const clearErrorHistory = (errorType?: string) => {
  errorManager.clearErrorHistory(errorType);
};

// Log errors for debugging while showing user-friendly messages
export const logAndGetUserError = (error: unknown, _context?: string): UserFriendlyError => {
  return getUserFriendlyError(error);
};
