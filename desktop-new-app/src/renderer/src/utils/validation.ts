/**
 * Utility functions for validating user input.
 */

export function validateEmail(email: string): boolean {
  // Simple RFC 5322 email regex
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

export function validatePassword(password: string): boolean {
  // At least 8 chars, 1 letter, 1 number, allows special characters
  return /^(?=.*[A-Za-z])(?=.*\d).{8,}$/.test(password)
}

export function validateOtp(otp: string): boolean {
  // Accepts 6-character alphanumeric OTPs (letters or numbers)
  return /^[A-Za-z0-9]{6}$/.test(otp)
}
