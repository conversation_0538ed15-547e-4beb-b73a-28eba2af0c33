@import "tailwindcss";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 210 40% 98%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
 
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* .versions {
  position: absolute;
  bottom: 30px;
  margin: 0 auto;
  padding: 15px 0;
  font-family: 'Menlo', 'Lucida Console', monospace;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  border-radius: 22px;
  background-color: var(--dark-theme-bg-card);
  backdrop-filter: blur(24px);
} */

code {
  font-weight: 600;
  padding: 3px 5px;
  border-radius: 2px;
  background-color: hsl(var(--card));
  font-family:
    ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  font-size: 85%;
}

.logo {
  margin-bottom: 20px;
  -webkit-user-drag: none;
  height: 128px;
  width: 128px;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 1.2em hsl(var(--primary)));
}

.creator {
  font-size: 14px;
  line-height: 16px;
  color: hsl(var(--muted-foreground));
  font-weight: 600;
  margin-bottom: 10px;
}

.text {
  font-size: 28px;
  color: hsl(var(--foreground));
  font-weight: 700;
  line-height: 32px;
  text-align: center;
  margin: 0 10px;
  padding: 16px 0;
}

.tip {
  font-size: 16px;
  line-height: 24px;
  color: hsl(var(--muted-foreground));
  font-weight: 600;
}

.react {
  background: -webkit-linear-gradient(315deg, #087ea4 55%, #7c93ee);
  background-clip: text;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.ts {
  background: -webkit-linear-gradient(315deg, #3178c6 45%, #f0dc4e);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.actions {
  display: flex;
  padding-top: 32px;
  margin: -6px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action {
  flex-shrink: 0;
  padding: 6px;
}

.action a {
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  text-align: center;
  font-weight: 600;
  white-space: nowrap;
  border-radius: 20px;
  padding: 0 20px;
  line-height: 38px;
  font-size: 14px;
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
}

.action a:hover {
  border-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  background-color: hsl(var(--secondary));
}

.versions {
  position: absolute;
  bottom: 30px;
  margin: 0 auto;
  padding: 15px 0;
  font-family: 'Menlo', 'Lucida Console', monospace;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  border-radius: 22px;
  background-color: hsl(var(--card));
  backdrop-filter: blur(24px);
}

.versions li {
  display: block;
  float: left;
  border-right: 1px solid hsl(var(--border));
  padding: 0 20px;
  font-size: 14px;
  line-height: 14px;
  opacity: 0.8;
  &:last-child {
    border: none;
  }
}

/* Glassmorphism utility classes */
.glass-card {
  background: hsl(var(--card) / 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 32px 0 hsl(var(--primary) / 0.15);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid hsl(var(--border));
}

.glass-input {
  background: hsl(var(--card) / 0.5);
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: hsl(var(--foreground));
}

.glass-input:focus {
  outline: 2px solid hsl(var(--accent));
  background: hsl(var(--secondary));
}

.glass-btn {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.4);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: background 0.2s, box-shadow 0.2s;
}

.glass-btn:hover {
  background: hsl(var(--accent));
  box-shadow: 0 4px 16px hsl(var(--primary) / 0.4);
}

/* General spacing and layout improvements */
.space-y-lg > *:not(:last-child) {
  margin-bottom: 1.5rem; /* 24px */
}

.space-x-lg > *:not(:last-child) {
  margin-right: 1.5rem; /* 24px */
}

@media (max-width: 720px) {
  .text {
    font-size: 20px;
  }
}

@media (max-width: 620px) {
  .versions {
    display: none;
  }
}

@media (max-width: 350px) {
  .tip,
  .actions {
    display: none;
  }
}

/* Utility for muted/secondary text */
.muted-text {
  color: hsl(var(--muted-foreground));
}

/* Utility for always-visible headings */
.heading-text {
  color: hsl(var(--foreground));
  font-weight: 700;
}

/* Cyber text gradient for dark theme */
.cyber-text-gradient {
  background: linear-gradient(45deg, #00aaff, #aa00ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Glow effect for dark theme */
.dark\:shadow-glow-blue-sm {
  box-shadow: 0 0 8px rgba(0, 170, 255, 0.3);
}

.dark\:shadow-glow-green-sm {
  box-shadow: 0 0 8px rgba(0, 255, 149, 0.3);
}
