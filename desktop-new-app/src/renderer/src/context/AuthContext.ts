import { createContext } from 'react'

export interface AuthContextType {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
  login: (credentials: LoginCredentials) => Promise<LoginResponse>
  signup: (credentials: SignupCredentials) => Promise<SignupResponse>
  logout: () => Promise<void>
  verifyOTP: (data: { email: string; otp: string }) => Promise<VerifyEmailResponse>
  resendOTP: (email: string) => Promise<ResendOtpResponse>
}

// User type and backend types are imported from backendService
import {
  LoginCredentials,
  LoginResponse,
  SignupCredentials,
  SignupResponse,
  VerifyEmailResponse,
  ResendOtpResponse
} from '../../../shared/api'

export interface User {
  id: string
  email: string
  initial?: string
  name?: string
  designation?: string
  police_station?: string
  district?: string
  state?: string
  is_active: boolean
  email_verified: boolean
  roles: string[]
  created_at: string // ISO 8601 string
  last_login?: string // ISO 8601 string
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)
