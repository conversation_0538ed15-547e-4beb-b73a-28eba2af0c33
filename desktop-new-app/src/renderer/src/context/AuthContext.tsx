import React, { useEffect, ReactNode } from 'react'
import { validateEmail, validatePassword, validateOtp } from '../utils/validation'
import { backendService } from '../services/backendService'
import {
  LoginCredentials,
  LoginResponse,
  SignupCredentials,
  SignupResponse,
  VerifyEmailResponse,
  ResendOtpResponse
} from '../../../shared/api'
import { AuthContext, User } from './AuthContext'

type State = {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
}

type Action =
  | { type: 'SET_AUTH'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_LOADING'; payload: boolean }

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_AUTH':
      return { ...state, isAuthenticated: action.payload }
    case 'SET_USER':
      return { ...state, user: action.payload }
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    default:
      return state
  }
}

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, {
    isAuthenticated: false,
    isLoading: true,
    user: null
  })

  useEffect(() => {
    const checkAuth = async (): Promise<void> => {
      try {
        const token = await backendService.getToken()
        if (token) {
          dispatch({ type: 'SET_AUTH', payload: true })
          const sessionUser = (await backendService.getSession()) as User | null
          dispatch({ type: 'SET_USER', payload: sessionUser })
        } else {
          dispatch({ type: 'SET_AUTH', payload: false })
          dispatch({ type: 'SET_USER', payload: null })
        }
      } catch (error) {
        console.error('Failed to check authentication status:', error)
        dispatch({ type: 'SET_AUTH', payload: false })
        dispatch({ type: 'SET_USER', payload: null })
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    }
    checkAuth()
  }, [])

  const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    if (!validateEmail(credentials.email) || !validatePassword(credentials.password)) {
      return { success: false, error: 'Invalid email or password format.' }
    }
    dispatch({ type: 'SET_LOADING', payload: true })
    try {
      const result = await backendService.login(credentials)
      if (result.success) {
        dispatch({ type: 'SET_AUTH', payload: true })
        const sessionUser = (await backendService.getSession()) as User | null
        dispatch({ type: 'SET_USER', payload: sessionUser })
      } else {
        dispatch({ type: 'SET_AUTH', payload: false })
        dispatch({ type: 'SET_USER', payload: null })
      }
      return result
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message || 'An unexpected error occurred during login.'
      }
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const signup = async (credentials: SignupCredentials): Promise<SignupResponse> => {
    if (!validateEmail(credentials.email) || !validatePassword(credentials.password)) {
      return { success: false, error: 'Invalid email or password format.' }
    }
    try {
      const result = await backendService.signup(credentials)
      return result
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message || 'An unexpected error occurred during signup.'
      }
    }
  }

  const logout = async (): Promise<void> => {
    await backendService.logout()
    dispatch({ type: 'SET_AUTH', payload: false })
    dispatch({ type: 'SET_USER', payload: null })
  }

  const verifyOTP = async (data: { email: string; otp: string }): Promise<VerifyEmailResponse> => {
    if (!validateEmail(data.email) || !validateOtp(data.otp)) {
      return { success: false, error: 'Invalid email or OTP format.' }
    }
    try {
      const response = await backendService.verifyEmail(data.email, data.otp)
      return response
    } catch (error) {
      console.error('Error verifying OTP:', error)
      return {
        success: false,
        error: (error as Error).message || 'An unexpected error occurred during OTP verification.'
      }
    }
  }

  const resendOTP = async (email: string): Promise<ResendOtpResponse> => {
    if (!validateEmail(email)) {
      return { success: false, error: 'Invalid email format.' }
    }
    try {
      const response = await backendService.resendOtp(email)
      return response
    } catch (error) {
      console.error('Error resending OTP:', error)
      return {
        success: false,
        error: (error as Error).message || 'An unexpected error occurred while resending OTP.'
      }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated: state.isAuthenticated,
        isLoading: state.isLoading,
        user: state.user,
        login,
        signup,
        logout,
        verifyOTP,
        resendOTP
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
