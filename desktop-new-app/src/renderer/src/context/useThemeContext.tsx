import { useContext } from 'react'
import { ThemeContext, ThemeContextType } from './ThemeContextBase'

export function useThemeContext(): ThemeContextType {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider')
  }
  return context
}

// Export useTheme as an alias for backward compatibility
export const useTheme = useThemeContext
