import { createContext, ReactNode } from 'react'

export interface AlertContextType {
  showSuccess: (message: ReactNode, duration?: number) => void
  showError: (message: ReactNode, duration?: number) => void
  showInfo: (message: ReactNode, duration?: number) => void
  showWarning: (message: ReactNode, duration?: number) => void
  showSmartError: (error: unknown, context?: string) => void
}

export const TailwindAlertContext = createContext<AlertContextType | undefined>(undefined)
