import React, { useState, ReactNode } from 'react'
import AlertContainer from '../components/ui/AlertContainer'
import { shouldDisplayError, logAndGetUserError } from '../utils/errorHandler'
import { TailwindAlertContext } from './alertContext'

type AlertSeverity = 'success' | 'info' | 'warning' | 'error'

interface AlertItem {
  id: number
  message: ReactNode
  severity: AlertSeverity
  duration: number
  retryable?: boolean
}

interface AlertProviderProps {
  children: ReactNode
}

export const TailwindAlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [alerts, setAlerts] = useState<AlertItem[]>([])

  const showAlert = (
    message: ReactNode,
    severity: AlertSeverity,
    duration: number = 6000
  ): void => {
    const timestamp = Date.now()
    const randomPart = Math.floor(Math.random() * 10000)
    const id = timestamp + randomPart

    const newAlert = { id, message, severity, duration }

    setAlerts((prev) => {
      const exists = prev.some((alert) => alert.id === id)
      if (exists) {
        return [...prev, { ...newAlert, id: id + 1 }]
      }
      return [...prev, newAlert]
    })

    setTimeout(() => {
      setAlerts((prev) => prev.filter((alert) => alert.id !== id))
    }, duration + 500)
  }

  const showSuccess = (message: ReactNode, duration?: number): void =>
    showAlert(message, 'success', duration)
  const showError = (message: ReactNode, duration?: number): void =>
    showAlert(message, 'error', duration)
  const showInfo = (message: ReactNode, duration?: number): void =>
    showAlert(message, 'info', duration)
  const showWarning = (message: ReactNode, duration?: number): void =>
    showAlert(message, 'warning', duration)

  const showSmartError = (error: unknown, context?: string): void => {
    if (!shouldDisplayError(error)) {
      return
    }
    const userError = logAndGetUserError(error, context)
    showAlert(userError.message, 'error', userError.duration)
  }

  const handleClose = (id: number): void => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id))
  }

  return (
    <TailwindAlertContext.Provider
      value={{ showSuccess, showError, showInfo, showWarning, showSmartError }}
    >
      {children}
      <AlertContainer alerts={alerts} onClose={handleClose} />
    </TailwindAlertContext.Provider>
  )
}
