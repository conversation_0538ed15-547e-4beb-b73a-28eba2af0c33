// backendService.ts
// Handles authentication and backend API calls via Electron IPC

import {
  API,
  LoginCredentials,
  LoginResponse,
  SignupCredentials,
  SignupResponse,
  VerifyEmailResponse,
  ResendOtpResponse
} from '../../../shared/api'

// Extend the Window interface to include the 'api' property
declare global {
  interface Window {
    api: API
  }
}

export const backendService = {
  async setToken(token: string): Promise<void> {
    await window.api?.auth?.setToken?.(token)
  },
  async getToken(): Promise<string | null> {
    try {
      const result = await window.api?.auth?.getToken?.()
      if (result?.error) {
        console.error('[backendService] getToken error:', result.error)
        return null
      }
      console.log('[backendService] getToken result:', result)
      return result?.token ?? null
    } catch (e) {
      console.error('[backendService] getToken exception:', e)
      return null
    }
  },
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      if (!window.api?.auth?.login) {
        return { success: false, error: 'Electron API not available' }
      }
      const result = await window.api.auth.login(credentials)
      if (result && result.token) {
        await this.setToken(result.token)
        return { success: true, token: result.token }
      }
      return { success: false, error: result?.error || 'Invalid credentials' }
    } catch (err: unknown) {
      return { success: false, error: (err as Error).message || 'Login failed' }
    }
  },
  async signup(credentials: SignupCredentials): Promise<SignupResponse> {
    try {
      if (!window.api?.auth?.signup) {
        return { success: false, error: 'Electron API not available' }
      }
      // Map to backend expected keys if needed
      const backendPayload = {
        ...credentials,
        initial: credentials.initial,
        name: credentials.name
      }
      const result = await window.api.auth.signup(backendPayload)
      // The backend now returns `requires_verification` and `email`
      return {
        success: result.success,
        token: result.token,
        error: result.error,
        message: result.message,
        requires_verification: result.requires_verification,
        email: result.email
      }
    } catch (err: unknown) {
      return { success: false, error: (err as Error).message || 'Signup failed' }
    }
  },
  async logout(): Promise<void> {
    if (window.api?.auth?.logout) {
      await window.api.auth.logout()
    }
    await window.api?.auth?.clearToken?.()
  },
  async getSession(): Promise<unknown> {
    if (window.api?.auth?.getSession) {
      return window.api.auth.getSession()
    }
    return null
  },
  async hasToken(): Promise<boolean> {
    if (window.api?.auth?.hasToken) {
      return window.api.auth.hasToken()
    }
    return false
  },
  async verifyEmail(email: string, otp: string): Promise<VerifyEmailResponse> {
    try {
      if (!window.api?.auth?.verifyEmail) {
        return { success: false, error: 'Electron API for email verification not available' }
      }
      const result = await window.api.auth.verifyEmail({ email, otp })
      return { success: result.success, message: result.message, error: result.error }
    } catch (err: unknown) {
      return { success: false, error: (err as Error).message || 'Email verification failed' }
    }
  },
  async resendOtp(email: string): Promise<ResendOtpResponse> {
    try {
      if (!window.api?.auth?.resendOtp) {
        return { success: false, error: 'Electron API for resending OTP not available' }
      }
      const result = await window.api.auth.resendOtp({ email })
      return { success: result.success, message: result.message, error: result.error }
    } catch (err: unknown) {
      return { success: false, error: (err as Error).message || 'Failed to resend OTP' }
    }
  },

  // Local Database Operations
  async storeComplaintLocally(complaint: {
    title: string
    description: string
    extracted_data?: Record<string, unknown>
  }): Promise<string> {
    try {
      if (!window.api?.database?.storeComplaint) {
        throw new Error('Database API not available')
      }
      return await window.api.database.storeComplaint(complaint)
    } catch (err: unknown) {
      throw new Error((err as Error).message || 'Failed to store complaint locally')
    }
  },

  async getLocalComplaints(): Promise<import('../../../shared/api').ComplaintData[]> {
    try {
      if (!window.api?.database?.getComplaints) {
        throw new Error('Database API not available')
      }
      return await window.api.database.getComplaints()
    } catch (err: unknown) {
      throw new Error((err as Error).message || 'Failed to get local complaints')
    }
  },

  async getLocalComplaint(id: string): Promise<import('../../../shared/api').ComplaintData | null> {
    try {
      if (!window.api?.database?.getComplaint) {
        throw new Error('Database API not available')
      }
      return await window.api.database.getComplaint(id)
    } catch (err: unknown) {
      throw new Error((err as Error).message || 'Failed to get local complaint')
    }
  },

  async deleteLocalComplaint(id: string): Promise<boolean> {
    try {
      if (!window.api?.database?.deleteComplaint) {
        throw new Error('Database API not available')
      }
      return await window.api.database.deleteComplaint(id)
    } catch (err: unknown) {
      throw new Error((err as Error).message || 'Failed to delete local complaint')
    }
  },

  async updateComplaint(
    id: string,
    data: Partial<import('../../../shared/api').ComplaintData>
  ): Promise<boolean> {
    try {
      if (!window.api?.database?.updateComplaint) {
        throw new Error('Database API not available')
      }
      return await window.api.database.updateComplaint(id, data)
    } catch (err: unknown) {
      throw new Error((err as Error).message || 'Failed to update local complaint')
    }
  },


}
