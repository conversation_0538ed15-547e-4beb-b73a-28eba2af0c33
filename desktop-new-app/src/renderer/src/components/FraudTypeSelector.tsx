import React from 'react'
import { fraudTypes } from '../lib/utils'

interface FraudTypeSelectorProps {
  selectedType: string | null
  onTypeSelect: (type: string, extractionType: string) => void
}

const FraudTypeSelector: React.FC<FraudTypeSelectorProps> = ({ selectedType, onTypeSelect }) => {
  return (
    <div className="p-4 rounded-lg border bg-gray-700 bg-opacity-50 border-gray-600">
      <h3 className="text-sm font-medium mb-3 text-white">Fraud Type</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {fraudTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => onTypeSelect(type.id, type.id)}
            className={`p-3 rounded-lg text-left transition-all duration-200 ${
              selectedType === type.id
                ? 'bg-purple-600 text-white shadow-lg'
                : 'bg-gray-800 hover:bg-gray-700'
            }`}
          >
            <span className="font-semibold">{type.name}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

export default FraudTypeSelector
