import React, { useMemo, useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  CellContext,
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  getPaginationRowModel,
  PaginationState
} from '@tanstack/react-table'
import {
  FiEye,
  FiEdit,
  FiTrash2,
  FiChevronLeft,
  FiChevronRight,
  FiFileText,
  FiMoreVertical
} from 'react-icons/fi'
import { FaSort, FaSortUp, FaSortDown } from 'react-icons/fa'

// Types
interface Complaint {
  id: string
  complaint_number: string
  complainant_name: string
  category_of_fraud: string
  date_of_complaint: string
  amount: number
  status: string
  metadata?: {
    total_amount?: string | number
    complainant_name?: string
    category?: string
    subcategory?: string
    date?: string
    [key: string]: unknown
  }
}

interface TransactionTableProps {
  complaints: Complaint[]
  loading: boolean
  onView: (complaintNumber: string) => void
  onEdit: (complaint: Complaint) => void
  onDelete: (complaintNumber: string) => void
  onSummary: (complaintNumber: string) => void
}

const TransactionTable: React.FC<TransactionTableProps> = ({
  complaints,
  loading,
  onEdit,
  onDelete,
  onSummary
}) => {
  const navigate = useNavigate()
  const [sorting, setSorting] = useState<SortingState>([])
  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10
  })
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)
  const [editingRowId, setEditingRowId] = useState<string | null>(null)
  const [editedData, setEditedData] = useState<Partial<Complaint>>({})
  const editedDataRef = React.useRef(editedData)
  editedDataRef.current = editedData

  const handleEditClick = useCallback((complaint: Complaint): void => {
    setEditingRowId(complaint.id)
    setEditedData(complaint)
  }, [])

  const handleCancelClick = useCallback((): void => {
    setEditingRowId(null)
    setEditedData({})
  }, [])

  const handleSaveClick = useCallback((): void => {
    if (editingRowId) {
      onEdit({ ...editedDataRef.current } as Complaint)
      setEditingRowId(null)
      setEditedData({})
    }
  }, [editingRowId, onEdit])

  const handleInputChange = useCallback((field: keyof Complaint, value: string): void => {
    setEditedData((prev) => ({ ...prev, [field]: value }))
  }, [])

  // Custom EditableCell component
  const EditableCell = React.memo(function EditableCell({
    value: initialValue,
    field,
    type = 'text',
    options
  }: {
    value: string | number | undefined
    field: keyof Complaint
    type?: string
    options?: string[]
  }) {
    const [cellValue, setCellValue] = useState(initialValue || '')
    const inputRef = React.useRef<HTMLInputElement | HTMLSelectElement>(null)

    const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>): void => {
      setCellValue(e.target.value)
      handleInputChange(field, e.target.value)
    }

    if (type === 'select' && options) {
      return (
        <select
          value={cellValue as string}
          onChange={onChange}
          className="bg-transparent border-b-2 border-gray-500"
        >
          {options.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      )
    }

    return (
      <input
        type={type}
        value={cellValue as string}
        onChange={onChange}
        className="bg-transparent border-b-2 border-gray-500"
        ref={inputRef as React.RefObject<HTMLInputElement>}
      />
    )
  })

  const columnHelper = createColumnHelper<Complaint>()

  const formatCurrency = (amount: number | string | undefined): string => {
    if (amount === undefined) {
      return ''
    }
    const numericAmount = typeof amount === 'string' ? parseFloat(amount.replace(/,/g, '')) : amount
    if (isNaN(numericAmount)) {
      return ''
    }
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(numericAmount)
  }

  const getStatusBadge = (status: string): React.ReactNode => {
    const lowercaseStatus = status?.toLowerCase() || ''

    const baseClasses = 'px-3 py-1 rounded-full text-xs font-semibold'
    if (lowercaseStatus === 'resolved' || lowercaseStatus === 'completed') {
      return <span className={`${baseClasses} bg-green-200 text-green-800`}>Resolved</span>
    }
    if (lowercaseStatus === 'pending') {
      return <span className={`${baseClasses} bg-yellow-200 text-yellow-800`}>Pending</span>
    }
    if (lowercaseStatus === 'in progress' || lowercaseStatus === 'processing') {
      return <span className={`${baseClasses} bg-blue-200 text-blue-800`}>In Progress</span>
    }
    return <span className={`${baseClasses} bg-muted text-muted-foreground`}>{status}</span>
  }

  const columns = useMemo(
    () => [
      columnHelper.display({
        id: 'sr_no',
        header: 'SR No.',
        cell: (info) => info.row.index + 1
      }),
      columnHelper.accessor('complaint_number', {
        header: 'Complaint Number',
        cell: (info) => info.row.original.metadata?.complaint_number || info.getValue()
      }),
      columnHelper.accessor('complainant_name', {
        header: 'Complainant Name',
        cell: (info) =>
          editingRowId === info.row.original.id ? (
            <EditableCell
              key={info.row.original.id + '-complainant_name'}
              value={editedDataRef.current.complainant_name}
              field="complainant_name"
            />
          ) : (
            info.row.original.metadata?.complainant_name || info.getValue()
          )
      }),
      columnHelper.accessor('amount', {
        header: 'Amount',
        cell: (info) => formatCurrency(info.getValue())
      }),
      columnHelper.accessor('date_of_complaint', {
        header: 'Date of Complaint',
        cell: (info) => {
          const dateString = info.getValue()
          if (!dateString || typeof dateString !== 'string') {
            return ''
          }
          // Handle dd/mm/yyyy format from metadata
          if (dateString.includes('/')) {
            const [day, month, year] = dateString.split('/')
            if (day && month && year) {
              const date = new Date(`${year}-${month}-${day}`)
              return date.toLocaleDateString('en-IN')
            }
          }
          // Handle ISO string from created_at
          return new Date(dateString).toLocaleDateString('en-IN')
        }
      }),
      columnHelper.accessor('category_of_fraud', {
        header: 'Sub Category',
        cell: (info) =>
          editingRowId === info.row.original.id ? (
            <EditableCell
              key={info.row.original.id + '-category_of_fraud'}
              value={editedDataRef.current.category_of_fraud}
              field="category_of_fraud"
            />
          ) : (
            info.row.original.metadata?.subcategory || info.getValue()
          )
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: (info) =>
          editingRowId === info.row.original.id ? (
            <EditableCell
              key={info.row.original.id + '-status'}
              value={editedDataRef.current.status}
              field="status"
              type="select"
              options={['pending', 'resolved', 'in progress']}
            />
          ) : (
            getStatusBadge(info.getValue())
          )
      }),
      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        cell: (props: CellContext<Complaint, unknown>) =>
          editingRowId === props.row.original.id ? (
            <div className="flex gap-2">
              <button onClick={() => handleSaveClick()} className="text-green-500">
                Save
              </button>
              <button onClick={() => handleCancelClick()} className="text-red-500">
                Cancel
              </button>
            </div>
          ) : (
            <div className="relative flex justify-center">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  setOpenDropdownId(
                    openDropdownId === props.row.original.id ? null : props.row.original.id
                  )
                }}
                className="p-2 rounded-full hover:bg-muted transition-colors"
              >
                <FiMoreVertical className="text-foreground" />
              </button>
              {openDropdownId === props.row.original.id && (
                <div
                  className="absolute top-full right-0 mt-2 w-48 bg-background rounded-lg shadow-lg z-20 border border-border"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ul className="py-1 text-foreground">
                    <li
                      className="flex items-center px-4 py-2 text-sm hover:bg-muted cursor-pointer"
                      onClick={() => {
                        handleEditClick(props.row.original)
                        setOpenDropdownId(null)
                      }}
                    >
                      <FiEdit className="mr-3" /> Edit
                    </li>
                    <li
                      className="flex items-center px-4 py-2 text-sm hover:bg-muted cursor-pointer"
                      onClick={() => {
                        navigate(`/complaint-details/${props.row.original.id}`)
                        setOpenDropdownId(null)
                      }}
                    >
                      <FiEye className="mr-3" /> Details
                    </li>
                    <li
                      className="flex items-center px-4 py-2 text-sm text-destructive hover:bg-muted cursor-pointer"
                      onClick={() => {
                        onDelete(props.row.original.complaint_number)
                        setOpenDropdownId(null)
                      }}
                    >
                      <FiTrash2 className="mr-3" /> Delete
                    </li>
                    <li
                      className="flex items-center px-4 py-2 text-sm hover:bg-muted cursor-pointer"
                      onClick={() => {
                        onSummary(props.row.original.complaint_number)
                        setOpenDropdownId(null)
                      }}
                    >
                      <FiFileText className="mr-3" /> Summary
                    </li>
                  </ul>
                </div>
              )}
            </div>
          )
      })
    ],
    [
      columnHelper,
      editingRowId,
      EditableCell,
      openDropdownId,
      handleSaveClick,
      handleCancelClick,
      handleEditClick,
      navigate,
      onDelete,
      onSummary
    ]
  )

  const table = useReactTable({
    data: complaints,
    columns,
    state: {
      sorting,
      pagination: { pageIndex, pageSize }
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel()
  })

  return (
    <div className="w-full overflow-x-auto bg-card rounded-xl p-4">
      <table className="w-full border-collapse border border-border">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="p-4 text-left text-sm font-bold text-muted-foreground uppercase tracking-wider border-b-2 border-r border-border last:border-r-0"
                  onClick={header.column.getToggleSortingHandler()}
                >
                  <div className="flex items-center gap-2">
                    {flexRender(header.column.columnDef.header, header.getContext())}
                    {{
                      asc: <FaSortUp />,
                      desc: <FaSortDown />
                    }[header.column.getIsSorted() as string] ?? <FaSort />}
                  </div>
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td
                colSpan={columns.length}
                className="text-center p-6 text-muted-foreground border-r border-border last:border-r-0"
              >
                Loading...
              </td>
            </tr>
          ) : table.getRowModel().rows.length === 0 ? (
            <tr>
              <td
                colSpan={columns.length}
                className="text-center p-6 text-muted-foreground border-r border-border last:border-r-0"
              >
                No complaints found.
              </td>
            </tr>
          ) : (
            table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className="border-b border-border hover:bg-muted transition-colors last:border-b-0"
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className="p-4 text-foreground border-r border-border last:border-r-0"
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
      <div className="flex items-center justify-between mt-6 text-foreground">
        <div className="text-sm">
          Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className="p-2 rounded-md bg-muted hover:bg-accent disabled:opacity-50"
          >
            First
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="p-2 rounded-md bg-muted hover:bg-accent disabled:opacity-50"
          >
            <FiChevronLeft />
          </button>
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="p-2 rounded-md bg-muted hover:bg-accent disabled:opacity-50"
          >
            <FiChevronRight />
          </button>
          <button
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
            className="p-2 rounded-md bg-muted hover:bg-accent disabled:opacity-50"
          >
            Last
          </button>
        </div>
      </div>
    </div>
  )
}

export default React.memo(TransactionTable)
