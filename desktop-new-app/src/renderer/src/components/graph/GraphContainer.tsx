import React, { useEffect, useCallback } from 'react'
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  Panel
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

// Import node components
import { TransactionNode } from './nodes/TransactionNode'
import { MetadataNode } from './nodes/MetadataNode'
import { useThemeContext } from '../../context/useThemeContext'
import { useAlert } from '../../hooks/useAlert'
import { ComplaintGraphData } from '../../../../shared/api'

// Define node types using ReactFlow's standard approach
const nodeTypes = {
  transaction: TransactionNode,
  metadata: MetadataNode
}

interface GraphContainerProps {
  complaintData: ComplaintGraphData
  complaintId: string
  onExportStateChange?: (isExporting: boolean) => void
  onExportProgressChange?: (progress: {
    current: number
    total: number
    currentPage?: string
  }) => void
}

export const GraphContainer: React.FC<GraphContainerProps> = ({ complaintData, complaintId }) => {
  const { isDark } = useThemeContext()
  const { showError } = useAlert()

  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([])

  // Simple state for tracking node visibility

  // Handle new connections between nodes
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  // Process graph data when complaint data changes
  useEffect(() => {
    if (!complaintData) return

    try {
      // Use the pre-processed graph data from the backend
      const initialNodes: Node[] = []
      const initialEdges: Edge[] = []

      // Convert graph nodes to ReactFlow nodes
      if (complaintData.nodes && Array.isArray(complaintData.nodes)) {
        complaintData.nodes.forEach((node) => {
          initialNodes.push({
            id: node.id,
            type: node.type || 'transaction',
            position: node.position || { x: 0, y: 0 },
            data: {
              ...node.data,
              label: node.data?.label || node.id
            }
          })
        })
      }

      // Convert graph edges to ReactFlow edges
      if (complaintData.edges && Array.isArray(complaintData.edges)) {
        complaintData.edges.forEach((edge) => {
          initialEdges.push({
            id: edge.id,
            source: edge.source,
            target: edge.target,
            type: edge.type || 'smoothstep',
            animated: Boolean(edge.data?.animated),
            style: edge.data?.style || { stroke: isDark ? '#00aaff' : '#6366f1' },
            label: edge.data?.label,
            data: edge.data
          })
        })
      }

      // If no pre-processed nodes/edges, create a simple fallback
      if (initialNodes.length === 0 && complaintData.metadata) {
        initialNodes.push({
          id: 'metadata',
          type: 'metadata',
          position: { x: 250, y: 50 },
          data: {
            label: 'Complaint Metadata',
            ...complaintData.metadata
          }
        })
      }

      setNodes(initialNodes)
      setEdges(initialEdges)
    } catch (error) {
      showError(
        'Failed to process graph data: ' + (error instanceof Error ? error.message : String(error))
      )
    }
  }, [complaintData, isDark, setNodes, setEdges, showError])

  return (
    <div className="w-full h-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        fitView
        fitViewOptions={{
          padding: 0.2,
          minZoom: 0.1,
          maxZoom: 1.5
        }}
        nodesDraggable={true}
        nodesConnectable={true}
        elementsSelectable={true}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        deleteKeyCode="Delete"
        className={isDark ? 'dark' : 'light'}
        style={{
          background: isDark ? '#1a1a1a' : '#f8f9fa',
          width: '100%',
          height: '100%'
        }}
      >
        <Background color={isDark ? '#333' : '#ddd'} gap={16} size={1} />
        <Controls />
        <MiniMap
          nodeColor={isDark ? '#374151' : '#e5e7eb'}
          maskColor={isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)'}
        />

        {/* Export Panel */}
        <Panel position="top-right">
          <div className="flex gap-2">
            <button
              onClick={() => {
                // Simple image download using html-to-image
                import('html-to-image').then(({ toPng }) => {
                  const element = document.querySelector('.react-flow') as HTMLElement
                  if (element) {
                    toPng(element)
                      .then((dataUrl) => {
                        const link = document.createElement('a')
                        link.download = `graph-${complaintId}.png`
                        link.href = dataUrl
                        link.click()
                      })
                      .catch((error) => {
                        showError('Failed to export image: ' + error.message)
                      })
                  }
                })
              }}
              className={`px-3 py-2 rounded text-sm font-medium ${
                isDark
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              📷 Export Image
            </button>
          </div>
        </Panel>
      </ReactFlow>
    </div>
  )
}
