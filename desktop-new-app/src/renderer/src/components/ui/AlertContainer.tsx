import React from 'react'
import Tailwind<PERSON>lert from './alert'

interface AlertItem {
  id: number
  message: React.ReactNode
  severity: 'success' | 'info' | 'warning' | 'error'
  duration: number
  retryable?: boolean
}

interface AlertContainerProps {
  alerts: AlertItem[]
  onClose: (id: number) => void
}

const AlertContainer: React.FC<AlertContainerProps> = ({ alerts, onClose }) => {
  return (
    <div className="fixed top-0 right-0 z-50 p-4 space-y-4 pointer-events-none">
      {alerts.map((alert, index) => (
        <div key={alert.id} className="pointer-events-auto" style={{ zIndex: 9999 - index }}>
          <TailwindAlert
            open={true}
            message={alert.message}
            severity={alert.severity}
            duration={alert.duration}
            onClose={() => onClose(alert.id)}
          />
        </div>
      ))}
    </div>
  )
}

export default AlertContainer
