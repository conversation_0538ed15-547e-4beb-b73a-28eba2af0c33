import * as React from 'react'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'destructive' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-300 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50
          ${variant === 'outline' ? 'border border-white/50 bg-white/30 backdrop-blur-sm shadow-md text-blue-700 hover:bg-white/50' : ''}
          ${variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
          ${variant === 'secondary' ? 'bg-secondary text-secondary-foreground hover:bg-secondary/80' : ''}
          ${variant === 'ghost' ? 'hover:bg-accent hover:text-accent-foreground' : ''}
          ${variant === 'link' ? 'text-primary underline-offset-4 hover:underline' : ''}
          ${size === 'sm' ? 'h-9 px-3' : ''}
          ${size === 'lg' ? 'h-11 px-8' : ''}
          ${size === 'icon' ? 'h-10 w-10' : ''}
          ${!variant || variant === 'default' ? 'bg-gradient-to-br from-blue-400/60 to-purple-400/60 text-white border border-white/40 backdrop-blur-sm shadow-md hover:from-blue-500/80 hover:to-purple-500/80' : ''}
          ${className || ''}
        `}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button }
