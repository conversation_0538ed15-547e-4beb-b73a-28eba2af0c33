import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { ComplaintData } from '../../../shared/api'
import TransactionTable from '../components/TransactionTable'
import { Button } from '../components/ui/Button.tsx'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'

interface Complaint {
  id: string
  complaint_number: string
  complainant_name: string
  category_of_fraud: string
  date_of_complaint: string
  amount: number
  status: string
  metadata?: Record<string, unknown>
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const [complaints, setComplaints] = useState<Complaint[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchComplaints = async (): Promise<void> => {
      try {
        setLoading(true)
        const dbComplaints: ComplaintData[] = await window.api.database.getComplaints()
        const transformedComplaints = dbComplaints.map((c) => ({
          id: c.id,
          complaint_number: (c.metadata?.complaint_number as string) || '',
          complainant_name: (c.metadata?.complainant_name as string) || '',
          category_of_fraud: (c.metadata?.subcategory as string) || '',
          date_of_complaint: (c.metadata?.date as string) || c.created_at,
          amount: parseFloat(String(c.metadata?.total_amount || 0).replace(/,/g, '')),
          status: c.status || 'unknown',
          metadata: c.metadata || {}
        }))
        setComplaints(transformedComplaints as Complaint[])
      } catch (error) {
        console.error('Failed to fetch complaints:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchComplaints()
  }, [])

  const handleView = useCallback(
    (complaintId: string): void => {
      navigate(`/complaint-details/${complaintId}`)
    },
    [navigate]
  )

  const handleEdit = useCallback(
    async (editedComplaint: Complaint): Promise<void> => {
      try {
        const originalComplaint = complaints.find((c) => c.id === editedComplaint.id)
        if (!originalComplaint) {
          console.error('Original complaint not found for editing')
          return
        }

        const updatedMetadata = {
          ...originalComplaint.metadata,
          complaint_number: editedComplaint.complaint_number,
          complainant_name: editedComplaint.complainant_name,
          subcategory: editedComplaint.category_of_fraud
        }

        const dataToUpdate = {
          metadata: updatedMetadata,
          status: editedComplaint.status
        }

        await window.api.database.updateComplaint(editedComplaint.id, dataToUpdate)

        setComplaints((prev) =>
          prev.map((c) =>
            c.id === editedComplaint.id ? { ...editedComplaint, metadata: updatedMetadata } : c
          )
        )
      } catch (error) {
        console.error('Failed to save complaint:', error)
      }
    },
    [complaints]
  ) // Dependency on complaints to ensure latest state for find

  const handleDelete = useCallback(
    (complaintNumber: string): void => {
      // Implement delete logic
      console.log('Deleting:', complaintNumber)
      setComplaints(complaints.filter((c) => c.complaint_number !== complaintNumber))
    },
    [complaints]
  )

  const handleSummary = useCallback(
    (complaintNumber: string): void => {
      navigate(`/complaints/summary/${complaintNumber}`)
    },
    [navigate]
  )

  const totalAmount = complaints.reduce((acc, complaint) => acc + complaint.amount, 0)
  const totalComplaints = complaints.length

  const monthlyComplaints = complaints.reduce(
    (acc, complaint) => {
      let date
      if (complaint.date_of_complaint.includes('/')) {
        const [day, month, year] = complaint.date_of_complaint.split('/')
        date = new Date(`${year}-${month}-${day}`)
      } else {
        date = new Date(complaint.date_of_complaint)
      }

      if (!isNaN(date.getTime())) {
        const month = date.toLocaleString('default', { month: 'short' })
        if (!acc[month]) {
          acc[month] = { pending: 0, resolved: 0, 'in progress': 0 }
        }
        const status = complaint.status.toLowerCase()
        if (status === 'pending') {
          acc[month].pending++
        } else if (status === 'resolved') {
          acc[month].resolved++
        } else if (status === 'in progress') {
          acc[month]['in progress']++
        }
      }
      return acc
    },
    {} as Record<string, { pending: number; resolved: number; 'in progress': number }>
  )

  return (
    <div className="flex min-h-screen bg-background text-foreground">
      <main className="flex-1 p-6 sm:p-10 overflow-y-auto">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button onClick={() => navigate('/upload')} className="px-4 py-2 text-lg">
            Create Complaint
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-6">
          <Card className="p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-0">
              <CardTitle className="text-xs font-medium">Total Amount</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="text-xl font-bold">₹{totalAmount.toLocaleString('en-IN')}</div>
            </CardContent>
          </Card>
          <Card className="p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-0">
              <CardTitle className="text-xs font-medium">Total Complaints</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="text-xl font-bold">{totalComplaints}</div>
            </CardContent>
          </Card>
          <Card className="col-span-2 p-4">
            <CardHeader className="p-0">
              <CardTitle className="text-sm">Complaints per month</CardTitle>
            </CardHeader>
            <CardContent className="pl-2 p-0">
              <div className="w-full h-32 flex items-end gap-4">
                {Object.entries(monthlyComplaints).map(([month, counts]) => {
                  const total = counts.pending + counts.resolved + counts['in progress']
                  return (
                    <div key={month} className="flex-1 flex flex-col items-center gap-1">
                      <div className="w-full h-full flex items-end">
                        <div
                          className="w-1/3 bg-yellow-400 rounded-t-md"
                          style={{ height: `${(counts.pending / total) * 100}%` }}
                        ></div>
                        <div
                          className="w-1/3 bg-green-400 rounded-t-md"
                          style={{ height: `${(counts.resolved / total) * 100}%` }}
                        ></div>
                        <div
                          className="w-1/3 bg-blue-400 rounded-t-md"
                          style={{ height: `${(counts['in progress'] / total) * 100}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-muted-foreground">{month}</div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="mt-10">
          <TransactionTable
            complaints={complaints}
            loading={loading}
            onView={handleView}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSummary={handleSummary}
          />
        </div>
      </main>
    </div>
  )
}

export default Dashboard
