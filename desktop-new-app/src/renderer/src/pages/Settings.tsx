import React, { useState } from 'react'
import { useThemeContext } from '../context/useThemeContext'
import { Tabs } from '../components/ui/tabs'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'

const Settings: React.FC = () => {
  const [autoSave, setAutoSave] = useState(true)
  const [language, setLanguage] = useState('en')
  const [showPassword, setShowPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [twoFactor, setTwoFactor] = useState(false)
  const [sessionTimeout, setSessionTimeout] = useState('30')

  // Use theme context
  const { theme, setTheme } = useThemeContext()

  const tabs = [
    {
      title: 'Application',
      value: 'application',
      content: (
        <div className="w-full overflow-hidden relative h-full rounded-2xl p-10 text-xl md:text-4xl font-bold text-white bg-gradient-to-br from-purple-700 to-violet-900">
          <p>Application Settings</p>
          <div className="space-y-8 mt-10">
            <CardContainer>
              <CardBody>
                <CardItem translateZ="50">
                  <label className="text-lg font-semibold text-white">Theme</label>
                  <p className="text-gray-400 mb-4">Select your preferred color scheme.</p>
                  <select
                    value={theme}
                    onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'system')}
                    className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="system">System</option>
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                  </select>
                </CardItem>
              </CardBody>
            </CardContainer>
            <CardContainer>
              <CardBody>
                <CardItem translateZ="50">
                  <label className="text-lg font-semibold text-white">Language</label>
                  <p className="text-gray-400 mb-4">Choose the application language.</p>
                  <select
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="en">English</option>
                    <option value="hi">Hindi</option>
                    <option value="mr">Marathi</option>
                  </select>
                </CardItem>
              </CardBody>
            </CardContainer>
            <CardContainer>
              <CardBody>
                <CardItem translateZ="50" className="flex items-center justify-between">
                  <div>
                    <label className="text-lg font-semibold text-white">Auto Save</label>
                    <p className="text-gray-400">Automatically save changes as you make them.</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={autoSave}
                    onChange={(e) => setAutoSave(e.target.checked)}
                    className="w-6 h-6 rounded text-primary-500 bg-gray-700 border-gray-600 focus:ring-primary-500"
                  />
                </CardItem>
              </CardBody>
            </CardContainer>
            <CardItem translateZ="60">
              <button className="w-full py-3 text-lg font-semibold bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                Save Application Settings
              </button>
            </CardItem>
          </div>
        </div>
      )
    },
    {
      title: 'Security',
      value: 'security',
      content: (
        <div className="w-full overflow-hidden relative h-full rounded-2xl p-10 text-xl md:text-4xl font-bold text-white bg-gradient-to-br from-purple-700 to-violet-900">
          <p>Security Settings</p>
          <div className="space-y-8 mt-10">
            <CardContainer>
              <CardBody>
                <h3 className="text-lg font-semibold text-white mb-4">Change Password</h3>
                <div className="space-y-4">
                  <CardItem translateZ="50">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Current Password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </CardItem>
                  <CardItem translateZ="50">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      placeholder="New Password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </CardItem>
                  <CardItem translateZ="50">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Confirm New Password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </CardItem>
                  <CardItem translateZ="50" className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="show-password"
                      checked={showPassword}
                      onChange={(e) => setShowPassword(e.target.checked)}
                      className="w-4 h-4 rounded text-primary-500 bg-gray-700 border-gray-600 focus:ring-primary-500"
                    />
                    <label htmlFor="show-password" className="text-gray-400">
                      Show Passwords
                    </label>
                  </CardItem>
                </div>
              </CardBody>
            </CardContainer>
            <CardContainer>
              <CardBody>
                <CardItem translateZ="50" className="flex items-center justify-between">
                  <div>
                    <label className="text-lg font-semibold text-white">
                      Two-Factor Authentication
                    </label>
                    <p className="text-gray-400">Add an extra layer of security to your account.</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={twoFactor}
                    onChange={(e) => setTwoFactor(e.target.checked)}
                    className="w-6 h-6 rounded text-primary-500 bg-gray-700 border-gray-600 focus:ring-primary-500"
                  />
                </CardItem>
              </CardBody>
            </CardContainer>
            <CardContainer>
              <CardBody>
                <CardItem translateZ="50">
                  <label className="text-lg font-semibold text-white">Session Timeout</label>
                  <p className="text-gray-400 mb-4">
                    Set the duration of inactivity before you are automatically logged out.
                  </p>
                  <select
                    value={sessionTimeout}
                    onChange={(e) => setSessionTimeout(e.target.value)}
                    className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="15">15 minutes</option>
                    <option value="30">30 minutes</option>
                    <option value="60">60 minutes</option>
                    <option value="120">120 minutes</option>
                    <option value="480">480 minutes</option>
                  </select>
                </CardItem>
              </CardBody>
            </CardContainer>
            <CardItem translateZ="60">
              <button className="w-full py-3 text-lg font-semibold bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                Save Security Settings
              </button>
            </CardItem>
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="flex min-h-screen bg-neutral-900 text-white">
      <main className="flex-1 p-6 sm:p-10">
        <h1 className="text-4xl font-extrabold text-white drop-shadow-lg mb-2">Settings</h1>
        <p className="text-gray-300 text-lg mb-10">
          Manage your application preferences and configuration.
        </p>
        <div className="h-[20rem] md:h-[40rem] [perspective:1000px] relative b flex flex-col max-w-5xl mx-auto w-full  items-start justify-start my-40">
          <Tabs tabs={tabs} />
        </div>
      </main>
    </div>
  )
}

export default Settings
