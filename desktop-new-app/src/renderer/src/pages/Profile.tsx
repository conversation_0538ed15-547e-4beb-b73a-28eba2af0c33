import React, { useState, useContext, useEffect } from 'react'
import { AuthContext } from '../context/AuthContext'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'

const Profile: React.FC = () => {
  const authContext = useContext(AuthContext)
  const user = authContext?.user
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    initial: user?.initial || '',
    name: user?.name || '',
    email: user?.email || '',
    designation: user?.designation || '',
    police_station: user?.police_station || '',
    district: user?.district || '',
    state: user?.state || ''
  })

  useEffect(() => {
    if (user) {
      setFormData({
        initial: user.initial || '',
        name: user.name || '',
        email: user.email || '',
        designation: user.designation || '',
        police_station: user.police_station || '',
        district: user.district || '',
        state: user.state || ''
      })
    }
  }, [user])
  const [loading, setLoading] = useState(false)

  const subscriptionData = {
    plan: 'Professional',
    status: 'active',
    expiryDate: '2025-12-31',
    features: ['Unlimited Complaints', 'Advanced Analytics', 'Priority Support', 'Custom Templates']
  }

  const handleInputChange = (field: string, value: string): void => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSave = async (): Promise<void> => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      setIsEditing(false)
    }, 1000)
  }

  const handleCancel = (): void => {
    setFormData({
      initial: user?.initial || '',
      name: user?.name || '',
      email: user?.email || '',
      designation: user?.designation || '',
      police_station: user?.police_station || '',
      district: user?.district || '',
      state: user?.state || ''
    })
    setIsEditing(false)
  }

  const renderInfoField = (label: string, value: string): React.ReactNode => (
    <CardItem translateZ="50">
      <label className="text-sm font-semibold text-gray-400">{label}</label>
      <div className="mt-1 p-3 bg-neutral-800 rounded-lg text-white">
        {value || 'Not specified'}
      </div>
    </CardItem>
  )

  const renderInputField = (
    label: string,
    field: keyof typeof formData,
    value: string
  ): React.ReactNode => (
    <CardItem translateZ="50">
      <label htmlFor={field} className="text-sm font-semibold text-gray-400">
        {label}
      </label>
      <input
        id={field}
        value={value}
        onChange={(e) => handleInputChange(field, e.target.value)}
        className="w-full mt-1 p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
      />
    </CardItem>
  )

  return (
    <div className="flex min-h-screen bg-neutral-900 text-white">
      <main className="flex-1 p-6 sm:p-10">
        <h1 className="text-4xl font-extrabold text-white drop-shadow-lg mb-2">Profile</h1>
        <p className="text-gray-300 text-lg mb-10">
          Manage your personal information and subscription details.
        </p>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <CardContainer className="lg:col-span-2">
            <CardBody>
              <div className="p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Personal Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {isEditing ? (
                    <>
                      <CardItem translateZ="50">
                        <label htmlFor="initial" className="text-sm font-semibold text-gray-400">
                          Initial
                        </label>
                        <select
                          id="initial"
                          value={formData.initial}
                          onChange={(e) => handleInputChange('initial', e.target.value)}
                          className="w-full mt-1 p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                        >
                          <option value="">Select Initial</option>
                          <option value="Mr.">Mr.</option>
                          <option value="Miss">Miss</option>
                          <option value="Mrs.">Mrs.</option>
                        </select>
                      </CardItem>
                      {renderInputField('Name', 'name', formData.name)}
                    </>
                  ) : (
                    <>
                      {renderInfoField('Initial', formData.initial)}
                      {renderInfoField('Name', formData.name)}
                    </>
                  )}
                </div>
                <div className="mt-6">
                  <label className="text-sm font-semibold text-gray-400">Email Address</label>
                  <div className="mt-1 p-3 bg-neutral-800 rounded-lg text-gray-400 cursor-not-allowed">
                    {formData.email} (Cannot be changed)
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  {isEditing
                    ? renderInputField('Designation', 'designation', formData.designation)
                    : renderInfoField('Designation', formData.designation)}
                  {isEditing
                    ? renderInputField('Police Station', 'police_station', formData.police_station)
                    : renderInfoField('Police Station', formData.police_station)}
                </div>
                <div className="mt-6">
                  {isEditing
                    ? renderInputField('District', 'district', formData.district)
                    : renderInfoField('District', formData.district)}
                </div>
                <div className="mt-6">
                  {isEditing
                    ? renderInputField('State', 'state', formData.state)
                    : renderInfoField('State', formData.state)}
                </div>
                <div className="mt-8 flex gap-4">
                  {!isEditing ? (
                    <CardItem translateZ="60">
                      <button
                        onClick={() => setIsEditing(true)}
                        className="w-full py-3 text-lg font-semibold bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                      >
                        Edit Profile
                      </button>
                    </CardItem>
                  ) : (
                    <>
                      <CardItem translateZ="60">
                        <button
                          onClick={handleCancel}
                          className="w-full py-3 text-lg font-semibold bg-neutral-500 text-white rounded-lg hover:bg-neutral-600 transition-colors"
                        >
                          Cancel
                        </button>
                      </CardItem>
                      <CardItem translateZ="60">
                        <button
                          onClick={handleSave}
                          disabled={loading}
                          className="w-full py-3 text-lg font-semibold bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
                        >
                          {loading ? 'Saving...' : 'Save Changes'}
                        </button>
                      </CardItem>
                    </>
                  )}
                </div>
              </div>
            </CardBody>
          </CardContainer>
          <CardContainer>
            <CardBody>
              <div className="p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Subscription</h2>
                <div className="space-y-4 text-gray-300">
                  <CardItem translateZ="50">
                    <span className="font-semibold text-gray-400">Plan:</span>{' '}
                    {subscriptionData.plan}
                  </CardItem>
                  <CardItem translateZ="50">
                    <span className="font-semibold text-gray-400">Status:</span>{' '}
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        subscriptionData.status === 'active'
                          ? 'bg-green-500 text-white'
                          : 'bg-red-500 text-white'
                      }`}
                    >
                      {subscriptionData.status}
                    </span>
                  </CardItem>
                  <CardItem translateZ="50">
                    <span className="font-semibold text-gray-400">Expires:</span>{' '}
                    {new Date(subscriptionData.expiryDate).toLocaleDateString('en-IN')}
                  </CardItem>
                  <CardItem translateZ="50">
                    <span className="font-semibold text-gray-400">Features:</span>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {subscriptionData.features.map((f, i) => (
                        <li key={i}>{f}</li>
                      ))}
                    </ul>
                  </CardItem>
                </div>
                <CardItem translateZ="60">
                  <button className="w-full mt-8 py-3 text-lg font-semibold bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                    Manage Subscription
                  </button>
                </CardItem>
              </div>
            </CardBody>
          </CardContainer>
        </div>
        <BackgroundBeams />
      </main>
    </div>
  )
}

export default Profile
