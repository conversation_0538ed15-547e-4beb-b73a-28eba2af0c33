import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'
import { ArrowLeft, Download } from 'lucide-react'
import { backendService } from '../services/backendService'
import { ComplaintData } from '../../../shared/api'

const DEFAULT_NOTICE_POINTS = [
  'Account Holder Information',
  'Linked Accounts and Cards',
  'IP and Device Records',
  'Authentication and Access Logs',
  'Beneficiary and Transaction History',
  'Wallet/Bank account status and Balance',
  'E-commerce or Delivery Information',
  'ATM CCTV footage',
  'BSA Certification'
]

const NoticeGenerationPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [complaint, setComplaint] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedBanks, setSelectedBanks] = useState<string[]>([])
  const [noticePoints, setNoticePoints] = useState<string[]>([...DEFAULT_NOTICE_POINTS])

  useEffect(() => {
    const loadComplaint = async (): Promise<void> => {
      if (!id) return
      setLoading(true)
      try {
        const data = await backendService.getLocalComplaint(id)
        setComplaint(data)
        // Preselect all banks from bank_notice_data if available
        if (data?.bank_notice_data && typeof data.bank_notice_data === 'object') {
          setSelectedBanks(Object.keys(data.bank_notice_data.banks || {}))
        }
      } catch {
        setError('Failed to load complaint data')
      } finally {
        setLoading(false)
      }
    }
    loadComplaint()
  }, [id])

  const handleBankToggle = (bank: string): void => {
    setSelectedBanks((prev) =>
      prev.includes(bank) ? prev.filter((b) => b !== bank) : [...prev, bank]
    )
  }

  const handleNoticePointToggle = (point: string): void => {
    setNoticePoints((prev) =>
      prev.includes(point) ? prev.filter((p) => p !== point) : [...prev, point]
    )
  }

  // Simulate notice generation (download as .txt for demo)
  const handleGenerateNotices = (): void => {
    if (!complaint || !selectedBanks.length) return
    selectedBanks.forEach((bank) => {
      const content = `Notice for ${bank}\n\nPoints:\n${noticePoints.join('\n')}`
      const blob = new Blob([content], { type: 'text/plain' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `Notice_${complaint.id}_${bank}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    )
  }
  if (error || !complaint) {
    return (
      <div className="p-6 w-full">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
          {error || 'No complaint data available'}
        </div>
      </div>
    )
  }

  const banks =
    complaint.bank_notice_data && typeof complaint.bank_notice_data === 'object'
      ? Object.keys(complaint.bank_notice_data.banks || {})
      : []

  return (
    <div className="w-full h-full min-w-0 max-w-full overflow-x-hidden bg-neutral-900 text-white">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 w-full p-4">
        <div className="flex-shrink-0">
          <button
            onClick={() => navigate(`/complaint/${id}`)}
            className="flex items-center gap-2 px-4 py-2 rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" /> Back to Complaint
          </button>
          <h1 className="text-xl font-semibold text-white mt-2">Notice Generation</h1>
        </div>
        <button
          onClick={handleGenerateNotices}
          className="flex items-center gap-2 px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 transition-colors disabled:opacity-50"
          disabled={!selectedBanks.length}
        >
          <Download className="h-4 w-4" /> Generate Notices
        </button>
      </div>
      <div className="px-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        <CardContainer>
          <CardBody>
            <div className="p-8">
              <CardItem as="h2" translateZ="60" className="text-2xl font-bold text-white mb-6">
                Select Banks
              </CardItem>
              {banks.length === 0 ? (
                <p className="text-gray-400">No bank data available</p>
              ) : (
                <div className="space-y-2">
                  {banks.map((bank) => (
                    <CardItem key={bank} translateZ="50" className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedBanks.includes(bank)}
                        onChange={() => handleBankToggle(bank)}
                        className="w-4 h-4 rounded text-primary-500 bg-gray-700 border-gray-600 focus:ring-primary-500"
                      />
                      <span>{bank}</span>
                    </CardItem>
                  ))}
                </div>
              )}
            </div>
          </CardBody>
        </CardContainer>
        <CardContainer>
          <CardBody>
            <div className="p-8">
              <CardItem as="h2" translateZ="60" className="text-2xl font-bold text-white mb-6">
                Notice Points
              </CardItem>
              <div className="space-y-2">
                {DEFAULT_NOTICE_POINTS.map((point) => (
                  <CardItem key={point} translateZ="50" className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={noticePoints.includes(point)}
                      onChange={() => handleNoticePointToggle(point)}
                      className="w-4 h-4 rounded text-primary-500 bg-gray-700 border-gray-600 focus:ring-primary-500"
                    />
                    <span>{point}</span>
                  </CardItem>
                ))}
              </div>
            </div>
          </CardBody>
        </CardContainer>
      </div>
      <BackgroundBeams />
    </div>
  )
}

export default NoticeGenerationPage
