import React, { useCallback, useEffect, Suspense, lazy } from 'react'
import {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  Panel,
  ReactFlowProvider,
  useReactFlow,
  ReactFlow,
  BackgroundVariant
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { useParams, useNavigate } from 'react-router-dom'
import { FiArrowLeft, FiDownload, FiPlus } from 'react-icons/fi'
import { toPng } from 'html-to-image'
import { v4 as uuidv4 } from 'uuid'
import dagre from 'dagre'

import { useThemeContext } from '../context/useThemeContext'
import { TailwindAlertProvider } from '../context/TailwindAlertContext'
import { useAlert } from '../hooks/useAlert'
import { NodeData, EdgeData, GraphNode } from '../../../shared/api.d'
import { useGraphData } from '../hooks/useComplaintData'

// Define types for raw graph data
interface RawGraphNode {
  id: string
  type: string
  label?: string
  account?: string
  layer?: number
  amount?: string
  date?: string
  bank?: string
  fraud_type?: string
  [key: string]: unknown
}

interface RawGraphEdge {
  id: string
  source: string
  target: string
  type?: string
  [key: string]: unknown
}

interface RawGraphData {
  nodes?: RawGraphNode[]
  edges?: RawGraphEdge[]
}

// Lazy load custom nodes if they become complex
const TransactionNode = lazy(() => import('../components/graph/nodes/TransactionNode'))
const MetadataNode = lazy(() => import('../components/graph/nodes/MetadataNode'))

const nodeTypes = {
  transaction: TransactionNode,
  metadata: MetadataNode,
  sender_account: TransactionNode,
  receiver_account: TransactionNode,
  default: TransactionNode
}

const edgeTypes = {
  // Define custom edge types here if needed
}

const GraphVisualizationContent: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark: isDarkMode } = useThemeContext()
  const { showError, showSuccess } = useAlert()
  const { fitView } = useReactFlow()

  // Ensure id is not undefined before passing to useGraphData
  console.log('[GraphVisualization] Component rendered with id:', id)
  const { graphData: complaintGraphData, loading, error } = useGraphData(id || '')

  console.log('[GraphVisualization] Hook state:', {
    graphData: complaintGraphData,
    loading,
    error,
    hasNodes: complaintGraphData?.nodes?.length,
    hasEdges: complaintGraphData?.edges?.length
  })

  const [nodes, setNodes, onNodesChange] = useNodesState<Node<NodeData>>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge<EdgeData>>([])

  // Function to apply Dagre layout for proper hierarchical positioning
  const applyDagreLayout = useCallback(
    (nodesToLayout: GraphNode[], edgesToLayout: Edge<EdgeData>[]): Node<NodeData>[] => {
      const dagreGraph = new dagre.graphlib.Graph()
      dagreGraph.setDefaultEdgeLabel(() => ({}))

      // Configure the graph for top-bottom layout
      dagreGraph.setGraph({
        rankdir: 'TB',
        nodesep: 100,
        ranksep: 200,
        marginx: 50,
        marginy: 50,
        align: 'UL'
      })

      const nodeWidth = 350
      const nodeHeight = 180

      // Add nodes to dagre graph
      nodesToLayout.forEach((node) => {
        dagreGraph.setNode(node.id, {
          width: nodeWidth,
          height: nodeHeight
        })
      })

      // Add edges to dagre graph
      edgesToLayout.forEach((edge) => {
        dagreGraph.setEdge(edge.source, edge.target)
      })

      // Apply dagre layout
      dagre.layout(dagreGraph)

      // Update node positions based on dagre layout
      const layoutedNodes = nodesToLayout.map((node) => {
        const nodeWithPosition = dagreGraph.node(node.id)
        return {
          ...node,
          position: {
            x: nodeWithPosition.x - nodeWidth / 2,
            y: nodeWithPosition.y - nodeHeight / 2
          }
        } as Node<NodeData>
      })

      return layoutedNodes
    },
    []
  )

  // Process raw graph data into proper hierarchical structure
  const processGraphData = useCallback((rawGraphData: { nodes?: unknown[]; edges?: unknown[] }) => {
    console.log('[GraphVisualization] Processing raw graph data:', rawGraphData)

    if (!rawGraphData?.nodes || !rawGraphData?.edges) {
      console.log('[GraphVisualization] Invalid graph data structure')
      return { nodes: [], edges: [] }
    }

    // Convert raw nodes to ReactFlow format with proper type mapping
    const processedNodes = rawGraphData.nodes.map((nodeData) => {
      const node = nodeData as RawGraphNode
      // Determine the proper ReactFlow node type
      let reactFlowNodeType = 'transaction'
      if (node.type === 'metadata') {
        reactFlowNodeType = 'metadata'
      }

      // Create proper node data structure
      const processedNodeData = {
        ...node, // Include all original properties first
        label: node.label || node.id,
        account: node.account || node.id,
        nodeType: node.type, // Rename to avoid conflict
        layer: node.layer || 0,
        amount: node.amount,
        date: node.date,
        bank: node.bank,
        fraud_type: node.fraud_type,
        isSenderNode: node.type === 'sender_account',
        isMainTransaction: node.type === 'receiver_account'
      }

      return {
        id: node.id,
        type: reactFlowNodeType,
        data: processedNodeData,
        position: { x: 0, y: 0 } // Will be set by layout
      }
    })

    // Convert raw edges to ReactFlow format
    const processedEdges = rawGraphData.edges.map((edgeData) => {
      const edge = edgeData as RawGraphEdge
      return {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: 'default',
        data: edge
      }
    })

    console.log('[GraphVisualization] Processed nodes:', processedNodes.length)
    console.log('[GraphVisualization] Processed edges:', processedEdges.length)

    return { nodes: processedNodes, edges: processedEdges }
  }, [])

  useEffect(() => {
    if (complaintGraphData) {
      const { nodes: processedNodes, edges: processedEdges } = processGraphData(complaintGraphData)

      if (processedNodes.length > 0) {
        const layoutedNodes = applyDagreLayout(processedNodes, processedEdges)

        setNodes(layoutedNodes)
        setEdges(processedEdges)

        // Fit view after nodes are set and laid out
        requestAnimationFrame(() => {
          fitView()
        })
      }
    }
  }, [complaintGraphData, setNodes, setEdges, fitView, applyDagreLayout, processGraphData])

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge<EdgeData> = {
        id: uuidv4(),
        source: params.source,
        target: params.target,
        type: 'default',
        data: {}
      }
      setEdges((eds) => addEdge(newEdge, eds))
      showSuccess('Edge added successfully!')
    },
    [setEdges, showSuccess]
  )

  const onNodesDelete = useCallback(
    (deleted: Node<NodeData>[]) => {
      setNodes((nds) => nds.filter((node) => !deleted.some((d) => d.id === node.id)))
      setEdges((eds) =>
        eds.filter((edge) => !deleted.some((d) => d.id === edge.source || d.id === edge.target))
      )
      showSuccess(`Deleted ${deleted.length} nodes.`)
    },
    [setNodes, setEdges, showSuccess]
  )

  const onAddNode = useCallback(() => {
    const newNode: Node<NodeData> = {
      id: uuidv4(),
      position: { x: Math.random() * 500, y: Math.random() * 500 },
      data: { label: 'New Node', layer: 0 },
      type: 'default'
    }
    setNodes((nds) => nds.concat(newNode))
    showSuccess('Added new node.')
  }, [setNodes, showSuccess])

  const onDownloadImage = useCallback(() => {
    const flowContainer = document.querySelector('.reactflow-wrapper')
    if (flowContainer) {
      toPng(flowContainer as HTMLElement, {
        backgroundColor: isDarkMode ? '#1a202c' : '#ffffff',
        width: flowContainer.clientWidth,
        height: flowContainer.clientHeight
      })
        .then((dataUrl) => {
          const a = document.createElement('a')
          a.setAttribute('download', `graph-${id}.png`)
          a.setAttribute('href', dataUrl)
          a.click()
          showSuccess('Graph image downloaded successfully!')
        })
        .catch((err) => {
          showError('Failed to download image: ' + err.message)
        })
    } else {
      showError('Could not find graph container for download.')
    }
  }, [id, isDarkMode, showError, showSuccess])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 text-lg ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Loading graph data...
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className={`text-red-500 text-xl mb-4`}>Error loading graph data</div>
          <p className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            <FiArrowLeft /> Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div
        className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-4 py-3 flex items-center justify-between`}
      >
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/dashboard')}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}`}
          >
            <FiArrowLeft />
            Back to Dashboard
          </button>
          <h1 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Graph Analysis - Complaint {id}
          </h1>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onAddNode}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-green-700 hover:bg-green-600 text-white' : 'bg-green-500 hover:bg-green-600 text-white'}`}
          >
            <FiPlus />
            Add Node
          </button>
          <button
            onClick={onDownloadImage}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-blue-700 hover:bg-blue-600 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white'}`}
          >
            <FiDownload />
            Download Image
          </button>
        </div>
      </div>

      {/* React Flow Container */}
      <div className="flex-1 relative reactflow-wrapper">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodesDelete={onNodesDelete}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
        >
          <MiniMap />
          <Controls />
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          <Panel position="top-left" className="p-2">
            <button
              onClick={() => fitView()}
              className={`px-3 py-2 rounded ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}`}
            >
              Fit View
            </button>
          </Panel>
        </ReactFlow>
      </div>
    </div>
  )
}

const GraphVisualization: React.FC = () => (
  <TailwindAlertProvider>
    <ReactFlowProvider>
      <Suspense fallback={<div>Loading Graph...</div>}>
        <GraphVisualizationContent />
      </Suspense>
    </ReactFlowProvider>
  </TailwindAlertProvider>
)

export default GraphVisualization
