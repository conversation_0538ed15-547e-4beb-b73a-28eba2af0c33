import React, { useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Upload, Info } from 'lucide-react'
import FraudTypeSelector from '../components/FraudTypeSelector'
import { CardContainer, CardBody } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

const ComplaintUpload: React.FC = () => {
  const navigate = useNavigate()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [file, setFile] = useState<File | null>(null)
  const [fraudType, setFraudType] = useState<string>('')
  const [layerDepth, setLayerDepth] = useState<number>(7)
  const [skipThresholdAmount, setSkipThresholdAmount] = useState<number>(0) // New state for threshold
  const [error, setError] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setError('')
    if (!event.target.files || event.target.files.length === 0) return
    const selectedFile = event.target.files[0]
    if (selectedFile.size > MAX_FILE_SIZE) {
      setError('File size exceeds 10MB limit')
      return
    }
    if (!selectedFile.name.endsWith('.html') && !selectedFile.name.endsWith('.htm')) {
      setError('Only HTML files are allowed')
      return
    }
    setFile(selectedFile)
  }

  const handleBrowseClick = (): void => {
    if (fileInputRef.current) fileInputRef.current.click()
  }

  const handleRemoveFile = (): void => {
    setFile(null)
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  const handleUpload = async (): Promise<void> => {
    if (!file) {
      setError('Please select a file')
      return
    }
    if (!fraudType) {
      setError('Please select a fraud type')
      return
    }
    setIsUploading(true)
    setUploadProgress(0)
    setError('')

    try {
      const fileContent = await file.text()
      const result = await window.api.uploadHtml({
        fileContent,
        fileName: file.name,
        fraudType,
        layerDepth,
        skipThresholdAmount
      })

      if (result.success && result.complaintId) {
        setUploadProgress(100)
        navigate(`/complaint/${result.complaintId}`)
      } else {
        setError(result.error || 'An unknown error occurred')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred.')
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="flex min-h-screen bg-neutral-900 text-white">
      <main className="flex-1 p-6 sm:p-10">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-extrabold text-white drop-shadow-lg mb-2">
            Upload Complaint
          </h1>
          <p className="text-gray-300 text-lg mb-10">
            Upload your bank statement HTML file for automatic transaction extraction.
          </p>
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-8">
            <div className="lg:col-span-8 space-y-8">
              <CardContainer>
                <CardBody>
                  <div className="p-6">
                    <h3 className="text-sm font-medium mb-4 text-white">Fraud Type</h3>
                    <FraudTypeSelector
                      selectedType={fraudType}
                      onTypeSelect={(type) => {
                        setFraudType(type)
                      }}
                    />
                  </div>
                </CardBody>
              </CardContainer>
              <CardContainer>
                <CardBody>
                  <div className="p-8">
                    <h3 className="text-lg font-semibold mb-5 text-white">Money Flow Depth</h3>
                    <div className="flex items-center mb-4">
                      <input
                        type="range"
                        min={2}
                        max={7}
                        step={1}
                        value={layerDepth}
                        onChange={(e) => setLayerDepth(parseInt(e.target.value))}
                        className="w-full h-2 rounded-lg appearance-none cursor-pointer bg-gray-600 accent-primary-500"
                      />
                    </div>
                    <div className="flex justify-between mb-4">
                      {[2, 3, 4, 5, 6, 7].map((value) => (
                        <button
                          key={value}
                          onClick={() => setLayerDepth(value)}
                          className={`flex flex-col items-center justify-center w-10 h-10 rounded-full text-sm border transition-all duration-200 ${
                            layerDepth === value
                              ? 'bg-primary-500 text-white border-primary-500 shadow-md'
                              : 'bg-neutral-800 text-gray-300 border-neutral-600 hover:bg-neutral-700'
                          }`}
                        >
                          {value}
                        </button>
                      ))}
                    </div>
                    <div className="flex justify-between items-center p-3 rounded-lg text-sm bg-neutral-800 border border-neutral-600">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2 text-xs bg-primary-500 text-white">
                          {layerDepth}
                        </div>
                        <p className="text-sm text-gray-400">
                          {layerDepth <= 3
                            ? 'Faster processing'
                            : layerDepth >= 6
                              ? 'Deeper tracing'
                              : 'Balanced depth'}
                        </p>
                      </div>
                      <div className="px-3 py-1 rounded-md bg-primary-500 text-white">
                        <p className="text-xs font-medium">
                          {layerDepth <= 3 ? 'Fast ⚡' : layerDepth <= 5 ? 'Medium' : 'Deep'}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </CardContainer>
              <CardContainer>
                <CardBody>
                  <div className="p-8">
                    <h3 className="text-lg font-semibold mb-5 text-white">Skip Threshold Amount</h3>
                    <div className="flex items-center mb-4">
                      <input
                        type="number"
                        min={0}
                        value={skipThresholdAmount}
                        onChange={(e) => setSkipThresholdAmount(parseInt(e.target.value))}
                        className="w-full p-2 rounded-lg bg-neutral-800 text-white border border-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Enter threshold amount"
                      />
                    </div>
                    <p className="text-sm text-gray-400">
                      Transactions below this amount will be skipped during analysis.
                    </p>
                  </div>
                </CardBody>
              </CardContainer>
              <CardContainer>
                <CardBody>
                  <div className="p-8 space-y-8">
                    <div>
                      <label className="block text-lg font-semibold mb-4">HTML File</label>
                      <div
                        className="mt-2 flex flex-col items-center justify-center w-full h-64 border-2 border-dashed border-gray-500 rounded-lg cursor-pointer hover:border-primary-500 transition-colors p-6"
                        onClick={handleBrowseClick}
                      >
                        {!file ? (
                          <div className="text-center">
                            <Upload className="mx-auto h-16 w-16 text-gray-400 mb-3" />
                            <p className="text-lg text-gray-400">
                              Click to select an HTML file (max 10MB)
                            </p>
                            <p className="text-sm text-gray-500 mt-1">or drag and drop here</p>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center w-full px-6 py-4">
                            <Upload className="h-16 w-16 text-green-400 mb-3" />
                            <span className="text-xl font-semibold text-center break-all">
                              {file.name}
                            </span>
                            <span className="text-md text-gray-400 mt-1">
                              ({(file.size / 1024 / 1024).toFixed(2)} MB)
                            </span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleRemoveFile()
                              }}
                              className="mt-4 text-red-500 hover:text-red-400 font-semibold text-lg"
                            >
                              Remove File
                            </button>
                          </div>
                        )}
                        <input
                          type="file"
                          accept=".html,.htm"
                          ref={fileInputRef}
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>

                    {error && (
                      <div className="text-red-400 text-center font-semibold text-lg">{error}</div>
                    )}

                    <div className="flex justify-end gap-4 pt-6">
                      <button
                        onClick={() => navigate('/dashboard')}
                        className="px-6 py-3 rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors font-semibold"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleUpload}
                        disabled={isUploading || !file || !fraudType}
                        className="px-6 py-3 rounded-lg bg-primary-500 hover:bg-primary-600 transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isUploading ? `Processing... ${uploadProgress}%` : 'Process Complaint'}
                      </button>
                    </div>
                  </div>
                </CardBody>
              </CardContainer>
            </div>
            <div className="lg:col-span-4 order-last">
              <CardContainer>
                <CardBody>
                  <div className="p-4">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100/80 dark:bg-[rgba(0,170,255,0.15)] rounded-full text-blue-600 dark:text-blue-400 mr-3 dark:shadow-glow-blue-sm">
                        <Info className="w-4 h-4" />
                      </div>
                      <h2 className="text-lg font-bold text-white">How to Upload</h2>
                    </div>
                    <ol className="space-y-4 text-gray-300 mt-4">
                      <li className="flex items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                          1
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">Select Fraud Type</h4>
                          <p className="text-sm">
                            Choose the type of fraud (e.g., &quots;Online
                            Banking&quots;,&quots;Credit/Debit Card&quots;).
                          </p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                          2
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">Set Money Flow Depth</h4>
                          <p className="text-sm">
                            Adjust the layer depth for transaction tracing (2-7).
                          </p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                          3
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">Set Skip Threshold Amount</h4>
                          <p className="text-sm">Specify a threshold to skip small transactions.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                          4
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">Upload HTML File</h4>
                          <p className="text-sm">
                            Drag and drop or browse to select your bank statement HTML file.
                          </p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                          5
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">Process Complaint</h4>
                          <p className="text-sm">
                            Click the &quots;Process Complaint&quots; button to initiate extraction
                            and analysis.
                          </p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                          6
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">Review Results</h4>
                          <p className="text-sm">
                            You will be redirected to the complaint details page to review the
                            extracted data and graph visualization.
                          </p>
                        </div>
                      </li>
                    </ol>
                  </div>
                </CardBody>
              </CardContainer>
            </div>
          </div>
        </div>
        <BackgroundBeams />
      </main>
    </div>
  )
}

export default ComplaintUpload
