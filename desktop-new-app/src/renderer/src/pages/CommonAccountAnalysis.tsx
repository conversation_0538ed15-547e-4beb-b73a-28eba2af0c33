import React, { useEffect, useState } from 'react'
import { backendService } from '../services/backendService'
import { Card, CardHeader, CardContent, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { ComplaintData, TransactionData } from '../../../shared/api'

interface CommonAccount {
  account: string
  count: number
  complaints: string[]
}

const CommonAccountAnalysis: React.FC = () => {
  const [complaints, setComplaints] = useState<ComplaintData[]>([])
  const [selected, setSelected] = useState<string[]>([])
  const [commonAccounts, setCommonAccounts] = useState<CommonAccount[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    backendService.getLocalComplaints().then((data) => {
      setComplaints(data)
    })
  }, [])

  const handleAnalyze = (): void => {
    setLoading(true)
    // Find all accounts in selected complaints
    const selectedComplaints = complaints.filter((c) => selected.includes(c.id))
    const accountMap: Record<string, { count: number; complaints: string[] }> = {}
    selectedComplaints.forEach((c) => {
      ;(Array.isArray(c.transactions) ? c.transactions : []).forEach((txn: TransactionData) => {
        const acc = txn.sender_account || txn.receiver_account
        if (acc) {
          if (!accountMap[acc]) accountMap[acc] = { count: 0, complaints: [] }
          accountMap[acc].count++
          if (!accountMap[acc].complaints.includes(c.title))
            accountMap[acc].complaints.push(c.title)
        }
      })
    })
    // Only keep accounts present in more than one complaint
    const commons = Object.entries(accountMap)
      .filter(([, v]) => v.count > 1)
      .map(([account, v]) => ({ account, count: v.count, complaints: v.complaints }))
    setCommonAccounts(commons)
    setLoading(false)
  }

  return (
    <div className="w-full h-full min-w-0 max-w-full overflow-x-hidden p-4">
      <Card>
        <CardHeader>
          <CardTitle>Common Account Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="font-semibold mb-2">Select Complaints:</div>
            <div className="flex flex-wrap gap-2">
              {complaints.map((c) => (
                <label key={c.id} className="flex items-center gap-1">
                  <input
                    type="checkbox"
                    checked={selected.includes(c.id)}
                    onChange={() =>
                      setSelected((sel) =>
                        sel.includes(c.id) ? sel.filter((id) => id !== c.id) : [...sel, c.id]
                      )
                    }
                  />
                  <span className="text-xs">{c.title}</span>
                </label>
              ))}
            </div>
            <Button
              className="mt-4"
              onClick={handleAnalyze}
              disabled={selected.length < 2 || loading}
            >
              {loading ? 'Analyzing...' : 'Find Common Accounts'}
            </Button>
          </div>
          {commonAccounts.length > 0 ? (
            <div>
              <div className="font-semibold mb-2">Common Accounts Found:</div>
              <table className="min-w-full border text-xs">
                <thead>
                  <tr>
                    <th>Account</th>
                    <th>Appears In</th>
                    <th>Complaints</th>
                  </tr>
                </thead>
                <tbody>
                  {commonAccounts.map((acc) => (
                    <tr key={acc.account}>
                      <td>{acc.account}</td>
                      <td>{acc.count}</td>
                      <td>{acc.complaints.join(', ')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-gray-500 mt-4">
              No common accounts found. Select at least two complaints and analyze.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default CommonAccountAnalysis
