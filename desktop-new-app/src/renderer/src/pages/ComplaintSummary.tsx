import React, { useEffect, useState } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { <PERSON>Left, Printer } from 'lucide-react'
import { backendService } from '../services/backendService'
import { ComplaintData } from '../../../shared/api'

const ComplaintSummary: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [complaint, setComplaint] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadComplaint = async (): Promise<void> => {
      if (!id) return
      setLoading(true)
      try {
        const data = await backendService.getLocalComplaint(id)
        setComplaint(data)
      } catch {
        setError('Failed to load complaint data')
      } finally {
        setLoading(false)
      }
    }
    loadComplaint()
  }, [id])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    )
  }
  if (error || !complaint) {
    return (
      <div className="p-6 w-full">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
          {error || 'No complaint data available'}
        </div>
      </div>
    )
  }

  const metadata = complaint.metadata || {}
  const financial = metadata.total_amount || 0
  const lien = metadata.lien_amount || 0
  const date = metadata.date || ''
  const category = metadata.category || ''
  const subcategory = metadata.subcategory || ''
  const complainant = metadata.complainant_name || ''
  const suspectDetails = metadata.suspect_details || null
  const extractedAt = metadata.extracted_at || ''

  return (
    <div className="w-full h-full min-w-0 max-w-full overflow-x-hidden p-4 bg-neutral-900 text-white">
      <div className="flex flex-wrap justify-between items-center mb-4 gap-2">
        <button
          onClick={() => navigate('/dashboard')}
          className="flex items-center gap-2 px-4 py-2 rounded-lg bg-neutral-800 hover:bg-neutral-700 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" /> Back to Dashboard
        </button>
        <div className="flex items-center gap-2 flex-wrap">
          <button
            className="flex items-center gap-2 px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 transition-colors"
            onClick={() => window.print()}
          >
            <Printer className="h-4 w-4" /> Print Summary
          </button>
          <h1 className="text-xl font-bold truncate">Complaint Summary: {complaint.title}</h1>
        </div>
        <button
          onClick={() => navigate(`/complaint-details/${id}`)}
          className="flex items-center gap-2 px-4 py-2 rounded-lg bg-blue-500 hover:bg-blue-600 transition-colors"
        >
          View Details
        </button>
      </div>
      <CardContainer>
        <CardBody>
          <div className="p-8">
            <CardItem as="h2" translateZ="60" className="text-2xl font-bold text-white mb-6">
              Complaint Information
            </CardItem>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Complaint Number</p>
                <p className="text-sm">
                  {typeof metadata.complaint_number === 'string' ||
                  typeof metadata.complaint_number === 'number'
                    ? metadata.complaint_number
                    : metadata.complaint_number !== undefined
                      ? JSON.stringify(metadata.complaint_number)
                      : complaint.id}
                </p>
              </CardItem>
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Date</p>
                <p className="text-sm">
                  {typeof date === 'string' || typeof date === 'number'
                    ? date
                    : date !== undefined
                      ? JSON.stringify(date)
                      : ''}
                </p>
              </CardItem>
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Category</p>
                <p className="text-sm">
                  {typeof category === 'string' || typeof category === 'number'
                    ? category
                    : category !== undefined
                      ? JSON.stringify(category)
                      : ''}
                </p>
              </CardItem>
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Subcategory</p>
                <p className="text-sm">
                  {typeof subcategory === 'string' || typeof subcategory === 'number'
                    ? subcategory
                    : subcategory !== undefined
                      ? JSON.stringify(subcategory)
                      : ''}
                </p>
              </CardItem>
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Complainant</p>
                <p className="text-sm">
                  {typeof complainant === 'string' || typeof complainant === 'number'
                    ? complainant
                    : complainant !== undefined
                      ? JSON.stringify(complainant)
                      : ''}
                </p>
              </CardItem>
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Total Amount</p>
                <p className="text-sm font-semibold text-red-600">
                  ₹
                  {typeof financial === 'string' || typeof financial === 'number'
                    ? financial
                    : financial !== undefined
                      ? JSON.stringify(financial)
                      : ''}
                </p>
              </CardItem>
              <CardItem translateZ="50">
                <p className="text-sm font-medium text-gray-400">Lien/Hold Amount</p>
                <p className="text-sm font-semibold text-green-600">
                  ₹
                  {typeof lien === 'string' || typeof lien === 'number'
                    ? lien
                    : lien !== undefined
                      ? JSON.stringify(lien)
                      : ''}
                </p>
              </CardItem>
            </div>
            {/* Suspect Metadata Table */}
            {suspectDetails && (
              <>
                <CardItem
                  as="h2"
                  translateZ="60"
                  className="text-2xl font-bold text-white mb-6 mt-8"
                >
                  Suspect Information
                </CardItem>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(suspectDetails).map(([key, value]) => (
                    <CardItem key={key} translateZ="50">
                      <p className="text-sm font-medium text-gray-400">{key.replace(/_/g, ' ')}</p>
                      <p className="text-sm">
                        {typeof value === 'object' && value !== null
                          ? JSON.stringify(value)
                          : String(value)}
                      </p>
                    </CardItem>
                  ))}
                  {extractedAt && (
                    <CardItem translateZ="50">
                      <p className="text-sm font-medium text-gray-400">Extracted At</p>
                      <p className="text-sm">{String(extractedAt)}</p>
                    </CardItem>
                  )}
                </div>
              </>
            )}
          </div>
        </CardBody>
      </CardContainer>
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-neutral-900 to-black"></div>
    </div>
  )
}

export default ComplaintSummary
