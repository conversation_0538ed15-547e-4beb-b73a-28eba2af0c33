import React, { useState } from 'react'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'

// Dummy data for demonstration
const dummyNodalOfficers = [
  {
    name: '<PERSON><PERSON> <PERSON>',
    organization: 'SBI',
    type: 'Bank',
    designation: 'Manager',
    email: '<EMAIL>',
    phone: '**********',
    address: 'Delhi',
    state: 'Delhi'
  },
  {
    name: '<PERSON><PERSON> Verma',
    organization: 'ICICI',
    type: 'Bank',
    designation: 'Officer',
    email: '<EMAIL>',
    phone: '**********',
    address: 'Mumbai',
    state: 'Maharashtra'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    organization: 'Paytm',
    type: 'Wallet',
    designation: 'Nodal Officer',
    email: '<EMAIL>',
    phone: '**********',
    address: 'Noida',
    state: 'UP'
  }
]

const InformationSearchPage: React.FC = () => {
  const [search, setSearch] = useState('')
  const [results, setResults] = useState(dummyNodalOfficers)

  const handleSearch = (e: React.FormEvent): void => {
    e.preventDefault()
    setResults(
      dummyNodalOfficers.filter(
        (officer) =>
          officer.name.toLowerCase().includes(search.toLowerCase()) ||
          officer.organization.toLowerCase().includes(search.toLowerCase()) ||
          officer.state.toLowerCase().includes(search.toLowerCase())
      )
    )
  }

  return (
    <div className="w-full h-full min-w-0 max-w-full overflow-x-hidden p-4 bg-neutral-900 text-white">
      <CardContainer>
        <CardBody>
          <div className="p-8">
            <CardItem as="h2" translateZ="60" className="text-2xl font-bold text-white mb-6">
              Nodal Officer Search
            </CardItem>
            <form onSubmit={handleSearch} className="flex gap-2 mb-4">
              <CardItem translateZ="50" className="flex-1">
                <input
                  placeholder="Search by name, organization, or state..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full p-3 bg-transparent border-b-2 border-neutral-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </CardItem>
              <CardItem translateZ="60">
                <button
                  type="submit"
                  className="px-6 py-3 rounded-lg bg-primary-500 hover:bg-primary-600 transition-colors font-semibold"
                >
                  Search
                </button>
              </CardItem>
            </form>
            <div className="overflow-x-auto">
              <table className="min-w-full border text-sm text-white">
                <thead>
                  <tr className="bg-neutral-800">
                    <th className="p-2">Name</th>
                    <th className="p-2">Organization</th>
                    <th className="p-2">Type</th>
                    <th className="p-2">Designation</th>
                    <th className="p-2">Email</th>
                    <th className="p-2">Phone</th>
                    <th className="p-2">Address</th>
                    <th className="p-2">State</th>
                  </tr>
                </thead>
                <tbody>
                  {results.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="text-center py-4">
                        No results found
                      </td>
                    </tr>
                  ) : (
                    results.map((officer, idx) => (
                      <tr key={idx} className="border-b border-neutral-800">
                        <td className="p-2">{officer.name}</td>
                        <td className="p-2">{officer.organization}</td>
                        <td className="p-2">{officer.type}</td>
                        <td className="p-2">{officer.designation}</td>
                        <td className="p-2">{officer.email}</td>
                        <td className="p-2">{officer.phone}</td>
                        <td className="p-2">{officer.address}</td>
                        <td className="p-2">{officer.state}</td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </CardBody>
      </CardContainer>
      <BackgroundBeams />
    </div>
  )
}

export default InformationSearchPage
