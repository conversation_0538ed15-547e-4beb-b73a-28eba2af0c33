import { app, shell, BrowserWindow, ipcMain, safeStorage } from 'electron'
import { join } from 'path'
import fs from 'fs'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import {
  AUTH_LOGIN,
  AUTH_SIGNUP,
  AUTH_LOGOUT,
  AUTH_GET_SESSION,
  AUTH_HAS_TOKEN,
  AUTH_VERIFY_EMAIL,
  AUTH_RESEND_OTP,
  AUTH_SET_TOKEN,
  AUTH_GET_TOKEN,
  AUTH_CLEAR_TOKEN,
  DB_STORE_COMPLAINT,
  DB_GET_COMPLAINTS,
  DB_GET_COMPLAINT,
  DB_DELETE_COMPLAINT,
  DB_UPDATE_COMPLAINT,
  API_UPLOAD_HTML,
  DB_GET_GRAPH_DATA,
  DB_UPDATE_GRAPH_NODE,
  DB_ADD_GRAPH_NODE,
  DB_DELETE_GRAPH_NODE,
  DB_ADD_GRAPH_EDGE,
  DB_DELETE_GRAPH_EDGE
} from '../shared/ipc-channels'
import { User, GraphNode, NodeData, GraphEdge } from '../shared/api'
import {
  initializeDatabase,
  closeDatabase,
  storeComplaint,
  getComplaints,
  getComplaint,
  deleteComplaint,
  updateComplaint
} from './db'
const BACKEND_BASE_URL = 'http://127.0.0.1:8000/api/v1'

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      contextIsolation: true,
      nodeIntegration: false,
      sandbox: true
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.

// Secure Token Storage Path
const tokenFilePath = join(app.getPath('userData'), 'session.token')

app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Initialize local database
  try {
    initializeDatabase()
  } catch (error) {
    console.error('[Main] Failed to initialize database:', error)
  }

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC Handlers for secure token storage
  ipcMain.handle(AUTH_SET_TOKEN, async (_, token: string) => {
    try {
      if (!safeStorage.isEncryptionAvailable()) throw new Error('Encryption not available')
      const encrypted = safeStorage.encryptString(token)
      fs.writeFileSync(tokenFilePath, encrypted)
      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to set token:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Set token failed' }
    }
  })

  ipcMain.handle(AUTH_GET_TOKEN, async () => {
    try {
      if (!fs.existsSync(tokenFilePath)) return { token: null }
      const encrypted = fs.readFileSync(tokenFilePath)
      if (!safeStorage.isEncryptionAvailable()) throw new Error('Encryption not available')
      const token = safeStorage.decryptString(encrypted)
      return { token }
    } catch (error) {
      console.error('[Main] Failed to get token:', error)
      return { token: null, error: error instanceof Error ? error.message : 'Get token failed' }
    }
  })

  ipcMain.handle(AUTH_CLEAR_TOKEN, async () => {
    try {
      if (fs.existsSync(tokenFilePath)) fs.unlinkSync(tokenFilePath)
      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to clear token:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear token failed'
      }
    }
  })

  // IPC Handlers for backend communication
  ipcMain.handle(AUTH_LOGIN, async (_, credentials) => {
    try {
      const url = `${BACKEND_BASE_URL}/auth/login`
      const body = new URLSearchParams({
        username: credentials.email,
        password: credentials.password
      }).toString()
      const headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
      console.log('[Electron Main] LOGIN: URL:', url)
      console.log('[Electron Main] LOGIN: Body:', body)
      console.log('[Electron Main] LOGIN: Headers:', headers)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body
      })

      console.log('[Electron Main] LOGIN: Response status:', response.status)
      const data = await response.text()
      console.log('[Electron Main] LOGIN: Response body:', data)

      let parsed
      try {
        parsed = JSON.parse(data)
      } catch {
        parsed = { raw: data }
      }
      // Adapt response to what frontend expects
      if (response.ok && parsed.access_token) {
        // No session persistence, just return success
        return {
          success: true,
          token: parsed.access_token,
          refresh_token: parsed.refresh_token,
          user: parsed.user
        }
      }
      return {
        success: false,
        error: parsed.error || parsed.message || 'Invalid credentials'
      }
    } catch (error) {
      console.error('[Electron Main] LOGIN: Error:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Login failed' }
    }
  })

  ipcMain.handle(AUTH_SIGNUP, async (_, credentials) => {
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      })
      const data = await response.json()
      // The backend now returns `requires_verification` and `email`
      // We don't set token here as user needs to verify email first
      return data
    } catch (error: unknown) {
      console.error('Signup failed in main process:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Signup failed' }
    }
  })

  ipcMain.handle(AUTH_VERIFY_EMAIL, async (_, { email, otp }) => {
    console.log('[Electron Main] OTP VERIFY: Handler called')
    try {
      const url = `${BACKEND_BASE_URL}/auth/verify-email`
      const payload = {
        email: typeof email === 'string' ? email : String(email),
        otp: typeof otp === 'string' ? otp : String(otp)
      }
      const headers = { 'Content-Type': 'application/json' }
      console.log('[Electron Main] OTP VERIFY: URL:', url)
      console.log('[Electron Main] OTP VERIFY: Payload:', JSON.stringify(payload))
      console.log('[Electron Main] OTP VERIFY: Headers:', headers)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })

      console.log('[Electron Main] OTP VERIFY: Response status:', response.status)
      const data = await response.text()
      console.log('[Electron Main] OTP VERIFY: Response body:', data)

      let parsed
      try {
        parsed = JSON.parse(data)
      } catch {
        parsed = { raw: data }
      }
      return parsed
    } catch (error) {
      console.error('[Electron Main] OTP VERIFY: Error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Email verification failed'
      }
    }
  })

  ipcMain.handle(AUTH_RESEND_OTP, async (_, { email }) => {
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/auth/resend-otp`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })
      const data = await response.json()
      return data
    } catch (error: unknown) {
      console.error('Resend OTP failed in main process:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to resend OTP'
      }
    }
  })

  // Removed TOKEN_SET, TOKEN_GET, TOKEN_CLEAR handlers and logout persistence
  ipcMain.handle(AUTH_LOGOUT, async () => {
    // No session persistence, just return success
    return { success: true }
  })

  ipcMain.handle(AUTH_GET_SESSION, async (): Promise<User | null> => {
    try {
      if (!fs.existsSync(tokenFilePath)) return null
      if (!safeStorage.isEncryptionAvailable()) throw new Error('Encryption not available')
      const encrypted = fs.readFileSync(tokenFilePath)
      const token = safeStorage.decryptString(encrypted)
      if (!token) return null
      const response = await fetch(`${BACKEND_BASE_URL}/auth/verify`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      if (!response.ok) {
        console.error(
          '[Electron Main] GET_SESSION: Backend /auth/verify returned non-ok status:',
          response.status
        )
        return null
      }
      const data = await response.json()
      if (data.valid && data.user) {
        return data.user
      }
      console.error('[Electron Main] GET_SESSION: Invalid response from /auth/verify:', data)
      return null
    } catch (error) {
      console.error('[Electron Main] GET_SESSION: Error:', error)
      return null
    }
  })

  ipcMain.handle(AUTH_HAS_TOKEN, async () => {
    try {
      return fs.existsSync(tokenFilePath)
    } catch (error) {
      console.error('[Main] HAS_TOKEN Error:', error)
      return false
    }
  })

  // Database IPC Handlers
  ipcMain.handle(DB_STORE_COMPLAINT, async (_, complaint) => {
    try {
      return storeComplaint(complaint)
    } catch (error) {
      console.error('[Main] Failed to store complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_GET_COMPLAINTS, async () => {
    try {
      return getComplaints()
    } catch (error) {
      console.error('[Main] Failed to get complaints:', error)
      throw error
    }
  })

  ipcMain.handle(DB_GET_COMPLAINT, async (_, id) => {
    try {
      return getComplaint(id)
    } catch (error) {
      console.error('[Main] Failed to get complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_DELETE_COMPLAINT, async (_, id) => {
    try {
      return deleteComplaint(id)
    } catch (error) {
      console.error('[Main] Failed to delete complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_UPDATE_COMPLAINT, async (_, id, data) => {
    try {
      return updateComplaint(id, data)
    } catch (error) {
      console.error('[Main] Failed to update complaint:', error)
      throw error
    }
  })

  ipcMain.handle(
    API_UPLOAD_HTML,
    async (_, { fileContent, fileName, fraudType, layerDepth, skipThresholdAmount }) => {
      console.log('[Main] uploadHtml args:', {
        fileName,
        fraudType,
        layerDepth,
        skipThresholdAmount,
        fileContentLength: fileContent.length
      })
      try {
        // Inspect FormData entries
        let token: string | null = null
        if (fs.existsSync(tokenFilePath)) {
          const encrypted = fs.readFileSync(tokenFilePath)
          if (safeStorage.isEncryptionAvailable()) {
            token = safeStorage.decryptString(encrypted)
          }
        }

        if (!token) {
          return { success: false, error: 'Unauthorized' }
        }

        const formData = new FormData()
        formData.append('file', new Blob([fileContent], { type: 'text/html' }), fileName)
        formData.append('fraud_type', fraudType)
        formData.append('max_layer', String(layerDepth))
        formData.append('skip_threshold', String(skipThresholdAmount))

        const response = await fetch(`${BACKEND_BASE_URL}/extraction/parse-html`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`
          },
          body: formData
        })

        const data = await response.json()
        if (response.ok) {
          // Remove any existing complaint with the same complaint_number
          const newComplaintNumber = data.data.metadata.complaint_number
          if (newComplaintNumber !== undefined && newComplaintNumber !== null) {
            const existing = getComplaints()
            existing.forEach((c) => {
              if (c.metadata?.complaint_number === newComplaintNumber) {
                deleteComplaint(c.id)
              }
            })
          }
          const complaintId = storeComplaint({
            title: `Complaint from ${fileName}`,
            description: `Fraud Type: ${fraudType}`,
            file_name: fileName,
            fraud_type: fraudType,
            extraction_type: data.data.extraction_info.service,
            max_layer: layerDepth,
            metadata: data.data.metadata,
            transactions: null, // As per previous instruction
            layer_transactions: data.data.layer_transactions,
            bank_notice_data: data.data.bank_notice_data,
            graph_data: data.data.graph_data,
            extraction_info: data.data.extraction_info,
            status: 'processed'
          })
          return { success: true, complaintId: complaintId, message: data.message }
        } else {
          return { success: false, error: data.detail || 'Upload failed' }
        }
      } catch (error) {
        console.error('[Main] Failed to upload HTML:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'HTML upload failed'
        }
      }
    }
  )

  ipcMain.handle(DB_GET_GRAPH_DATA, async (_, complaintId: string) => {
    try {
      const complaint = getComplaint(complaintId)
      return complaint?.graph_data
    } catch (error) {
      console.error('[Main] Failed to get graph data:', error)
      throw error
    }
  })

  ipcMain.handle(
    DB_UPDATE_GRAPH_NODE,
    async (_, complaintId: string, nodeId: string, data: NodeData) => {
      try {
        const complaint = getComplaint(complaintId)
        if (complaint && complaint.graph_data) {
          const nodeIndex = complaint.graph_data.nodes.findIndex((n) => n.id === nodeId)
          if (nodeIndex > -1) {
            complaint.graph_data.nodes[nodeIndex].data = data
            updateComplaint(complaintId, { graph_data: complaint.graph_data })
          }
        }
      } catch (error) {
        console.error('[Main] Failed to update graph node:', error)
        throw error
      }
    }
  )

  ipcMain.handle(DB_ADD_GRAPH_NODE, async (_, complaintId: string, node: GraphNode) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.nodes.push(node)
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to add graph node:', error)
      throw error
    }
  })

  ipcMain.handle(DB_DELETE_GRAPH_NODE, async (_, complaintId: string, nodeId: string) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.nodes = complaint.graph_data.nodes.filter((n) => n.id !== nodeId)
        complaint.graph_data.edges = complaint.graph_data.edges.filter(
          (e) => e.source !== nodeId && e.target !== nodeId
        )
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to delete graph node:', error)
      throw error
    }
  })

  ipcMain.handle(DB_ADD_GRAPH_EDGE, async (_, complaintId: string, edge: GraphEdge) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.edges.push(edge)
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to add graph edge:', error)
      throw error
    }
  })

  ipcMain.handle(DB_DELETE_GRAPH_EDGE, async (_, complaintId: string, edgeId: string) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.edges = complaint.graph_data.edges.filter((e) => e.id !== edgeId)
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to delete graph edge:', error)
      throw error
    }
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Clean up database connection on app quit
app.on('before-quit', () => {
  closeDatabase()
  // Clear the authentication token file on app quit
  try {
    if (fs.existsSync(tokenFilePath)) {
      fs.unlinkSync(tokenFilePath)
      console.log('[Main] Authentication token cleared on app quit.')
    }
  } catch (error) {
    console.error('[Main] Failed to clear token on app quit:', error)
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
