{"$schema": "https://json.schemastore.org/tsconfig.json", "extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/**/*.{ts,tsx}", "src/preload/index.d.ts", "src/shared/api.d.ts", "src/renderer/src/global.d.ts"], "compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "moduleResolution": "bundler", "paths": {"@/*": ["./src/renderer/src/*"], "@shared/*": ["../shared/*"]}}}