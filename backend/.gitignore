# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.env
.env.*
!.env.example
.venv
venv/
ENV/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm
.yarn-integrity
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Frontend build
frontend/
frontend/dist/
frontend/.vite/
frontend/coverage/
frontend/.env
frontend/.env.*
!frontend/.env.example

# Backend
backend website/
backend/logs/
backend/.env
backend/.env.*
!backend/.env.example
backend/uploads/
backend/temp/
backend/.coverage
backend/htmlcov/
.venv
.venv/
backend/.venv
backend/venv

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.AppleDouble
.LSOverride
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Temporary files
*.tmp
*.bak
*.log
logs/
tmp/
temp/

# Database
*.sqlite3
*.db

# Secrets
*.pem
*.key
*.crt
*.cer
*.p12
*.pfx
*.jks
*.keystore

# Deployment
.vercel
.netlify
.serverless/
.fusebox/
.dynamodb/
.terraform/
*.tfstate
*.tfstate.*
.env.production
.env.staging

# Misc
.DS_Store
.qodo/
sample_data/
