"""
Simplified Authentication Routes - Removed unnecessary complexity
"""
import logging
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.core.database import get_database
from app.core.security import verify_token, create_tokens, generate_csrf_token
from app.models.user import UserCreate, UserLogin
from app.models.token import Token, RefreshTokenRequest
from app.models.auth import EmailRequest, PasswordResetRequest, OTPVerificationRequest
from app.services.auth_service import AuthService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])


@router.post("/register", response_model=dict, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Register a new user account - Simplified
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.create_user(user_data, send_email=False)
        if result.get("existing"):
            if not result.get("email_verified"):
                # User exists but not verified, resend OTP in background
                background_tasks.add_task(auth_service._send_verification_email, result["email"], user_data.name or "")
                return {
                    "message": "User already exists but is not verified. Verification code resent.",
                    "email": result["email"],
                    "requires_verification": True
                }
            else:
                # User exists and is verified
                return {
                    "message": "User with this email already exists and is verified. Please login.",
                    "email": result["email"],
                    "requires_verification": False,
                    "redirect_to_login": True
                }
        user = result["user"]
        background_tasks.add_task(auth_service._send_verification_email, user.email, user.name or "")
        logger.info(f"User registered successfully: {user.email}")
        return {
            "message": "User registered successfully. Please check your email for verification code.",
            "user_id": str(user.id),
            "email": user.email,
            "requires_verification": True
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed. Please try again."
        )


@router.post("/verify-email")
async def verify_email(
    otp_request: OTPVerificationRequest,
    request: Request,
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Verify email address using OTP - Simplified
    """
    try:
        auth_service = AuthService(db)

        # Verify email with OTP
        success = await auth_service.verify_email(
            email=otp_request.email,
            otp=otp_request.otp
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )

        logger.info(f"Email verified successfully for user: {otp_request.email}")

        return {
            "success": True,
            "message": "Email verified successfully",
            "user": {
                "email": otp_request.email,
                "email_verified": True
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed. Please try again."
        )


@router.post("/resend-otp")
async def resend_verification_otp(
    email_request: EmailRequest,
    request: Request,
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Resend email verification OTP - Simplified
    """
    try:
        auth_service = AuthService(db)
        
        # Resend OTP
        success = await auth_service.resend_verification_otp(email_request.email)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to resend OTP. Please check your email address."
            )
        
        logger.info(f"OTP resent successfully to: {email_request.email}")
        
        return {
            "message": "Verification code sent successfully. Please check your email.",
            "email": email_request.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resend OTP error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resend verification code. Please try again."
        )


@router.post("/login", response_model=Token, response_model_exclude_unset=True)
async def login_for_access_token(
    request: Request,
    response: Response,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    OAuth2 compatible token login - Simplified
    """
    try:
        auth_service = AuthService(db)
        
        # Create login data from form
        login_data = UserLogin(
            email=form_data.username,  # OAuth2 uses 'username' field for email
            password=form_data.password
        )
        
        # Get client IP for security logging
        client_ip = request.client.host if request and request.client else "unknown"

        # Simplified authentication (removed device fingerprinting)
        auth_result = await auth_service.authenticate_user(
            login_data,
            client_ip
        )

        access_token = auth_result.get("access_token")
        refresh_token = auth_result.get("refresh_token")
        token_type = auth_result.get("token_type", "bearer")
        expires_in = auth_result.get("expires_in", settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60)
        user = auth_result.get("user")

        # Set secure HTTP-only cookie for refresh token
        if settings.SECURE_COOKIES and refresh_token:
            response.set_cookie(
                key="refresh_token",
                value=refresh_token,
                httponly=True,
                secure=True,
                samesite="strict",
                max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
            )
        
        logger.info(f"User logged in successfully: {user['email'] if user else 'unknown'}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": token_type,
            "expires_in": expires_in,
            "user": user
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed. Please try again."
        )


@router.post("/refresh", response_model=Token)
async def refresh_access_token(
    refresh_request: RefreshTokenRequest,
    request: Request,
    response: Response,
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Refresh access token using refresh token - Simplified
    """
    try:
        # Verify refresh token
        payload = verify_token(refresh_request.refresh_token, token_type="refresh")
        email = payload.get("sub")
        user_id = payload.get("user_id")
        
        if not email or not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Get user from database
        auth_service = AuthService(db)
        user = await auth_service.get_user_by_email(email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        access_token, refresh_token = create_tokens(
            user.email,
            user.roles if hasattr(user, 'roles') else ["user"]
        )
        
        # Update refresh token cookie
        if settings.SECURE_COOKIES:
            response.set_cookie(
                key="refresh_token",
                value=refresh_token,
                httponly=True,
                secure=True,
                samesite="strict",
                max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
            )
        
        logger.info(f"Token refreshed successfully for user: {email}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": {
                "id": str(user.id),
                "email": user.email,
                "initial": user.initial or "",
                "name": user.name or "",
                "designation": user.designation or "User",
                "email_verified": user.email_verified,
                "is_active": user.is_active
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )


@router.post("/logout")
async def logout_user(
    request: Request,
    response: Response,
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    User logout - Simplified
    """
    try:
        # Clear refresh token cookie
        response.delete_cookie(key="refresh_token")
        
        logger.info("User logged out successfully")
        
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/verify")
async def verify_token_endpoint(
    request: Request,
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Verify token validity - Simplified
    """
    try:
        # Get token from Authorization header
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing or invalid authorization header"
            )
        
        token = authorization.split(" ")[1]
        
        # Verify token
        payload = verify_token(token, token_type="access")
        email = payload.get("sub")
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Get user from database
        auth_service = AuthService(db)
        user = await auth_service.get_user_by_email(email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        return {
            "valid": True,
            "user": {
                "id": str(user.id),
                "email": user.email,
                "initial": user.initial or "",
                "name": user.name or "",
                "designation": user.designation or "User",
                "email_verified": user.email_verified,
                "is_active": user.is_active
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )


@router.get("/csrf", tags=["csrf"])
async def get_csrf_token(response: Response):
    """
    Return a CSRF token for Electron clients. No cookies, just a header and JSON.
    """
    token = generate_csrf_token()
    response.headers["X-CSRF-Token"] = token
    return {"csrf_token": token, "algorithm": "random-url-safe", "note": "Send this token in X-CSRF-Token header for state-changing requests."}
