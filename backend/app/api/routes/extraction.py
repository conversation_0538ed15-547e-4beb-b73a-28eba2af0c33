"""
HTML Extraction routes for Desktop NCRP Matrix Backend
Handles HTML file upload and data extraction
"""
import logging
import tempfile
import os
from datetime import datetime
from typing import Any, Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, status, Depends, Request
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.core.database import get_database
from app.models.user import UserInDB
from app.services.extraction_service import HTMLExtractionService
from app.core.security import get_current_user # Import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/extraction", tags=["extraction"])


@router.post("/parse-html")
async def extract_html_data(
    request: Request,
    file: UploadFile = File(...),
    max_layer: int = Form(7),
    fraud_type: Optional[str] = Form(None),
    extraction_type: Optional[str] = Form(None),
    db: AsyncIOMotorDatabase = Depends(get_database),
    current_user: UserInDB = Depends(get_current_user) # Add authentication dependency
) -> Any:
    """
    Extract data from uploaded HTML complaint file
    
    Args:
        file: HTML file containing complaint data
        max_layer: Maximum layer to extract and visualize (0-7, default: 7)
        fraud_type: Type of fraud (online_banking, card, etc.)
        extraction_type: Specialized extraction logic (banking_upi, card, etc.)
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing extracted data including metadata, transactions,
        layer transactions, bank notice data, and graph data
    """
    temp_file_path = None
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )
        
        # Check file extension
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in settings.ALLOWED_FILE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed types: {settings.ALLOWED_FILE_TYPES}"
            )
        
        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Reset file pointer
        await file.seek(0)
        
        # Validate parameters
        if max_layer < 0 or max_layer > 7:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="max_layer must be between 0 and 7"
            )
        
        # Sanitize optional parameters
        fraud_type = fraud_type.strip() if fraud_type else None
        extraction_type = extraction_type.strip() if extraction_type else None
        
        logger.info(
            f"HTML extraction request: "
            f"file={file.filename}, max_layer={max_layer}, "
            f"fraud_type={fraud_type}, extraction_type={extraction_type}"
        )
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.html') as temp_file:
            temp_file_path = temp_file.name
            content = await file.read()
            temp_file.write(content)
        
        # Read file content
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Initialize extraction service
        extraction_service = HTMLExtractionService()
        
        # Extract data
        extraction_result = await extraction_service.extract_html_data(
            html_content=html_content,
            max_layer=max_layer,
            fraud_type=fraud_type,
            extraction_type=extraction_type
        )
        
        # Add extraction metadata
        extraction_result["extraction_info"] = {
            "extracted_at": datetime.now().isoformat(),
            "service": "Desktop NCRP Matrix"
        }
        
        logger.info(
            f"HTML extraction completed successfully. "
            f"Transactions extracted: {len(extraction_result.get('transactions', []))}"
        )
        
        return {
            "success": True,
            "data": extraction_result,
            "message": "HTML extraction completed successfully"
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"HTML extraction failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"HTML extraction failed: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {temp_file_path}: {e}")


@router.get("/health")
async def extraction_health_check() -> Any:
    """
    Health check endpoint for extraction service
    """
    try:
        # Basic health check
        extraction_service = HTMLExtractionService()
        
        return {
            "status": "healthy",
            "service": "HTML Extraction Service",
            "version": settings.VERSION,
            "max_file_size": settings.MAX_FILE_SIZE,
            "allowed_file_types": settings.ALLOWED_FILE_TYPES
        }
        
    except Exception as e:
        logger.error(f"Extraction service health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Extraction service is not available"
        )


@router.get("/supported-types")
async def get_supported_extraction_types() -> Any:
    """
    Get list of supported extraction types and fraud types
    """
    return {
        "extraction_types": [
            {
                "value": "banking_upi",
                "label": "Banking/UPI Transactions",
                "description": "Extract banking and UPI transaction data"
            },
            {
                "value": "card",
                "label": "Card Transactions", 
                "description": "Extract credit/debit card transaction data"
            }
        ],
        "fraud_types": [
            {
                "value": "online_banking",
                "label": "Online Banking Fraud",
                "description": "Fraud involving online banking systems"
            },
            {
                "value": "card",
                "label": "Card Fraud",
                "description": "Fraud involving credit/debit cards"
            },
            {
                "value": "upi",
                "label": "UPI Fraud",
                "description": "Fraud involving UPI transactions"
            }
        ],
        "max_layers": {
            "min": 0,
            "max": 7,
            "default": 7,
            "description": "Maximum transaction layer to extract and visualize"
        }
    }
