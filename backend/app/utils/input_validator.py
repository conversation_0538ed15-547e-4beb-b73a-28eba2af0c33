"""
Input validation utilities for Desktop NCRP Matrix Backend
Implements comprehensive input sanitization and validation
"""
import re
import html
import logging
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

# Regex patterns for validation
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
PASSWORD_PATTERN = re.compile(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$')
NAME_PATTERN = re.compile(r'^[a-zA-Z\s\-\'\.]{1,50}$')
PHONE_PATTERN = re.compile(r'^\+?[1-9]\d{1,14}$')

# Dangerous patterns to detect
XSS_PATTERNS = [
    re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
    re.compile(r'javascript:', re.IGNORECASE),
    re.compile(r'on\w+\s*=', re.IGNORECASE),
    re.compile(r'<iframe[^>]*>', re.IGNORECASE),
    re.compile(r'<object[^>]*>', re.IGNORECASE),
    re.compile(r'<embed[^>]*>', re.IGNORECASE),
    re.compile(r'<link[^>]*>', re.IGNORECASE),
    re.compile(r'<meta[^>]*>', re.IGNORECASE),
]

SQL_INJECTION_PATTERNS = [
    re.compile(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)', re.IGNORECASE),
    re.compile(r'(\b(OR|AND)\s+\d+\s*=\s*\d+)', re.IGNORECASE),
    re.compile(r'(\'\s*(OR|AND)\s*\')', re.IGNORECASE),
    re.compile(r'(--|\#|/\*|\*/)', re.IGNORECASE),
]

# File upload validation
ALLOWED_HTML_EXTENSIONS = ['.html', '.htm']
MAX_FILENAME_LENGTH = 255
DANGEROUS_EXTENSIONS = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js']


class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass


def sanitize_string(value: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize string input by removing dangerous content
    
    Args:
        value: Input string to sanitize
        max_length: Maximum allowed length
        
    Returns:
        str: Sanitized string
        
    Raises:
        ValidationError: If input is invalid
    """
    if not isinstance(value, str):
        raise ValidationError("Input must be a string")
    
    # Remove null bytes and control characters
    value = value.replace('\x00', '').replace('\r', '').replace('\n', ' ')
    
    # HTML escape to prevent XSS
    value = html.escape(value, quote=True)
    
    # Trim whitespace
    value = value.strip()
    
    # Check length
    if max_length and len(value) > max_length:
        raise ValidationError(f"Input too long. Maximum length: {max_length}")
    
    # Check for XSS patterns
    for pattern in XSS_PATTERNS:
        if pattern.search(value):
            logger.warning(f"XSS pattern detected in input: {value[:100]}...")
            raise ValidationError("Invalid input detected")
    
    # Check for SQL injection patterns
    for pattern in SQL_INJECTION_PATTERNS:
        if pattern.search(value):
            logger.warning(f"SQL injection pattern detected in input: {value[:100]}...")
            raise ValidationError("Invalid input detected")
    
    return value


def validate_email(email: str) -> str:
    """
    Validate and sanitize email address
    
    Args:
        email: Email address to validate
        
    Returns:
        str: Validated email address
        
    Raises:
        ValidationError: If email is invalid
    """
    if not email:
        raise ValidationError("Email is required")
    
    # Sanitize
    email = sanitize_string(email, max_length=254).lower()
    
    # Validate format
    if not EMAIL_PATTERN.match(email):
        raise ValidationError("Invalid email format")
    
    # Additional checks
    if '..' in email or email.startswith('.') or email.endswith('.'):
        raise ValidationError("Invalid email format")
    
    return email


def validate_password(password: str) -> str:
    """
    Validate password strength
    
    Args:
        password: Password to validate
        
    Returns:
        str: Validated password
        
    Raises:
        ValidationError: If password is invalid
    """
    if not password:
        raise ValidationError("Password is required")
    
    if len(password) < 8:
        raise ValidationError("Password must be at least 8 characters long")
    
    if len(password) > 128:
        raise ValidationError("Password too long. Maximum length: 128")
    
    # Check for required character types
    if not re.search(r'[a-z]', password):
        raise ValidationError("Password must contain at least one lowercase letter")
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError("Password must contain at least one uppercase letter")
    
    if not re.search(r'\d', password):
        raise ValidationError("Password must contain at least one digit")
    
    if not re.search(r'[@$!%*?&]', password):
        raise ValidationError("Password must contain at least one special character (@$!%*?&)")
    
    # Check for common weak patterns
    if password.lower() in ['password', '12345678', 'qwerty123', 'admin123']:
        raise ValidationError("Password is too common")
    
    return password


def validate_name(name: str, field_name: str = "Name") -> str:
    """
    Validate and sanitize name fields
    
    Args:
        name: Name to validate
        field_name: Name of the field for error messages
        
    Returns:
        str: Validated name
        
    Raises:
        ValidationError: If name is invalid
    """
    if not name:
        raise ValidationError(f"{field_name} is required")
    
    # Sanitize
    name = sanitize_string(name, max_length=50)
    
    # Validate format
    if not NAME_PATTERN.match(name):
        raise ValidationError(f"{field_name} contains invalid characters")
    
    if len(name) < 1:
        raise ValidationError(f"{field_name} is too short")
    
    return name.title()  # Capitalize properly


def validate_phone(phone: str) -> str:
    """
    Validate and sanitize phone number
    
    Args:
        phone: Phone number to validate
        
    Returns:
        str: Validated phone number
        
    Raises:
        ValidationError: If phone is invalid
    """
    if not phone:
        raise ValidationError("Phone number is required")
    
    # Remove spaces, dashes, and parentheses
    phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # Sanitize
    phone = sanitize_string(phone, max_length=20)
    
    # Validate format
    if not PHONE_PATTERN.match(phone):
        raise ValidationError("Invalid phone number format")
    
    return phone


def validate_filename(filename: str) -> str:
    """
    Validate uploaded filename for security
    
    Args:
        filename: Filename to validate
        
    Returns:
        str: Validated filename
        
    Raises:
        ValidationError: If filename is invalid
    """
    if not filename:
        raise ValidationError("Filename is required")
    
    # Sanitize
    filename = sanitize_string(filename, max_length=MAX_FILENAME_LENGTH)
    
    # Check for path traversal
    if '..' in filename or '/' in filename or '\\' in filename:
        raise ValidationError("Invalid filename: path traversal detected")
    
    # Check extension
    extension = filename.lower().split('.')[-1] if '.' in filename else ''
    
    if f'.{extension}' in DANGEROUS_EXTENSIONS:
        raise ValidationError("File type not allowed for security reasons")
    
    if f'.{extension}' not in ALLOWED_HTML_EXTENSIONS:
        raise ValidationError(f"Only HTML files are allowed. Allowed extensions: {ALLOWED_HTML_EXTENSIONS}")
    
    return filename


def validate_integer(value: Any, min_value: Optional[int] = None, max_value: Optional[int] = None, field_name: str = "Value") -> int:
    """
    Validate integer input
    
    Args:
        value: Value to validate
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        field_name: Name of the field for error messages
        
    Returns:
        int: Validated integer
        
    Raises:
        ValidationError: If value is invalid
    """
    try:
        value = int(value)
    except (ValueError, TypeError):
        raise ValidationError(f"{field_name} must be a valid integer")
    
    if min_value is not None and value < min_value:
        raise ValidationError(f"{field_name} must be at least {min_value}")
    
    if max_value is not None and value > max_value:
        raise ValidationError(f"{field_name} must be at most {max_value}")
    
    return value


def validate_url(url: str, allowed_schemes: Optional[List[str]] = None) -> str:
    """
    Validate URL for security
    
    Args:
        url: URL to validate
        allowed_schemes: List of allowed URL schemes
        
    Returns:
        str: Validated URL
        
    Raises:
        ValidationError: If URL is invalid
    """
    if not url:
        raise ValidationError("URL is required")
    
    if allowed_schemes is None:
        allowed_schemes = ['http', 'https']
    
    # Sanitize
    url = sanitize_string(url, max_length=2048)
    
    try:
        parsed = urlparse(url)
    except Exception:
        raise ValidationError("Invalid URL format")
    
    if parsed.scheme not in allowed_schemes:
        raise ValidationError(f"URL scheme not allowed. Allowed schemes: {allowed_schemes}")
    
    if not parsed.netloc:
        raise ValidationError("URL must have a valid domain")
    
    return url


def sanitize_dict(data: Dict[str, Any], allowed_keys: List[str]) -> Dict[str, Any]:
    """
    Sanitize dictionary by removing unexpected keys and validating values
    
    Args:
        data: Dictionary to sanitize
        allowed_keys: List of allowed keys
        
    Returns:
        Dict[str, Any]: Sanitized dictionary
        
    Raises:
        ValidationError: If data is invalid
    """
    if not isinstance(data, dict):
        raise ValidationError("Input must be a dictionary")
    
    sanitized = {}
    
    for key, value in data.items():
        if key not in allowed_keys:
            logger.warning(f"Unexpected key in input: {key}")
            continue
        
        if isinstance(value, str):
            sanitized[key] = sanitize_string(value)
        elif isinstance(value, (int, float, bool)):
            sanitized[key] = value
        elif value is None:
            sanitized[key] = None
        else:
            logger.warning(f"Unexpected value type for key {key}: {type(value)}")
            continue
    
    return sanitized


def validate_json_size(data: str, max_size: int = 1024 * 1024) -> None:
    """
    Validate JSON data size to prevent DoS attacks
    
    Args:
        data: JSON string to validate
        max_size: Maximum allowed size in bytes
        
    Raises:
        ValidationError: If data is too large
    """
    if len(data.encode('utf-8')) > max_size:
        raise ValidationError(f"Request too large. Maximum size: {max_size} bytes")
