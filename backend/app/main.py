"""
Desktop NCRP Matrix Backend
FastAPI application with enterprise-level security for authentication and HTML extraction
"""
import logging
import sys
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, status, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidation<PERSON>rror
import structlog

from app.core.config import settings
from app.core.database import connect_to_mongo, close_mongo_connection
from app.api.routes import auth, extraction
from app.middleware.security_middleware import SecurityHeadersMiddleware
from app.middleware.rate_limit_middleware import RateLimitMiddleware

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# Setup logging
logging.basicConfig(
    format="%(message)s",
    stream=sys.stdout,
    level=getattr(logging, settings.LOG_LEVEL.upper())
)
logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Desktop NCRP Matrix Backend", version=settings.VERSION)
    try:
        await connect_to_mongo()
        logger.info("Database connection established successfully")
        app.state.database_available = True
    except Exception as e:
        logger.error("Failed to connect to database", error=str(e))
        if settings.ENVIRONMENT == "development":
            logger.warning("Development mode: Database connection failed")
            logger.warning("This may be due to:")
            logger.warning("1. MongoDB Atlas network/IP whitelist issues")
            logger.warning("2. Invalid connection credentials")
            logger.warning("3. Network connectivity problems")
            logger.warning("Continuing in limited mode - only HTML extraction available")
            app.state.database_available = False
        else:
            logger.error("Production mode: Database connection is required")
            raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Desktop NCRP Matrix Backend")
    await close_mongo_connection()
    logger.info("Database connection closed")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.DEBUG else None,
    docs_url=f"{settings.API_V1_STR}/docs" if settings.DEBUG else None,
    redoc_url=f"{settings.API_V1_STR}/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Security Middleware
app.add_middleware(SecurityHeadersMiddleware)

# Rate Limiting Middleware
app.add_middleware(RateLimitMiddleware)

# Trusted Host Middleware (production only)
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", settings.HOST]
    )

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "X-Client-Type",
        "X-Device-Fingerprint",
        "X-App-Version",
        "User-Agent",
        "Accept",
        "Origin",
        "X-Requested-With",
        "Cache-Control",
        "Pragma"
    ],
    expose_headers=["X-Total-Count", "X-Request-ID"]
)


# Exception Handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors with detailed information"""
    # Convert any non-serializable error context to string
    def serialize_error(err):
        err = dict(err)
        if "ctx" in err and err["ctx"]:
            err["ctx"] = {k: str(v) for k, v in err["ctx"].items()}
        return err
    errors = [serialize_error(e) for e in exc.errors()]
    logger.warning(
        "Validation error",
        path=request.url.path,
        method=request.method,
        errors=errors
    )
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "detail": "Validation error",
            "errors": errors
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc),
        exc_info=True
    )
    
    if settings.DEBUG:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error",
                "error": str(exc)
            }
        )
    else:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"}
        )


# Middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests for monitoring"""
    start_time = logger.info(
        "Request started",
        method=request.method,
        path=request.url.path,
        client_ip=request.client.host if request.client else "unknown",
        user_agent=request.headers.get("User-Agent", ""),
        client_type=request.headers.get("X-Client-Type", "")
    )
    
    response = await call_next(request)
    
    logger.info(
        "Request completed",
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        client_ip=request.client.host if request.client else "unknown"
    )
    
    return response


# Health Check Endpoints
@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    database_status = "connected" if getattr(app.state, 'database_available', False) else "disconnected"

    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "database": database_status,
        "features_available": {
            "html_extraction": True,
            "authentication": getattr(app.state, 'database_available', False),
            "user_management": getattr(app.state, 'database_available', False)
        }
    }


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": f"Welcome to {settings.PROJECT_NAME}",
        "version": settings.VERSION,
        "description": settings.DESCRIPTION,
        "docs_url": f"{settings.API_V1_STR}/docs" if settings.DEBUG else None,
        "health_url": "/health",
        "database_status": "connected" if getattr(app.state, 'database_available', False) else "disconnected"
    }


@app.post("/api/v1/dev/create-test-user")
async def create_test_user():
    """Development endpoint to create test user"""
    if settings.ENVIRONMENT != "development":
        raise HTTPException(status_code=404, detail="Not found")

    if not getattr(app.state, 'database_available', False):
        raise HTTPException(status_code=503, detail="Database not available")

    try:
        from app.core.database import get_database
        from app.core.security import get_password_hash
        from datetime import datetime, timezone, timedelta

        db = get_database()

        # Check if test user already exists
        existing_user = await db.users.find_one({"email": "<EMAIL>"})
        if existing_user:
            return {"message": "Test user already exists", "email": "<EMAIL>"}

        # Create test user
        test_user = {
            "email": "<EMAIL>",
            "hashed_password": get_password_hash("dev123"),
            "first_name": "Developer",
            "last_name": "User",
            "designation": "Developer",
            "email_verified": True,  # Skip email verification for dev user
            "created_at": datetime.now(timezone.utc),
            "paid": True,
            "subscription_start_date": datetime.now(timezone.utc),
            "subscription_expires": datetime.now(timezone.utc) + timedelta(days=365),
            "failed_login_attempts": 0,
            "account_locked_until": None,
            "two_step_enabled": False,
            "active": True
        }

        result = await db.users.insert_one(test_user)

        return {
            "message": "Test user created successfully",
            "email": "<EMAIL>",
            "password": "dev123",
            "user_id": str(result.inserted_id)
        }

    except Exception as e:
        logger.error(f"Failed to create test user: {e}")
        raise HTTPException(status_code=500, detail="Failed to create test user")


# Include API routes
app.include_router(
    auth.router,
    prefix=settings.API_V1_STR,
    tags=["Authentication"]
)

app.include_router(
    extraction.router,
    prefix=settings.API_V1_STR,
    tags=["HTML Extraction"]
)


# Development server
if __name__ == "__main__":
    import uvicorn
    
    logger.info(
        "Starting development server",
        host=settings.HOST,
        port=settings.PORT,
        debug=settings.DEBUG
    )
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
