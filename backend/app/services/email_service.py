"""
Email service for Desktop NCRP Matrix Backend
Handles user authentication emails with enterprise-level security
"""
import smtplib
import secrets
import asyncio
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor
from typing import Optional

from app.core.config import settings

logger = logging.getLogger(__name__)

# Thread pool for sending emails asynchronously
email_thread_pool = ThreadPoolExecutor(max_workers=4)


def generate_otp(length: int = 6) -> str:
    """
    Generate a secure alphanumeric OTP
    
    Args:
        length: Length of the OTP (default: 6)
        
    Returns:
        str: Secure OTP with mixed letters and numbers
    """
    # Exclude confusing characters
    digits = '23456789'
    letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'
    
    # Ensure at least 2 digits and 2 letters for security
    num_digits = max(2, length // 3)
    num_letters = length - num_digits
    
    # Generate components
    otp_digits = ''.join(secrets.choice(digits) for _ in range(num_digits))
    otp_letters = ''.join(secrets.choice(letters) for _ in range(num_letters))
    
    # Combine and shuffle
    combined = list(otp_digits + otp_letters)
    secrets.SystemRandom().shuffle(combined)
    
    return ''.join(combined)


def get_otp_expiry(minutes: int = 15) -> datetime:
    """
    Get OTP expiry time
    
    Args:
        minutes: Number of minutes until expiry (default: 15)
        
    Returns:
        datetime: UTC timestamp when OTP expires
    """
    return datetime.now(timezone.utc) + timedelta(minutes=minutes)


def _send_email_sync(to_email: str, subject: str, html_content: str) -> bool:
    """
    Synchronous email sending function (runs in thread pool)
    
    Args:
        to_email: Recipient email address
        subject: Email subject
        html_content: HTML email content
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    # Redact email for security logging
    email_domain = to_email.split('@')[1] if '@' in to_email else '[INVALID]'
    logger.info(f"Attempting to send email to [REDACTED]@{email_domain} with subject: {subject}")
    
    if not settings.email_enabled:
        logger.error("Email service not configured properly")
        return False
    
    # Create message
    msg = MIMEMultipart()
    msg['From'] = f"{settings.EMAIL_FROM_NAME} <{settings.EMAIL_FROM}>"
    msg['To'] = to_email
    msg['Subject'] = subject
    
    # Add HTML content
    msg.attach(MIMEText(html_content, 'html'))
    
    try:
        # Connect to SMTP server
        logger.info("Connecting to SMTP server...")
        if settings.SMTP_USE_SSL:
            server = smtplib.SMTP_SSL(settings.SMTP_HOST, settings.SMTP_PORT)
        else:
            server = smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT)
            server.starttls()
        
        # Check if SMTP credentials are provided
        if settings.SMTP_USERNAME and settings.SMTP_PASSWORD:
            server.login(settings.SMTP_USERNAME, settings.SMTP_PASSWORD)
        else:
            logger.error("SMTP credentials not configured properly")
            server.quit()
            return False
        
        server.send_message(msg)
        server.quit()
        
        logger.info(f"Email sent successfully to [REDACTED]@{email_domain}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send email: {str(e)}")
        return False


async def _send_email(to_email: str, subject: str, html_content: str) -> bool:
    """
    Asynchronous email sending wrapper
    
    Args:
        to_email: Recipient email address
        subject: Email subject
        html_content: HTML email content
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    email_domain = to_email.split('@')[1] if '@' in to_email else '[INVALID]'
    logger.info(f"Queueing email to [REDACTED]@{email_domain}")
    
    # Run in thread pool
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        email_thread_pool,
        lambda: _send_email_sync(to_email, subject, html_content)
    )
    
    return result


async def send_verification_email(
    email: str, 
    otp: str, 
    name: str = "", 
    is_login: bool = False
) -> bool:
    """
    Send email verification or login OTP
    
    Args:
        email: Recipient email address
        otp: One-time password
        name: Recipient's name
        is_login: Whether this is for login verification
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    email_domain = email.split('@')[1] if '@' in email else '[INVALID]'
    
    if is_login:
        logger.info(f"Preparing login verification email for [REDACTED]@{email_domain}")
        subject = "Login Verification Code - NCRP Matrix Desktop"
        title = "Login Verification"
        message = f"Hello {name}, we received a login request for your NCRP Matrix Desktop account. Please use the verification code below to complete your login:"
        expiry = "5 minutes"
        note = "If you didn't attempt to login, please change your password immediately."
        color_scheme = "#0891B2"
    else:
        logger.info(f"Preparing email verification for [REDACTED]@{email_domain}")
        subject = "Verify your NCRP Matrix Desktop account"
        title = f"Welcome {name} to NCRP Matrix Desktop!"
        message = "Thank you for registering with NCRP Matrix Desktop. Please verify your email address using the verification code below:"
        expiry = "15 minutes"
        note = "If you didn't create this account, please ignore this email."
        color_scheme = "#0891B2"
    
    # Create HTML content
    html_content = f"""
        <html>
        <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div style="background: linear-gradient(135deg, {color_scheme} 0%, #0369A1 100%); padding: 30px 20px; text-align: center;">
                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">{title}</h1>
                    <p style="color: #E0F2FE; margin: 10px 0 0 0; font-size: 16px;">NCRP Matrix Desktop - Professional Cyber Crime Investigation Platform</p>
                </div>
                <div style="padding: 30px 20px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">{message}</p>
                    <div style="background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%); padding: 20px; border-radius: 8px; text-align: center; margin: 25px 0; border: 2px solid {color_scheme};">
                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #0369A1; font-weight: 600;">VERIFICATION CODE</p>
                        <div style="font-size: 32px; font-weight: bold; color: {color_scheme}; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                            {otp}
                        </div>
                    </div>
                    <div style="background-color: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px; color: #92400E;"><strong>Important:</strong> This code contains both letters and numbers. Please enter it exactly as shown above.</p>
                    </div>
                    <p style="font-size: 14px; color: #6B7280;">This verification code will expire in <strong>{expiry}</strong>.</p>
                    <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 25px 0;">
                    <p style="font-size: 14px; color: #6B7280;">{note}</p>
                    <div style="margin-top: 30px;">
                        <p style="margin: 0; font-size: 16px; color: #374151;">Best regards,</p>
                        <p style="margin: 5px 0 0 0; font-size: 16px; color: {color_scheme}; font-weight: 600;">The NCRP Matrix Desktop Team</p>
                    </div>
                </div>
                <div style="background-color: #F9FAFB; padding: 20px; text-align: center; border-top: 1px solid #E5E7EB;">
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #9CA3AF;">This is an automated message, please do not reply to this email.</p>
                    <p style="margin: 0; font-size: 12px; color: #6B7280;">♥ Made with the help of PS Cyber Crime Bhiwani</p>
                </div>
            </div>
        </body>
        </html>
    """
    
    result = await _send_email(
        to_email=email,
        subject=subject,
        html_content=html_content
    )
    
    logger.info(f"Verification email send result: {result}")
    return result


async def send_password_reset_email(email: str, otp: str, name: str = "") -> bool:
    """
    Send password reset OTP email
    
    Args:
        email: Recipient email address
        otp: One-time password for reset
        name: Recipient's name
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    email_domain = email.split('@')[1] if '@' in email else '[INVALID]'
    logger.info(f"Preparing password reset email for [REDACTED]@{email_domain}")
    
    # Create HTML content with red color scheme for password reset
    html_content = f"""
        <html>
        <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div style="background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%); padding: 30px 20px; text-align: center;">
                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">Password Reset Request</h1>
                    <p style="color: #FEE2E2; margin: 10px 0 0 0; font-size: 16px;">NCRP Matrix Desktop - Professional Cyber Crime Investigation Platform</p>
                </div>
                <div style="padding: 30px 20px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">Hello {name},</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">We received a request to reset your NCRP Matrix Desktop account password.</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">Please use the verification code below to reset your password:</p>
                    <div style="background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%); padding: 20px; border-radius: 8px; text-align: center; margin: 25px 0; border: 2px solid #DC2626;">
                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #B91C1C; font-weight: 600;">PASSWORD RESET CODE</p>
                        <div style="font-size: 32px; font-weight: bold; color: #DC2626; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                            {otp}
                        </div>
                    </div>
                    <div style="background-color: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px; color: #92400E;"><strong>Important:</strong> This code contains both letters and numbers. Please enter it exactly as shown above.</p>
                    </div>
                    <p style="font-size: 14px; color: #6B7280;">This verification code will expire in <strong>15 minutes</strong>.</p>
                    <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 25px 0;">
                    <div style="background-color: #FEF2F2; border-left: 4px solid #DC2626; padding: 15px; margin: 20px 0; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px; color: #B91C1C;">If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
                    </div>
                    <div style="margin-top: 30px;">
                        <p style="margin: 0; font-size: 16px; color: #374151;">Best regards,</p>
                        <p style="margin: 5px 0 0 0; font-size: 16px; color: #0891B2; font-weight: 600;">The NCRP Matrix Desktop Team</p>
                    </div>
                </div>
                <div style="background-color: #F9FAFB; padding: 20px; text-align: center; border-top: 1px solid #E5E7EB;">
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #9CA3AF;">This is an automated message, please do not reply to this email.</p>
                    <p style="margin: 0; font-size: 12px; color: #6B7280;">♥ Made with the help of PS Cyber Crime Bhiwani</p>
                </div>
            </div>
        </body>
        </html>
    """
    
    result = await _send_email(
        to_email=email,
        subject="Reset Your NCRP Matrix Desktop Password",
        html_content=html_content
    )
    
    logger.info(f"Password reset email send result: {result}")
    return result


async def test_email_configuration(email: str) -> bool:
    """
    Test email configuration by sending a test email
    
    Args:
        email: Test recipient email address
        
    Returns:
        bool: True if test email sent successfully, False otherwise
    """
    email_domain = email.split('@')[1] if '@' in email else '[INVALID]'
    logger.info(f"Testing email configuration by sending to [REDACTED]@{email_domain}")
    
    html_content = """
        <html>
        <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div style="background: linear-gradient(135deg, #0891B2 0%, #0369A1 100%); padding: 30px 20px; text-align: center;">
                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">Email Configuration Test</h1>
                    <p style="color: #E0F2FE; margin: 10px 0 0 0; font-size: 16px;">NCRP Matrix Desktop - Professional Cyber Crime Investigation Platform</p>
                </div>
                <div style="padding: 30px 20px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">This is a test email to verify that your NCRP Matrix Desktop email configuration is working correctly.</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">If you received this email, your SMTP settings are configured properly.</p>
                    <div style="margin-top: 30px;">
                        <p style="margin: 0; font-size: 16px; color: #374151;">Best regards,</p>
                        <p style="margin: 5px 0 0 0; font-size: 16px; color: #0891B2; font-weight: 600;">The NCRP Matrix Desktop Team</p>
                    </div>
                </div>
                <div style="background-color: #F9FAFB; padding: 20px; text-align: center; border-top: 1px solid #E5E7EB;">
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #9CA3AF;">This is an automated message, please do not reply to this email.</p>
                    <p style="margin: 0; font-size: 12px; color: #6B7280;">♥ Made with the help of PS Cyber Crime Bhiwani</p>
                </div>
            </div>
        </body>
        </html>
    """
    
    result = await _send_email(
        to_email=email,
        subject="NCRP Matrix Desktop Email Configuration Test",
        html_content=html_content
    )
    
    logger.info(f"Test email send result: {result}")
    return result
