"""
Security utilities following 2025 FastAPI best practices
Implements Argon2 password hashing and JWT token management
"""
import secrets
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, List, Tuple
from jose import jwt, JW<PERSON>rror
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTT<PERSON>Ex<PERSON>, status, Depends
from fastapi.security import OAuth2<PERSON>asswordBearer
from app.core.config import settings


# Password hashing context with Argon2 (2025 best practice)
pwd_context = CryptContext(
    schemes=settings.PASSWORD_HASH_SCHEMES,
    deprecated="auto",
    argon2__time_cost=settings.ARGON2_TIME_COST,
    argon2__memory_cost=settings.ARGON2_MEMORY_COST,
    argon2__parallelism=settings.ARGON2_PARALLELISM,
    argon2__hash_len=settings.ARGON2_HASH_LENGTH
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")


def get_password_hash(password: str) -> str:
    """
    Hash a password using Argon2 (2025 security standard)
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash with automatic migration support
    """
    return pwd_context.verify(plain_password, hashed_password)


def create_access_token(
    subject: str,
    roles: Optional[List[str]] = None,
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT access token with enhanced security
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode = {
        "sub": str(subject),
        "exp": expire,
        "iat": datetime.now(timezone.utc),
        "jti": secrets.token_urlsafe(16),
        "type": "access",
        "roles": roles or ["user"],
        "iss": settings.PROJECT_NAME,
    }
    return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)


def create_refresh_token(subject: str) -> str:
    """
    Create JWT refresh token with extended expiration
    """
    expire = datetime.now(timezone.utc) + timedelta(
        days=settings.REFRESH_TOKEN_EXPIRE_DAYS
    )
    
    to_encode = {
        "sub": str(subject),
        "exp": expire,
        "iat": datetime.now(timezone.utc),
        "jti": secrets.token_urlsafe(16),
        "type": "refresh",
        "iss": settings.PROJECT_NAME,
    }
    
    return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)


def create_tokens(user_email: str, roles: Optional[List[str]] = None) -> Tuple[str, str]:
    """
    Create both access and refresh tokens
    """
    access_token = create_access_token(subject=user_email, roles=roles)
    refresh_token = create_refresh_token(subject=user_email)
    return access_token, refresh_token


def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
    """
    Verify and decode JWT token with comprehensive validation
    """
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        
        # Verify token type
        if payload.get("type") != token_type:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token type: expected {token_type}",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify issuer
        if payload.get("iss") != settings.PROJECT_NAME:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token issuer",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify expiration
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return payload
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )


def generate_session_id() -> str:
    """
    Generate a secure session ID
    """
    return secrets.token_urlsafe(32)


def generate_csrf_token() -> str:
    """
    Generate CSRF token for additional security
    """
    return secrets.token_urlsafe(32)


def get_current_user(token: str = Depends(oauth2_scheme)) -> dict:
    """
    FastAPI dependency to get the current user from the JWT token.
    """
    payload = verify_token(token)
    return payload
