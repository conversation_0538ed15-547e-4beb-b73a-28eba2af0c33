"""
Configuration settings for the Desktop NCRP Matrix Backend
Following 2025 FastAPI best practices for security and performance
"""
import secrets
from typing import List, Optional, Union
from pydantic import field_validator, AnyHttpUrl, EmailStr
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with security-first approach"""

    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Desktop NCRP Matrix Backend"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Lightweight FastAPI backend for authentication and HTML extraction"

    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    # Security - JWT Configuration
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Security - Password Hashing (Argon2 - 2025 best practice)
    PASSWORD_HASH_SCHEMES: List[str] = ["argon2", "bcrypt"]
    ARGON2_TIME_COST: int = 3
    ARGON2_MEMORY_COST: int = 65536  # 64MB
    ARGON2_PARALLELISM: int = 4
    ARGON2_HASH_LENGTH: int = 32

    # Security - Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    MAX_FAILED_LOGIN_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_MINUTES: int = 15

    # Database
    MONGODB_URL: str = "mongodb://localhost:27017"
    DATABASE_NAME: str = "ncrp_matrix_desktop"

    # Email Configuration (REQUIRED for production)
    SMTP_HOST: str = "smtp.hostinger.com"
    SMTP_PORT: int = 465
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_USE_SSL: bool = True
    EMAIL_FROM: Optional[EmailStr] = None
    EMAIL_FROM_NAME: str = "NCRP Matrix Desktop"

    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:3000",  # Vite dev server port
        "http://127.0.0.1:3000",  # Vite dev server port
        "http://localhost:8080",  # Alternative dev port
        "http://127.0.0.1:8080",  # Alternative dev port
    ]

    # Server Configuration
    HOST: str = "127.0.0.1"
    PORT: int = 8000

    # Logging
    LOG_LEVEL: str = "INFO"

    # Cookie Settings
    SECURE_COOKIES: bool = False  # Set to True in production
    COOKIE_SAMESITE: str = "lax"
    COOKIE_DOMAIN: Optional[str] = None

    # File Upload Limits
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [".html", ".htm"]

    # Production Security Settings
    ENABLE_SECURITY_HEADERS: bool = True
    ENABLE_RATE_LIMITING: bool = True
    ENABLE_IP_BLOCKING: bool = True
    BLOCK_DURATION: int = 1800  # 30 minutes

    # Health Check Settings
    HEALTH_CHECK_TIMEOUT: int = 30
    DATABASE_HEALTH_CHECK: bool = True
    
    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        raise ValueError(v)

    @field_validator("SECRET_KEY", mode="before")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v

    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT.lower() == "production"

    @property
    def mongodb_url_with_db(self) -> str:
        return f"{self.MONGODB_URL}/{self.DATABASE_NAME}"

    @property
    def email_enabled(self) -> bool:
        """Check if email service is properly configured"""
        return bool(
            self.EMAIL_FROM and
            self.SMTP_USERNAME and
            self.SMTP_PASSWORD and
            self.SMTP_HOST
        )

    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"
    }


# Global settings instance
settings = Settings()
