"""
Security middleware for Desktop NCRP Matrix Backend
Implements enterprise-level security headers and protections
"""
import secrets
import time
import logging
from typing import Dict
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings

logger = logging.getLogger(__name__)

# Cache for nonce values with TTL
NONCE_CACHE: Dict[str, float] = {}


def generate_nonce() -> str:
    """Generate a secure nonce value for CSP"""
    return secrets.token_hex(16)


def get_request_nonce(request_id: str) -> str:
    """
    Get or create a nonce for a specific request
    
    Args:
        request_id: Unique identifier for the request
        
    Returns:
        str: The nonce value
    """
    # Clean up expired nonces (older than 5 minutes)
    current_time = time.time()
    expired_keys = [k for k, v in NONCE_CACHE.items() if current_time - v > 300]
    for key in expired_keys:
        NONCE_CACHE.pop(key, None)
    
    # Generate new nonce if needed
    if request_id not in NONCE_CACHE:
        NONCE_CACHE[request_id] = current_time
        request_id = generate_nonce()
    
    return request_id


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Security headers middleware for Desktop NCRP Matrix Backend
    Implements enterprise-level security protections
    """
    
    async def dispatch(self, request: Request, call_next):
        # Generate unique request ID and nonce
        request_id = request.headers.get("X-Request-ID", generate_nonce())
        nonce = get_request_nonce(request_id)
        
        # Store nonce in request state for use in templates
        request.state.csp_nonce = nonce
        
        # Process the request
        response = await call_next(request)
        
        # Skip security headers if disabled in config
        if not settings.ENABLE_SECURITY_HEADERS:
            return response
        
        # Add enhanced security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["X-Permitted-Cross-Domain-Policies"] = "none"
        response.headers["X-DNS-Prefetch-Control"] = "off"
        response.headers["X-Download-Options"] = "noopen"
        
        # Add HSTS header in production
        if settings.is_production:
            response.headers["Strict-Transport-Security"] = "max-age=63072000; includeSubDomains; preload"
        
        # Content Security Policy for Desktop application
        csp_policy = (
            f"default-src 'self'; "
            f"script-src 'self' 'nonce-{nonce}' 'unsafe-inline'; "
            f"style-src 'self' 'nonce-{nonce}' 'unsafe-inline'; "
            f"font-src 'self' data:; "
            f"img-src 'self' data: blob:; "
            f"connect-src 'self'; "
            f"media-src 'self' data: blob:; "
            f"object-src 'none'; "
            f"base-uri 'self'; "
            f"form-action 'self'; "
            f"frame-ancestors 'none'; "
            f"worker-src 'self' blob:; "
            f"child-src 'self' blob:; "
            f"manifest-src 'self'"
        )
        
        # Add upgrade-insecure-requests in production
        if settings.is_production:
            csp_policy += "; upgrade-insecure-requests"
        
        response.headers["Content-Security-Policy"] = csp_policy
        
        # Permissions Policy - restrict browser features
        response.headers["Permissions-Policy"] = (
            "accelerometer=(), "
            "autoplay=(), "
            "camera=(), "
            "clipboard-read=(), "
            "clipboard-write=(), "
            "display-capture=(), "
            "encrypted-media=(), "
            "fullscreen=(), "
            "gamepad=(), "
            "geolocation=(), "
            "gyroscope=(), "
            "hid=(), "
            "idle-detection=(), "
            "magnetometer=(), "
            "microphone=(), "
            "midi=(), "
            "payment=(), "
            "picture-in-picture=(), "
            "publickey-credentials-get=(), "
            "screen-wake-lock=(), "
            "serial=(), "
            "sync-xhr=(), "
            "usb=(), "
            "xr-spatial-tracking=()"
        )
        
        # Add Cache-Control for API responses
        if request.url.path.startswith("/api") or request.url.path.startswith("/auth"):
            response.headers["Cache-Control"] = "no-store, max-age=0"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        
        # Add Cross-Origin headers for API responses
        if request.url.path.startswith("/api"):
            response.headers["Cross-Origin-Resource-Policy"] = "same-origin"
            response.headers["Cross-Origin-Opener-Policy"] = "same-origin"
            response.headers["Cross-Origin-Embedder-Policy"] = "require-corp"
        
        return response
