"""
Rate limiting middleware for Desktop NCRP Matrix Backend
Implements intelligent rate limiting with user-based and IP-based controls
"""
import time
import logging
import re
from collections import defaultdict
from typing import Tuple, Dict
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.config import settings

logger = logging.getLogger(__name__)

# Define endpoint categories for granular rate limiting
ENDPOINT_CATEGORIES = {
    'auth_login': {
        'pattern': r'^/api/v1/auth/login$',
        'limit': 10,    # 10 requests per minute
        'window': 60,   # 1 minute window
        'per_user': True,
    },
    'auth_register': {
        'pattern': r'^/api/v1/auth/register$',
        'limit': 5,     # 5 requests per 5 minutes
        'window': 300,  # 5 minute window
        'per_user': False,  # IP-based
    },
    'auth_password_reset': {
        'pattern': r'^/api/v1/auth/forgot-password$|^/api/v1/auth/reset-password$',
        'limit': 5,     # 5 requests per 5 minutes
        'window': 300,  # 5 minute window
        'per_user': True,
    },
    'auth_verify': {
        'pattern': r'^/api/v1/auth/verify$|^/api/v1/auth/refresh$',
        'limit': 20,    # 20 requests per minute
        'window': 60,   # 1 minute window
        'per_user': True,
    },
    'extraction_html': {
        'pattern': r'^/api/v1/extraction/parse-html$',
        'limit': 30,    # 30 requests per minute (resource intensive)
        'window': 60,   # 1 minute window
        'per_user': True,
    },
    'general_api': {
        'pattern': r'^/api/v1/',
        'limit': 100,   # 100 requests per minute
        'window': 60,   # 1 minute window
        'per_user': True,
    },
}


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware with intelligent controls
    """
    
    def __init__(self, app):
        super().__init__(app)
        
        # Compile regex patterns for endpoint categories
        self.endpoint_categories = {}
        for category, config in ENDPOINT_CATEGORIES.items():
            self.endpoint_categories[category] = {
                'pattern': re.compile(config['pattern']),
                'limit': config['limit'],
                'window': config['window'],
                'per_user': config.get('per_user', False)
            }
        
        # Store request timestamps by key and endpoint category
        self.requests = defaultdict(list)
        
        # Track suspicious IPs
        self.suspicious_ips = {}
        
        # Last cleanup time
        self.last_cleanup = time.time()
        
        logger.info(f"Rate limit middleware initialized with {len(self.endpoint_categories)} endpoint categories")
    
    async def dispatch(self, request: Request, call_next):
        # Skip rate limiting if disabled
        if not settings.ENABLE_RATE_LIMITING:
            return await call_next(request)
        
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Get current time
        now = time.time()
        
        # Periodic cleanup (every 5 minutes)
        if now - self.last_cleanup > 300:
            self._cleanup_old_requests(now)
            self.last_cleanup = now
        
        # Check if IP is banned
        if client_ip in self.suspicious_ips:
            ban_until, reason = self.suspicious_ips[client_ip]
            if now < ban_until:
                ban_remaining = int(ban_until - now)
                logger.warning(f"Blocked request from banned IP {client_ip}: {reason}. Ban remaining: {ban_remaining}s")
                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": "Too many requests. Please try again later.",
                        "success": False,
                        "retry_after": ban_remaining
                    },
                    headers={"Retry-After": str(ban_remaining)}
                )
            else:
                # Ban expired
                del self.suspicious_ips[client_ip]
        
        # Determine endpoint category and get rate limit
        path = request.url.path
        category, rate_limit, window, per_user = self._get_endpoint_category(path)
        
        # Try to get user identifier for per-user rate limiting
        user_id = None
        if per_user:
            user_id = await self._get_user_identifier(request)
        
        # Create rate limiting key
        if user_id and per_user:
            key = f"user:{user_id}:{category}"
            logger.debug(f"Using per-user rate limiting for {user_id} on {category}")
        else:
            key = f"ip:{client_ip}:{category}"
            logger.debug(f"Using IP-based rate limiting for {client_ip} on {category}")
        
        # Get request times for this key
        requests_list = self.requests.get(key, [])
        
        # Remove requests outside the window
        requests_list = [t for t in requests_list if now - t < window]
        
        # Check if rate limit exceeded
        if len(requests_list) >= rate_limit:
            # Calculate ban duration based on violation severity
            ban_duration = self._calculate_ban_duration(len(requests_list), rate_limit)
            
            if ban_duration > 0 and settings.ENABLE_IP_BLOCKING:
                # Add to suspicious IPs with ban
                self.suspicious_ips[client_ip] = (now + ban_duration, f"Rate limit exceeded on {category}")
                
                logger.warning(
                    f"Rate limit severely exceeded for {key}: "
                    f"{len(requests_list)} requests in {window} seconds. IP {client_ip} banned for {ban_duration} seconds."
                )
            else:
                logger.warning(
                    f"Rate limit exceeded for {key}: "
                    f"{len(requests_list)} requests in {window} seconds"
                )
            
            # Return 429 Too Many Requests
            retry_after = ban_duration if ban_duration > 0 else window
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "Too many requests. Please try again later.",
                    "success": False,
                    "retry_after": retry_after
                },
                headers={"Retry-After": str(retry_after)}
            )
        
        # Add current request time
        requests_list.append(now)
        self.requests[key] = requests_list
        
        # Process request
        return await call_next(request)
    
    def _get_endpoint_category(self, path: str) -> Tuple[str, int, int, bool]:
        """
        Determine the endpoint category and its rate limit
        
        Returns:
            Tuple containing:
            - category name (str)
            - rate limit (int)
            - window in seconds (int)
            - whether to apply per-user limiting (bool)
        """
        # Check each category pattern in order of specificity
        for category, config in self.endpoint_categories.items():
            if config['pattern'].search(path):
                return category, config['limit'], config['window'], config.get('per_user', False)
        
        # Default to general category
        return "general", settings.RATE_LIMIT_REQUESTS, settings.RATE_LIMIT_WINDOW, False
    
    def _calculate_ban_duration(self, requests: int, limit: int) -> int:
        """
        Calculate ban duration based on violation severity
        
        Args:
            requests: Number of requests made
            limit: Rate limit threshold
            
        Returns:
            int: Ban duration in seconds (0 = no ban)
        """
        # No ban for minor violations
        if requests < limit * 1.5:
            return 0
        
        # Progressive ban duration based on severity
        base_duration = settings.BLOCK_DURATION  # Default 30 minutes
        
        if requests < limit * 2:
            return int(base_duration * 0.1)  # 10% for moderate violations
        elif requests < limit * 3:
            return int(base_duration * 0.5)  # 50% for serious violations
        elif requests < limit * 5:
            return base_duration  # Full duration for severe violations
        else:
            return base_duration * 2  # Double for extreme violations
    
    def _get_client_ip(self, request: Request) -> str:
        """Get the client IP address with proxy support"""
        # Check for X-Forwarded-For header
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # Check for X-Real-IP header
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"
    
    async def _get_user_identifier(self, request: Request) -> str:
        """
        Get user identifier for per-user rate limiting
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: User identifier (email) or empty string if not found
        """
        # Try to get user from Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.replace("Bearer ", "")
            try:
                # Import here to avoid circular imports
                from app.core.security import verify_token
                payload = verify_token(token, "access")
                sub = payload.get("sub")
                if sub:
                    return str(sub)
            except:
                pass
        
        # For auth endpoints, try to get email from request body
        if request.url.path.startswith("/api/v1/auth/") and request.method == "POST":
            try:
                # Try to get request body without consuming it
                body = await request.body()
                request._body = body  # Put the body back
                
                # Try to parse JSON body
                try:
                    json_body = await request.json()
                    val = json_body.get('email') or json_body.get('username')
                    if val:
                        return str(val)
                except:
                    # If not JSON, try form data
                    form_data = await request.form()
                    val = form_data.get('email') or form_data.get('username')
                    if val:
                        return str(val)
            except:
                pass
        
        return ""
    
    def _cleanup_old_requests(self, now: float) -> None:
        """Clean up old request records to prevent memory leaks"""
        # Clean up old requests
        for key in list(self.requests.keys()):
            # Get category from key
            parts = key.split(":", 2)
            if len(parts) >= 3:
                category = parts[2]
            else:
                category = "general"
            
            # Get window for this category
            window = settings.RATE_LIMIT_WINDOW
            for cat_name, config in self.endpoint_categories.items():
                if cat_name == category:
                    window = config['window']
                    break
            
            # Calculate cutoff time
            cutoff = now - window
            
            # Filter out old requests
            self.requests[key] = [t for t in self.requests[key] if t >= cutoff]
            
            # Remove empty lists
            if not self.requests[key]:
                del self.requests[key]
        
        # Clean up expired bans
        for ip in list(self.suspicious_ips.keys()):
            ban_until, _ = self.suspicious_ips[ip]
            if now > ban_until:
                del self.suspicious_ips[ip]
        
        logger.debug(f"Cleaned up rate limit tracking. Current tracked keys: {len(self.requests)}, Banned IPs: {len(self.suspicious_ips)}")
