"""
Subscription middleware for handling trial expiration
"""
from datetime import datetime, timezone
from fastapi import Request
from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings

async def check_subscription_status(request: Request):
    """
    Middleware to check and update subscription status
    """
    if request.url.path.startswith("/api/v1/auth"):
        db = request.app.state.db
        users_collection = db.users

        # Update expired trials
        now = datetime.now(timezone.utc)
        
        # Find users with expired trials but still marked as paid
        await users_collection.update_many(
            {
                "paid": True,
                "subscription_expires": {"$lt": now}
            },
            {
                "$set": {
                    "paid": False,
                    "updated_at": now
                }
            }
        )

    return None
