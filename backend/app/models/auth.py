"""
Authentication models for Desktop NCRP Matrix Backend
"""
from pydantic import BaseModel, EmailStr, validator
from typing import Optional

from app.utils.input_validator import validate_email, validate_password


class EmailRequest(BaseModel):
    """Request model for email-based operations"""
    email: EmailStr
    
    @validator('email')
    def validate_email_format(cls, v):
        return validate_email(str(v))


class PasswordResetRequest(BaseModel):
    """Request model for password reset"""
    email: EmailStr
    otp: str
    password: str
    
    @validator('email')
    def validate_email_format(cls, v):
        return validate_email(str(v))
    
    @validator('otp')
    def validate_otp_format(cls, v):
        if not v or len(v) != 6:
            raise ValueError("OTP must be 6 characters long")
        return v.upper().strip()
    
    @validator('password')
    def validate_password_strength(cls, v):
        return validate_password(v)


class OTPVerificationRequest(BaseModel):
    """Request model for OTP verification"""
    email: EmailStr
    otp: str
    
    @validator('email')
    def validate_email_format(cls, v):
        return validate_email(str(v))
    
    @validator('otp')
    def validate_otp_format(cls, v):
        if not v or len(v) != 6:
            raise ValueError("OTP must be 6 characters long")
        return v.upper().strip()


class PasswordChangeRequest(BaseModel):
    """Request model for password change (authenticated users)"""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_new_password_strength(cls, v):
        return validate_password(v)
    
    @validator('current_password')
    def validate_current_password_not_empty(cls, v):
        if not v:
            raise ValueError("Current password is required")
        return v
