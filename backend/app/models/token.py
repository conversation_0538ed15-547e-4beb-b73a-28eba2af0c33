"""
Token models for JWT authentication
Following 2025 security best practices
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from app.models.user import User


class Token(BaseModel):
    """Token response model"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds until access token expires
    user: Optional[dict] = None  # Add user field for login response


class TokenPayload(BaseModel):
    """JWT token payload model"""
    sub: Optional[str] = None  # subject (user email)
    exp: Optional[int] = None  # expiration timestamp
    iat: Optional[int] = None  # issued at timestamp
    jti: Optional[str] = None  # JWT ID for revocation
    type: Optional[str] = None  # token type (access/refresh)
    roles: List[str] = Field(default_factory=list)
    iss: Optional[str] = None  # issuer


class TokenData(BaseModel):
    """Token data for dependency injection"""
    email: Optional[str] = None
    roles: List[str] = Field(default_factory=list)


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str


class TokenRevoke(BaseModel):
    """Token revocation model"""
    token: str
    token_type: str = "access"  # access or refresh
