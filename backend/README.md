# Desktop NCRP Matrix Backend

A lightweight FastAPI backend designed specifically for the Desktop NCRP Matrix application, focusing on authentication and HTML extraction services.

## Features

### 🔐 Enterprise-Level Authentication
- **Argon2 Password Hashing**: Industry-standard password security (2025 best practice)
- **JWT Token Management**: Secure access and refresh token system
- **Account Lockout Protection**: Progressive lockout after failed login attempts
- **Session Management**: Secure session handling with HTTP-only cookies
- **Role-Based Access Control**: Flexible permission system

### 📄 HTML Extraction Service
- **Complaint Data Extraction**: Extract structured data from HTML complaint files
- **Multiple Extraction Types**: Support for banking/UPI and card transactions
- **Layer-Based Processing**: Configurable transaction layer extraction (0-7)
- **Fraud Type Detection**: Specialized processing for different fraud types
- **Data Validation**: Comprehensive input validation and sanitization

### 🛡️ Security Features
- **Client Validation**: Desktop application-specific access control
- **Rate Limiting**: Protection against abuse (ready for Redis integration)
- **CORS Configuration**: Secure cross-origin resource sharing
- **Request Logging**: Comprehensive audit trail
- **Input Sanitization**: XSS and injection attack prevention

## Architecture

```
Desktop-NCRP-Matrix/backend/
├── app/
│   ├── api/
│   │   ├── dependencies.py      # Authentication & authorization
│   │   └── routes/
│   │       ├── auth.py          # Authentication endpoints
│   │       └── extraction.py    # HTML extraction endpoints
│   ├── core/
│   │   ├── config.py           # Application configuration
│   │   ├── database.py         # MongoDB connection
│   │   └── security.py         # Security utilities
│   ├── models/
│   │   ├── user.py             # User data models
│   │   └── token.py            # Token models
│   ├── services/
│   │   ├── auth_service.py     # Authentication business logic
│   │   └── extraction_service.py # HTML extraction logic
│   └── main.py                 # FastAPI application
├── requirements.txt            # Python dependencies
├── .env.example               # Environment configuration template
└── README.md                  # This file
```

## Quick Start

### 1. Environment Setup

```bash
# Clone the repository (if not already done)
cd Desktop-NCRP-Matrix/backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# Important: Change SECRET_KEY in production!
```

### 3. Database Setup

Ensure MongoDB is running locally or update `MONGODB_URL` in `.env`:

```bash
# Default MongoDB connection
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=ncrp_matrix_desktop
```

### 4. Run the Application

```bash
# Development mode
python -m app.main

# Or using uvicorn directly
uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload
```

The API will be available at:
- **API Base**: http://127.0.0.1:8000
- **Documentation**: http://127.0.0.1:8000/api/v1/docs (development only)
- **Health Check**: http://127.0.0.1:8000/health

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/verify` - Verify token validity

### HTML Extraction
- `POST /api/v1/extraction/parse-html` - Extract data from HTML file
- `GET /api/v1/extraction/health` - Extraction service health check
- `GET /api/v1/extraction/supported-types` - Get supported extraction types

## Security Configuration

### Production Deployment

1. **Environment Variables**:
   ```bash
   ENVIRONMENT=production
   SECRET_KEY=your-super-secure-secret-key-here
   SECURE_COOKIES=true
   DEBUG=false
   ```

2. **HTTPS Setup**: Always use HTTPS in production
3. **Database Security**: Use MongoDB authentication and SSL
4. **Rate Limiting**: Implement Redis-based rate limiting
5. **Monitoring**: Set up logging and monitoring

### Desktop Application Integration

The backend validates requests from the desktop application using:
- `X-Client-Type: desktop-app` header
- `User-Agent` containing "NCRP-Matrix-Desktop"

## Development

### Code Structure
- **Models**: Pydantic models for data validation
- **Services**: Business logic layer
- **API Routes**: HTTP endpoint handlers
- **Dependencies**: Reusable dependency injection
- **Core**: Configuration and utilities

### Adding New Features
1. Define models in `app/models/`
2. Implement business logic in `app/services/`
3. Create API routes in `app/api/routes/`
4. Add dependencies if needed in `app/api/dependencies.py`

### Testing
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests (when implemented)
pytest
```

## Monitoring and Logging

The application uses structured logging with:
- Request/response logging
- Error tracking with stack traces
- Performance monitoring hooks
- Security event logging

Logs are output in JSON format for easy parsing by log aggregation systems.

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure MongoDB is running
   - Check `MONGODB_URL` in `.env`
   - Verify network connectivity

2. **Authentication Errors**
   - Check `SECRET_KEY` configuration
   - Verify token expiration settings
   - Ensure client headers are correct

3. **File Upload Issues**
   - Check `MAX_FILE_SIZE` setting
   - Verify file type is in `ALLOWED_FILE_TYPES`
   - Ensure sufficient disk space for temp files

### Debug Mode

Enable debug mode for detailed error messages:
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Contributing

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include logging for important operations
4. Update documentation for new features
5. Follow security best practices

## License

This project is part of the Desktop NCRP Matrix application.
