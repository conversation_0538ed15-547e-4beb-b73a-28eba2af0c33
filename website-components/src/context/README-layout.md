# Layout Context System

This system allows components to declare their own layout preferences without having to manually pass props to the `TailwindLayout` component.

## Features

- Declarative layout configuration at the component level
- Automatic reset of layout when navigating between routes
- Support for multiple layout options (fullWidth, hideSidebar, customPadding, etc.)
- Compatible with React Router v6 and SSR
- Two implementation approaches: HOC and Hook

## Usage

### Setup

The layout system is already set up in the application. The `LayoutProvider` is wrapped around the routes in `App.tsx`, and the `TailwindLayout` component consumes the layout context.

### Option 1: Using the Higher-Order Component (HOC)

The HOC approach is useful when you want to declare layout preferences at the component level without modifying the component's internal code.

```tsx
import { withLayout } from '../context/LayoutContext';

// Your component
const YourComponent: React.FC = () => {
  // Component code...
};

// Apply layout configuration using the HOC
const EnhancedComponent = withLayout(YourComponent, {
  fullWidth: true,
  customPadding: '0'
});

export default EnhancedComponent;
```

### Option 2: Using the Hook

The hook approach is useful when you want to set layout preferences inside your component, potentially based on component state or props.

```tsx
import { useLayout } from '../context/LayoutContext';

const YourComponent: React.FC = () => {
  const { updateLayout } = useLayout();
  
  // Set layout configuration when component mounts
  React.useEffect(() => {
    updateLayout({
      fullWidth: true,
      customPadding: '1rem'
    });
  }, [updateLayout]);
  
  // Component code...
};

export default YourComponent;
```

### Option 3: Using the useLayoutEffect Hook

For a more concise approach, you can use the `useLayoutEffect` hook:

```tsx
import { useLayoutEffect } from '../context/LayoutContext';

const YourComponent: React.FC = () => {
  // Apply layout configuration using the hook
  useLayoutEffect({
    fullWidth: true,
    customPadding: '1rem'
  });
  
  // Component code...
};

export default YourComponent;
```

## Available Layout Options

The layout context supports the following options:

| Option | Type | Description |
|--------|------|-------------|
| `fullWidth` | boolean | When true, removes left margin and sidebar spacing |
| `hideSidebar` | boolean | When true, hides the sidebar completely |
| `hideHeader` | boolean | When true, hides the header (if implemented) |
| `customPadding` | string | Custom padding value for the main content area |
| `customBackground` | string | Custom background for the main content area |

## How It Works

1. The `LayoutProvider` maintains the current layout configuration state
2. When a component sets layout preferences, they are stored in the context
3. The `TailwindLayout` component consumes these preferences and adjusts its layout accordingly
4. When navigating to a different route, the layout is automatically reset to defaults

## Example: ComplaintDetail Page

The `ComplaintDetail` page uses the HOC approach to set its layout preferences:

```tsx
// Apply the layout configuration using the HOC
const ComplaintDetail = withLayout(ComplaintDetailComponent, {
  fullWidth: true,
  customPadding: '0'
});

export default ComplaintDetail;
```

This makes the complaint detail page use the full width of the screen without any padding, which is ideal for displaying data tables and other content that benefits from maximum space.

## SSR Compatibility

The layout system is designed to be compatible with server-side rendering. The `LayoutProvider` uses the `useLocation` hook from React Router v6, which is safe to use in SSR environments.

## Adding New Layout Options

To add new layout options:

1. Update the `LayoutConfig` interface in `LayoutContext.tsx`
2. Add the new option to the `defaultLayoutConfig` object
3. Update the `TailwindLayout` component to use the new option
