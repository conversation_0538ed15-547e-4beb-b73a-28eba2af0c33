import { useAuth } from './AuthContext';

// This is a compatibility hook that redirects to the unified AuthContext
// It allows existing components to continue using useUser() without changes
export const useUser = () => {
  const auth = useAuth();

  return {
    userInfo: auth.user,
    setUserInfo: () => {}, // No-op function since we don't allow direct setting anymore
    fetchUserInfo: auth.refreshUserData,
    isLoading: auth.loading
  };
};
