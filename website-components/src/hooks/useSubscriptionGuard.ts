import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useAlert } from '../context/TailwindAlertContext';

/**
 * Hook to guard navigation when subscription is expired
 * Prevents users from navigating to other routes when subscription is expired
 */
export const useSubscriptionGuard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { showError } = useAlert();

  useEffect(() => {
    if (!user) return;

    const checkSubscriptionAndBlockNavigation = () => {
      const now = new Date();
      const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;
      const isSubscriptionExpired = !Boolean(user.paid) || (subscriptionExpires && subscriptionExpires < now);

      // If subscription is expired and user tries to navigate away from current page
      if (isSubscriptionExpired) {
        // Allow staying on current page but show error for navigation attempts
        const currentPath = location.pathname;

        // Block navigation to other routes
        const handlePopState = (event: PopStateEvent) => {
          event.preventDefault();
          showError('Your subscription has expired. Please renew to continue using the application.');
          // Force back to current page
          window.history.pushState(null, '', currentPath);
        };

        // Add event listener for browser back/forward buttons
        window.addEventListener('popstate', handlePopState);

        // Cleanup function
        return () => {
          window.removeEventListener('popstate', handlePopState);
        };
      }
    };

    return checkSubscriptionAndBlockNavigation();
  }, [user, location.pathname, navigate, showError]);

  /**
   * Check if navigation should be blocked
   */
  const isNavigationBlocked = (): boolean => {
    if (!user) return false;

    const now = new Date();
    const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;
    return !Boolean(user.paid) || Boolean(subscriptionExpires && subscriptionExpires < now);
  };

  /**
   * Attempt to navigate to a route, but block if subscription is expired
   */
  const guardedNavigate = (to: string, options?: any) => {
    if (isNavigationBlocked()) {
      showError('Your subscription has expired. Please renew to continue using the application.');
      return false;
    }

    navigate(to, options);
    return true;
  };

  return {
    isNavigationBlocked,
    guardedNavigate
  };
};
