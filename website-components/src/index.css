@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Base styles */
@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  code, pre, kbd, samp {
    font-family: 'JetBrains Mono', 'Roboto Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  }

  html, body, #root {
    width: 100%;
    height: 100%;
    overflow: hidden;
    max-width: 100vw;
  }

  /* Responsive font sizes with better scaling */
  html {
    font-size: 16px;
  }

  /* Large screens and 4K displays */
  @media (min-width: 1440px) {
    html {
      font-size: 18px;
    }
  }

  @media (min-width: 1920px) {
    html {
      font-size: 20px;
    }
  }

  @media (min-width: 2560px) {
    html {
      font-size: 22px;
    }
  }

  /* Tablet and smaller laptops */
  @media (max-width: 1024px) {
    html {
      font-size: 15px;
    }
  }

  @media (max-width: 768px) {
    html {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    html {
      font-size: 12px;
    }
  }

  @media (max-width: 320px) {
    html {
      font-size: 11px;
    }
  }

  /* Theme-based styling */
  body {
    background-color: var(--theme-bg-primary);
    color: var(--theme-text);
    position: relative;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes scanLine {
    0% {
      transform: translateY(-100%);
    }
    100% {
      transform: translateY(100%);
    }
  }

  @keyframes glowPulse {
    0%, 100% {
      opacity: 0.3;
      background: radial-gradient(circle at 50% 50%, rgba(0, 170, 255, 0.05) 0%, rgba(0, 0, 0, 0) 70%);
    }
    50% {
      opacity: 0.6;
      background: radial-gradient(circle at 50% 50%, rgba(0, 170, 255, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
    }
  }

  /* Text colors based on theme - more consistent approach */
  .text-gray-700 {
    color: var(--theme-text) !important;
    opacity: 0.9;
  }

  .text-gray-600 {
    color: var(--theme-text) !important;
    opacity: 0.8;
  }

  .text-gray-500 {
    color: var(--theme-text) !important;
    opacity: 0.7;
  }

  .text-gray-400 {
    color: var(--theme-text) !important;
    opacity: 0.6;
  }

  /* Table styles - subtle hover animation */
  .hoverable-table tr {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hoverable-table tr:hover {
    transform: translateY(-1px) scale(1.005);
    box-shadow: 0 2px 8px var(--theme-glow);
  }

  .striped-table tr:nth-child(even) {
    background-color: var(--theme-glow);
    opacity: 0.3;
  }
}

/* Fix for mobile viewport height issues and better responsive design */
.vh-100 {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* Improved responsive design for square/portrait screens */
@media (max-aspect-ratio: 1/1) {
  /* Square and portrait screens */
  .responsive-container {
    padding-left: clamp(0.25rem, 1vw, 1rem);
    padding-right: clamp(0.25rem, 1vw, 1rem);
  }

  .responsive-card {
    padding: clamp(0.75rem, 2vw, 1.5rem);
    margin: clamp(0.25rem, 1vw, 0.75rem);
  }

  .responsive-text-base {
    font-size: clamp(0.8rem, 2.2vw, 1rem);
  }

  .responsive-text-lg {
    font-size: clamp(0.9rem, 2.8vw, 1.125rem);
  }

  .responsive-text-xl {
    font-size: clamp(1rem, 3.5vw, 1.25rem);
  }
}

@media (min-aspect-ratio: 2/1) {
  /* Wide screens */
  .responsive-container {
    max-width: 85vw;
    padding-left: clamp(1rem, 3vw, 3rem);
    padding-right: clamp(1rem, 3vw, 3rem);
  }

  .responsive-card {
    max-width: 70vw;
    margin: clamp(0.75rem, 2vw, 1.5rem) auto;
  }
}

/* Enhanced responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 100vw;
  padding-left: clamp(0.5rem, 2vw, 2rem);
  padding-right: clamp(0.5rem, 2vw, 2rem);
}

/* Responsive text scaling */
.responsive-text-sm {
  font-size: clamp(0.75rem, 1.5vw, 0.875rem);
}

.responsive-text-base {
  font-size: clamp(0.875rem, 2vw, 1rem);
}

.responsive-text-lg {
  font-size: clamp(1rem, 2.5vw, 1.125rem);
}

.responsive-text-xl {
  font-size: clamp(1.125rem, 3vw, 1.25rem);
}

.responsive-text-2xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
}

/* Better spacing for different screen sizes */
.responsive-spacing {
  margin: clamp(0.5rem, 2vw, 1rem);
  padding: clamp(0.5rem, 2vw, 1rem);
}

/* Prevent components from being too large on big screens */
.max-width-container {
  max-width: min(90vw, 1200px);
  margin: 0 auto;
}

.max-width-form {
  max-width: min(100%, 400px);
  margin: 0 auto;
}

.max-width-card {
  max-width: min(100%, 600px);
  margin: 0 auto;
}

.max-width-modal {
  max-width: min(95vw, 800px);
  margin: 0 auto;
}

/* Responsive grid that adapts to screen size */
.responsive-grid {
  display: grid;
  gap: clamp(0.5rem, 2vw, 1.5rem);
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
}

/* Responsive button sizing */
.responsive-button {
  padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
  font-size: clamp(0.875rem, 2vw, 1rem);
  min-height: 44px; /* Minimum touch target size */
}

/* Responsive input sizing */
.responsive-input {
  padding: clamp(0.5rem, 1.5vw, 0.75rem);
  font-size: clamp(0.875rem, 2vw, 1rem);
  min-height: 44px; /* Minimum touch target size */
}

/* Responsive card sizing */
.responsive-card {
  padding: clamp(1rem, 3vw, 2rem);
  margin: clamp(0.5rem, 2vw, 1rem);
  border-radius: clamp(0.5rem, 1vw, 1rem);
}

/* Responsive table styling */
.responsive-table {
  font-size: clamp(0.75rem, 1.5vw, 0.875rem);
  overflow-x: auto;
}

.responsive-table th,
.responsive-table td {
  padding: clamp(0.5rem, 1vw, 0.75rem);
  white-space: nowrap;
}

/* Responsive modal sizing */
.responsive-modal {
  width: clamp(300px, 90vw, 800px);
  max-height: clamp(400px, 90vh, 600px);
  margin: clamp(1rem, 5vh, 3rem) auto;
}

/* Responsive sidebar */
.responsive-sidebar {
  width: clamp(200px, 20vw, 280px);
}

/* Responsive header */
.responsive-header {
  height: clamp(60px, 8vh, 80px);
  padding: clamp(0.5rem, 2vw, 1rem);
}

/* Responsive navigation */
.responsive-nav {
  gap: clamp(0.5rem, 2vw, 1rem);
}

.responsive-nav-item {
  padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(0.75rem, 2vw, 1rem);
  font-size: clamp(0.875rem, 1.8vw, 1rem);
}

/* React Flow container fix */
.react-flow-wrapper {
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.react-flow {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7 !important; /* Muted white background instead of pure white */
}

/* Dark mode graph background - improved cyberpunk styling */
.theme-darkmode .react-flow {
  background-color: #0a0a0f !important;
  background-image:
    linear-gradient(rgba(0, 170, 255, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 170, 255, 0.08) 1px, transparent 1px),
    radial-gradient(circle at 25% 25%, rgba(0, 170, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
  background-size: 30px 30px, 30px 30px, 200px 200px, 200px 200px;
}

/* Ensure graph node colors are preserved during processing */
.react-flow__node {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

/* Preserve node colors in dark mode */
.theme-darkmode .bg-blue-900\/30 { background-color: rgba(30, 58, 138, 0.3) !important; }
.theme-darkmode .border-blue-700 { border-color: #1d4ed8 !important; }
.theme-darkmode .text-blue-400 { color: #60a5fa !important; }

.theme-darkmode .bg-red-900\/30 { background-color: rgba(127, 29, 29, 0.3) !important; }
.theme-darkmode .border-red-700 { border-color: #b91c1c !important; }
.theme-darkmode .text-red-400 { color: #f87171 !important; }

.theme-darkmode .bg-purple-900\/30 { background-color: rgba(88, 28, 135, 0.3) !important; }
.theme-darkmode .border-purple-700 { border-color: #7e22ce !important; }
.theme-darkmode .text-purple-400 { color: #c084fc !important; }

.theme-darkmode .bg-green-900\/30 { background-color: rgba(20, 83, 45, 0.3) !important; }
.theme-darkmode .border-green-700 { border-color: #15803d !important; }
.theme-darkmode .text-green-400 { color: #4ade80 !important; }

.theme-darkmode .bg-amber-900\/30 { background-color: rgba(120, 53, 15, 0.3) !important; }
.theme-darkmode .border-amber-700 { border-color: #b45309 !important; }
.theme-darkmode .text-amber-400 { color: #fbbf24 !important; }

.theme-darkmode .bg-indigo-900\/30 { background-color: rgba(49, 46, 129, 0.3) !important; }
.theme-darkmode .border-indigo-700 { border-color: #4338ca !important; }
.theme-darkmode .text-indigo-400 { color: #818cf8 !important; }

/* Excel-like grid styles */
.excel-grid table {
  border-collapse: collapse !important;
  width: 100% !important;
  border-spacing: 0 !important;
}

.excel-grid table th,
.excel-grid table td {
  border: 1px solid var(--theme-border) !important;
  box-sizing: border-box !important;
}

.excel-grid table th {
  font-weight: 600 !important;
  height: 40px !important; /* Increased header height */
  padding: 10px 8px !important; /* Increased padding */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  letter-spacing: 0.01em !important;
}

/* Force table borders to be visible */
.excel-grid table,
.excel-grid table th,
.excel-grid table td {
  border-style: solid !important;
  border-width: 1px !important;
  border-color: var(--theme-border) !important;
}

/* Override any conflicting styles */
.excel-grid table td > div,
.excel-grid table th > div {
  border: none !important;
}

/* CSV Preview Table specific styles */
.csv-preview-table table {
  border-collapse: collapse !important;
}

.csv-preview-table table th,
.csv-preview-table table td {
  border: 0.5px solid var(--theme-border) !important;
  padding: 2px !important;
}

/* Direct CSS for ReactTableCSVPreview */
#root .ReactTableCSVPreview table,
#root .ReactTableCSVPreview th,
#root .ReactTableCSVPreview td {
  border: 0.5px solid var(--theme-border) !important;
}

#root .ReactTableCSVPreview table {
  table-layout: fixed !important;
  width: 100% !important;
  display: table !important;
}

#root .ReactTableCSVPreview table tr {
  display: table-row !important;
}

#root .ReactTableCSVPreview table th,
#root .ReactTableCSVPreview table td {
  display: table-cell !important;
}

#root .ReactTableCSVPreview table th {
  height: 40px !important; /* Increased header height */
  padding: 8px 4px !important; /* Increased padding */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
}

/* Force table styling for complaint detail page */
.complaint-details-container table,
.complaint-details-container th,
.complaint-details-container td {
  border: 0.5px solid var(--theme-border) !important;
  padding: 4px !important;
}

.complaint-details-container th {
  height: 40px !important; /* Increased header height */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
  padding: 8px 4px !important;
}

/* Direct styling for excel-table class */
.excel-table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  background-color: transparent !important;
  table-layout: fixed !important;
  width: 100% !important;
  display: table !important;
}

.excel-table tr {
  display: table-row !important;
}

.excel-table th,
.excel-table td {
  display: table-cell !important;
}

/* Cell styling */
.excel-table th {
  border: 0.5px solid var(--theme-border) !important;
  padding: 8px 4px !important;
  height: 40px !important; /* Increased header height */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
}

.excel-table td {
  background-color: var(--theme-bg-card) !important;
  border: 0.5px solid var(--theme-border) !important;
  padding: 4px !important;
  color: var(--theme-text) !important;
  transition: none !important;
}

/* Row styling */
.excel-table tr {
  background-color: transparent !important;
}

/* Force table styling */
.excel-table,
.excel-table * {
  box-sizing: border-box !important;
}

/* Global table styling to ensure consistency */
table {
  table-layout: fixed !important;
  width: 100% !important;
  display: table !important;
  border-collapse: collapse !important;
}

table tr {
  display: table-row !important;
}

table th,
table td {
  display: table-cell !important;
}

table th {
  height: 40px !important;
  border: 1px solid var(--theme-border) !important;
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
  padding: 10px 8px !important;
  letter-spacing: 0.01em !important;
}

table td {
  border: 1px solid var(--theme-border) !important;
  color: var(--theme-text) !important;
  padding: 8px !important;
  transition: none !important;
}

/* Add subtle shadow to tables */
.excel-grid,
.ReactTableCSVPreview,
.complaint-details-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

/* Fix DashboardTable borders */
td[style*="border:"],
td[style*="border-color:"],
th[style*="border:"],
th[style*="border-color:"] {
  border: 1px solid var(--theme-border) !important;
  border-color: var(--theme-border) !important;
}

/* Add glow effect to action buttons */
button[aria-label="View complaint"],
button[aria-label="Edit complaint"] {
  box-shadow: 0 2px 6px var(--theme-glow) !important;
  transition: all 0.2s ease-in-out !important;
}

button[aria-label="View complaint"]:hover,
button[aria-label="Edit complaint"]:hover {
  box-shadow: 0 4px 12px var(--theme-glow) !important;
  transform: translateY(-1px) !important;
}

button[aria-label="Delete complaint"] {
  box-shadow: 0 2px 6px rgba(244, 67, 54, 0.2) !important;
  opacity: 0.5;
  transition: all 0.2s ease-in-out !important;
}

button[aria-label="Delete complaint"]:hover {
  opacity: 0.8;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3) !important;
}

/* Force all table cells to have the correct border color */
table td, table th,
td[style*="border"], th[style*="border"],
.excel-grid td, .excel-grid th,
.ReactTableCSVPreview td, .ReactTableCSVPreview th,
.complaint-details-container td, .complaint-details-container th {
  border: 1px solid var(--theme-border) !important;
}

/* Add subtle hover effect to table rows - animation instead of color change */
table tr {
  transition: transform 0.15s ease, box-shadow 0.15s ease;
}

table tr:hover {
  transform: translateY(-1px) scale(1.002);
  box-shadow: 0 1px 4px var(--theme-glow);
}

/* Theme colors with consistent black text */
:root {
  /* 🎨 1. Off-White */
  --theme-offwhite-bg-primary: #eae3e3;
  --theme-offwhite-bg-card: #f4eaea;
  --theme-offwhite-bg-lighter: #ffffffe5;
  --theme-offwhite-border: #E0E0E0;
  --theme-offwhite-accent: #d5d0d0;
  --theme-offwhite-glow: rgba(204, 204, 204, 0.3);
  --theme-offwhite-text: #333333;
  --theme-offwhite-text-muted: #666666;
  --theme-offwhite-button: #CCCCCC;
  --theme-offwhite-button-hover: #AAAAAA;
  --theme-offwhite-sidebar-darker: #a6a4a4;

  /* 🎨 2. Mint Green - More muted for eye protection */
  --theme-mint-bg-primary: #a8d9bc;
  --theme-mint-bg-card: #d8efe0;
  --theme-mint-bg-lighter: #e8f5ec;
  --theme-mint-border: #8abca0;
  --theme-mint-accent: #66a080;
  --theme-mint-glow: rgba(102, 160, 128, 0.2);
  --theme-mint-text: #333333;
  --theme-mint-text-muted: #4d5d52;
  --theme-mint-button: #66a080;
  --theme-mint-button-hover: #4d8066;
  --theme-mint-sidebar-darker: #66a080;

  /* 🎨 3. Light Blue - Even more muted for eye protection */
  --theme-lightblue-bg-primary: #c0d8e6;
  --theme-lightblue-bg-card: #d8e6f0;
  --theme-lightblue-bg-lighter: #e6eef5;
  --theme-lightblue-border: #a8c0d0;
  --theme-lightblue-accent: #7a9cb3;
  --theme-lightblue-glow: rgba(122, 156, 179, 0.15);
  --theme-lightblue-text: #333333;
  --theme-lightblue-text-muted: #4d5d66;
  --theme-lightblue-button: #7a9cb3;
  --theme-lightblue-button-hover: #6889a3;
  --theme-lightblue-sidebar-darker: #7a9cb3;

  /* 🎨 4. Orange - Warm and vibrant theme */
  --theme-orange-bg-primary: #fbe8cf;
  --theme-orange-bg-card: #fbe7ce;
  --theme-orange-bg-lighter: #f2e4d1;
  --theme-orange-border: #fed7aa;
  --theme-orange-accent: #fb923c;
  --theme-orange-glow: rgba(251, 146, 60, 0.2);
  --theme-orange-text: #333333;
  --theme-orange-text-muted: #7c2d12;
  --theme-orange-button: #ea580c;
  --theme-orange-button-hover: #c2410c;
  --theme-orange-sidebar-darker: #fed7aa;





  /* 🎨 8. Light Gray - Even more muted for eye protection */
  --theme-lightgray-bg-primary: #e0e0e0;
  --theme-lightgray-bg-card: #e8e8e8;
  --theme-lightgray-bg-lighter: #f2f2f2;
  --theme-lightgray-border: #c8c8c8;
  --theme-lightgray-accent: #a0a0a0;
  --theme-lightgray-glow: rgba(160, 160, 160, 0.15);
  --theme-lightgray-text: #333333;
  --theme-lightgray-text-muted: #595959;
  --theme-lightgray-button: #a0a0a0;
  --theme-lightgray-button-hover: #888888;
  --theme-lightgray-sidebar-darker: #a0a0a0;



  /* 🎨 6. Dark Cyberpunk - Improved dark theme with cyberpunk aesthetics */
  --theme-darkmode-bg-primary: #0a0a0f;
  --theme-darkmode-bg-card: rgba(10, 10, 15, 0.85);
  --theme-darkmode-bg-lighter: #12121a;
  --theme-darkmode-border: rgba(0, 170, 255, 0.25);
  --theme-darkmode-accent: #00aaff;
  --theme-darkmode-glow: rgba(0, 170, 255, 0.3);
  --theme-darkmode-text: #e8f4ff;
  --theme-darkmode-text-muted: #c2d9f0;
  --theme-darkmode-button: #00aaff;
  --theme-darkmode-button-hover: #0088cc;
  --theme-darkmode-sidebar-darker: #050508;
}

/* Theme color classes */
:root {
  /* Default theme (Light Blue) */
  --theme-bg-primary: var(--theme-lightblue-bg-primary);
  --theme-bg-card: var(--theme-lightblue-bg-card);
  --theme-bg-lighter: var(--theme-lightblue-bg-lighter);
  --theme-border: var(--theme-lightblue-border);
  --theme-accent: var(--theme-lightblue-accent);
  --theme-glow: var(--theme-lightblue-glow);
  --theme-text: var(--theme-lightblue-text);
  --theme-text-muted: var(--theme-lightblue-text-muted);
  --theme-button: var(--theme-lightblue-button);
  --theme-button-hover: var(--theme-lightblue-button-hover);

  /* Additional theme variables for components */
  --theme-sidebar-bg: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightblue-border);
  --theme-sidebar-text: var(--theme-lightblue-text);
  --theme-header-bg: var(--theme-lightblue-accent);
  --theme-input-bg: var(--theme-lightblue-bg-card);
  --theme-input-border: var(--theme-lightblue-border);
  --theme-input-text: var(--theme-lightblue-text);
  --theme-success-color: #4CAF50;
  --theme-warning-color: #FF9800;
  --theme-error-color: #F44336;
  --theme-info-color: #2196F3;
}



.theme-offwhite {
  --theme-bg-primary: var(--theme-offwhite-bg-primary);
  --theme-bg-card: var(--theme-offwhite-bg-card);
  --theme-bg-lighter: var(--theme-offwhite-bg-lighter);
  --theme-border: var(--theme-offwhite-border);
  --theme-accent: var(--theme-offwhite-accent);
  --theme-glow: var(--theme-offwhite-glow);
  --theme-text: var(--theme-offwhite-text);
  --theme-text-muted: var(--theme-offwhite-text-muted);
  --theme-button: var(--theme-offwhite-button);
  --theme-button-hover: var(--theme-offwhite-button-hover);
  --theme-sidebar-bg: var(--theme-offwhite-sidebar-darker);
  --theme-sidebar-darker: var(--theme-offwhite-sidebar-darker);
  --theme-sidebar-border: var(--theme-offwhite-border);
  --theme-sidebar-text: var(--theme-offwhite-text);
  --theme-header-bg: var(--theme-offwhite-accent);
  --theme-input-bg: var(--theme-offwhite-bg-card);
  --theme-input-border: var(--theme-offwhite-border);
  --theme-input-text: var(--theme-offwhite-text);
}

/* New theme classes */

.theme-mint {
  --theme-bg-primary: var(--theme-mint-bg-primary);
  --theme-bg-card: var(--theme-mint-bg-card);
  --theme-bg-lighter: var(--theme-mint-bg-lighter);
  --theme-border: var(--theme-mint-border);
  --theme-accent: var(--theme-mint-accent);
  --theme-glow: var(--theme-mint-glow);
  --theme-text: var(--theme-mint-text);
  --theme-text-muted: var(--theme-mint-text-muted);
  --theme-button: var(--theme-mint-button);
  --theme-button-hover: var(--theme-mint-button-hover);
  --theme-sidebar-bg: var(--theme-mint-sidebar-darker);
  --theme-sidebar-darker: var(--theme-mint-sidebar-darker);
  --theme-sidebar-border: var(--theme-mint-border);
  --theme-sidebar-text: var(--theme-mint-text);
  --theme-header-bg: var(--theme-mint-accent);
  --theme-input-bg: var(--theme-mint-bg-card);
  --theme-input-border: var(--theme-mint-border);
  --theme-input-text: var(--theme-mint-text);
}



.theme-lightblue {
  --theme-bg-primary: var(--theme-lightblue-bg-primary);
  --theme-bg-card: var(--theme-lightblue-bg-card);
  --theme-bg-lighter: var(--theme-lightblue-bg-lighter);
  --theme-border: var(--theme-lightblue-border);
  --theme-accent: var(--theme-lightblue-accent);
  --theme-glow: var(--theme-lightblue-glow);
  --theme-text: var(--theme-lightblue-text);
  --theme-text-muted: var(--theme-lightblue-text-muted);
  --theme-button: var(--theme-lightblue-button);
  --theme-button-hover: var(--theme-lightblue-button-hover);
  --theme-sidebar-bg: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightblue-border);
  --theme-sidebar-text: var(--theme-lightblue-text);
  --theme-header-bg: var(--theme-lightblue-accent);
  --theme-input-bg: var(--theme-lightblue-bg-card);
  --theme-input-border: var(--theme-lightblue-border);
  --theme-input-text: var(--theme-lightblue-text);
}



.theme-lightgray {
  --theme-bg-primary: var(--theme-lightgray-bg-primary);
  --theme-bg-card: var(--theme-lightgray-bg-card);
  --theme-bg-lighter: var(--theme-lightgray-bg-lighter);
  --theme-border: var(--theme-lightgray-border);
  --theme-accent: var(--theme-lightgray-accent);
  --theme-glow: var(--theme-lightgray-glow);
  --theme-text: var(--theme-lightgray-text);
  --theme-text-muted: var(--theme-lightgray-text-muted);
  --theme-button: var(--theme-lightgray-button);
  --theme-button-hover: var(--theme-lightgray-button-hover);
  --theme-sidebar-bg: var(--theme-lightgray-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightgray-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightgray-border);
  --theme-sidebar-text: var(--theme-lightgray-text);
  --theme-header-bg: var(--theme-lightgray-accent);
  --theme-input-bg: var(--theme-lightgray-bg-card);
  --theme-input-border: var(--theme-lightgray-border);
  --theme-input-text: var(--theme-lightgray-text);
}



.theme-orange {
  --theme-bg-primary: var(--theme-orange-bg-primary);
  --theme-bg-card: var(--theme-orange-bg-card);
  --theme-bg-lighter: var(--theme-orange-bg-lighter);
  --theme-border: var(--theme-orange-border);
  --theme-accent: var(--theme-orange-accent);
  --theme-glow: var(--theme-orange-glow);
  --theme-text: var(--theme-orange-text);
  --theme-text-muted: var(--theme-orange-text-muted);
  --theme-button: var(--theme-orange-button);
  --theme-button-hover: var(--theme-orange-button-hover);
  --theme-sidebar-bg: var(--theme-orange-sidebar-darker);
  --theme-sidebar-darker: var(--theme-orange-sidebar-darker);
  --theme-sidebar-border: var(--theme-orange-border);
  --theme-sidebar-text: var(--theme-orange-text);
  --theme-header-bg: var(--theme-orange-accent);
  --theme-input-bg: var(--theme-orange-bg-card);
  --theme-input-border: var(--theme-orange-border);
  --theme-input-text: var(--theme-orange-text);
}





/* Dark Cyberpunk Theme - Enhanced with cyberpunk aesthetics (only applies to dark mode) */
.theme-darkmode {
  --theme-bg-primary: var(--theme-darkmode-bg-primary);
  --theme-bg-card: var(--theme-darkmode-bg-card);
  --theme-bg-lighter: var(--theme-darkmode-bg-lighter);
  --theme-border: var(--theme-darkmode-border);
  --theme-accent: var(--theme-darkmode-accent);
  --theme-glow: var(--theme-darkmode-glow);
  --theme-text: var(--theme-darkmode-text);
  --theme-text-muted: var(--theme-darkmode-text-muted);
  --theme-button: var(--theme-darkmode-button);
  --theme-button-hover: var(--theme-darkmode-button-hover);
  --theme-sidebar-bg: var(--theme-darkmode-sidebar-darker);
  --theme-sidebar-darker: var(--theme-darkmode-sidebar-darker);
  --theme-sidebar-border: var(--theme-darkmode-border);
  --theme-sidebar-text: var(--theme-darkmode-text);
  --theme-header-bg: var(--theme-darkmode-accent);
  --theme-input-bg: var(--theme-darkmode-bg-card);
  --theme-input-border: var(--theme-darkmode-border);
  --theme-input-text: var(--theme-darkmode-text);
}

/* Cyberpunk-inspired particle effects for dark mode */
.theme-darkmode body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(1px 1px at 25px 5px, rgba(0, 170, 255, 0.6), rgba(0, 170, 255, 0)),
    radial-gradient(0.8px 0.8px at 50px 25px, rgba(138, 43, 226, 0.5), rgba(138, 43, 226, 0)),
    radial-gradient(1.2px 1.2px at 125px 20px, rgba(0, 170, 255, 0.7), rgba(0, 170, 255, 0)),
    radial-gradient(0.6px 0.6px at 50px 75px, rgba(138, 43, 226, 0.4), rgba(138, 43, 226, 0)),
    radial-gradient(1.5px 1.5px at 175px 125px, rgba(0, 170, 255, 0.8), rgba(0, 170, 255, 0)),
    radial-gradient(1.5px 1.5px at 20px 150px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(0.8px 0.8px at 200px 220px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(1px 1px at 120px 250px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(0.5px 0.5px at 200px 280px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
  background-repeat: repeat;
  background-size: 400px 400px;
  opacity: 0.08;
  z-index: -1;
  animation: starsMovement 180s linear infinite;
}

@keyframes starsMovement {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 400px 400px;
  }
}

/* Add second layer of stars for parallax effect */
.theme-darkmode body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(0.7px 0.7px at 150px 15px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(0.7px 0.7px at 90px 40px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(0.7px 0.7px at 230px 85px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(1px 1px at 110px 120px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(1.2px 1.2px at 10px 170px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
  background-repeat: repeat;
  background-size: 400px 400px;
  opacity: 0.05;
  z-index: -1;
  animation: starsMovement2 240s linear infinite;
}

@keyframes starsMovement2 {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -400px -400px;
  }
}

/* Cyberpunk-inspired glassmorphic effect for dark mode */
.theme-darkmode .card,
.theme-darkmode .bg-white,
.theme-darkmode [class*="bg-card"] {
  background: rgba(10, 10, 15, 0.85) !important;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 170, 255, 0.25);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 16px rgba(0, 170, 255, 0.1);
  transition: all 0.3s ease;
}

.theme-darkmode .card:hover,
.theme-darkmode .bg-white:hover,
.theme-darkmode [class*="bg-card"]:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.5), 0 0 24px rgba(0, 170, 255, 0.2);
  border: 1px solid rgba(0, 170, 255, 0.4);
  transform: translateY(-2px);
}

/* Improved table styling in dark mode - cyberpunk inspired */
.theme-darkmode table,
.theme-darkmode .table {
  background-color: rgba(10, 10, 15, 0.6) !important;
  border-color: rgba(0, 170, 255, 0.2) !important;
}

.theme-darkmode th {
  background-color: rgba(10, 10, 15, 0.9) !important;
  color: var(--theme-darkmode-text) !important;
  border-color: rgba(0, 170, 255, 0.3) !important;
}

.theme-darkmode td {
  border-color: rgba(0, 170, 255, 0.15) !important;
}

/* Improved header styling in dark mode - cyberpunk inspired */
.theme-darkmode header,
.theme-darkmode .header,
.theme-darkmode [class*="header"] {
  background-color: rgba(10, 10, 15, 0.9) !important;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(0, 170, 255, 0.3) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* Apply theme colors to elements */
body {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text);
}

/* Card styles */
.card, .bg-white, [class*="bg-white"] {
  background-color: var(--theme-bg-card) !important;
  border-color: var(--theme-border) !important;
}

/* Sidebar styles */
aside, .sidebar, [class*="sidebar"] {
  background-color: var(--theme-sidebar-bg) !important;
  border-color: var(--theme-sidebar-border) !important;
  color: var(--theme-sidebar-text) !important;
}

/* Header styles - improved consistency */
header, .header, [class*="header"] {
  background-color: var(--theme-header-bg) !important;
  border-color: var(--theme-border) !important;
  color: var(--theme-text) !important;
}

/* Fix for header text colors */
header h1, header h2, header h3, header h4, header h5, header h6,
.header h1, .header h2, .header h3, .header h4, .header h5, .header h6,
[class*="header"] h1, [class*="header"] h2, [class*="header"] h3,
[class*="header"] h4, [class*="header"] h5, [class*="header"] h6 {
  color: var(--theme-text) !important;
}

/* Button styles */
.btn-primary, button.primary, [class*="btn-primary"] {
  background-color: var(--theme-button) !important;
  color: white !important;
  border-color: var(--theme-button) !important;
}

.btn-primary:hover, button.primary:hover, [class*="btn-primary"]:hover {
  background-color: var(--theme-button-hover) !important;
  border-color: var(--theme-button-hover) !important;
}

/* Secondary button styles */
.btn-secondary, button.secondary, [class*="btn-secondary"] {
  background-color: transparent !important;
  color: var(--theme-text) !important;
  border-color: var(--theme-border) !important;
}

.btn-secondary:hover, button.secondary:hover, [class*="btn-secondary"]:hover {
  background-color: var(--theme-bg-primary) !important;
  border-color: var(--theme-accent) !important;
}

/* Input styles */
input, textarea, select, .input, [class*="input"] {
  background-color: var(--theme-input-bg) !important;
  border-color: var(--theme-input-border) !important;
  color: var(--theme-input-text) !important;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--theme-accent) !important;
  box-shadow: 0 0 0 2px var(--theme-glow) !important;
}

/* Link styles */
a, .link, [class*="link"] {
  color: var(--theme-accent) !important;
}

a:hover, .link:hover, [class*="link"]:hover {
  color: var(--theme-button-hover) !important;
}

/* Text colors - consistent styling */
.text-primary, [class*="text-primary"] {
  color: var(--theme-text) !important;
}

.text-secondary, [class*="text-secondary"] {
  color: var(--theme-text) !important;
  opacity: 0.8;
}

/* Fix for dark mode text colors */
.theme-darkmode .text-gray-700,
.theme-darkmode .text-gray-600,
.theme-darkmode .text-gray-500,
.theme-darkmode .text-gray-400,
.theme-darkmode .text-primary,
.theme-darkmode .text-secondary,
.theme-darkmode [class*="text-gray-"],
.theme-darkmode [class*="text-primary"],
.theme-darkmode [class*="text-secondary"] {
  color: var(--theme-darkmode-text) !important;
}

/* Border colors */
.border, [class*="border"] {
  border-color: var(--theme-border) !important;
}

/* Background colors */
.bg-primary, [class*="bg-primary"] {
  background-color: var(--theme-bg-primary) !important;
}

.bg-secondary, [class*="bg-secondary"] {
  background-color: var(--theme-bg-card) !important;
}

/* Remove any Tailwind dark mode classes */
[class*="dark:bg-"], [class*="dark:text-"], [class*="dark:border-"] {
  display: inherit !important; /* Override with neutral property */
}

/* Apply theme colors to tables and cells */
table td, table th,
td[style*="border"], th[style*="border"],
.excel-grid td, .excel-grid th,
.ReactTableCSVPreview td, .ReactTableCSVPreview th,
.complaint-details-container td, .complaint-details-container th {
  border: 1px solid var(--theme-border) !important;
  color: var(--theme-text) !important;
}

/* Fix DashboardTable cell borders */
td[style*="border: isDark"],
th[style*="border: isDark"] {
  border: 1px solid var(--theme-border) !important;
}

/* Standardized table header background colors */
table th,
th[style*="backgroundColor"],
thead th,
.excel-grid th,
.ReactTableCSVPreview th,
.complaint-details-container th {
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
  padding: 8px 4px !important;
  height: 40px !important;
}

/* Complaint details page specific fixes */
.complaint-details-container {
  width: 100% !important;
  box-sizing: border-box !important;
}

.complaint-details-content {
  width: 100% !important;
  box-sizing: border-box !important;
}

.complaint-details-table {
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Page transition effects - optimized to prevent blinking */
.page-transition {
  position: relative;
  width: 100%;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  opacity: 1;
  transform: translateY(0);
}

.page-transition.fadeIn {
  opacity: 1;
  transform: translateY(0);
}

.page-transition.fadeOut {
  opacity: 0;
  transform: translateY(-10px);
}

/* Disable animations for table components to prevent blinking */
.excel-grid,
.ReactTableCSVPreview,
.complaint-details-container {
  animation: none !important;
  transition: none !important;
}

/* Cyberpunk-inspired utility classes */
.cyber-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  transform: translateY(0);
}

.cyber-card:hover {
  transform: translateY(-5px);
}

/* Light mode cyber card */
.cyber-card.light {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 170, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.cyber-card.light:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 170, 255, 0.4);
}

/* Dark mode cyber card */
.dark .cyber-card {
  background: rgba(16, 16, 30, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 170, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .cyber-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 170, 255, 0.3);
  border-color: rgba(0, 170, 255, 0.5);
}

/* Cyber grid background - completely removed */

/* Cyber dots background - completely removed */

/* Cyber glow effect */
.cyber-glow {
  position: relative;
}

.cyber-glow::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
  z-index: -1;
}

.cyber-glow:hover::after {
  opacity: 1;
}

/* Cyber text with gradient */
.cyber-text-gradient {
  background: linear-gradient(135deg, #00DDFF 0%, #0088FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-purple {
  background: linear-gradient(135deg, #FF5CFF 0%, #CC00CC 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-green {
  background: linear-gradient(135deg, #66FFBB 0%, #00AA44 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Sidebar toggle button styles */
.sidebar-toggle-btn {
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: translateX(0);
  position: relative;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

.sidebar-toggle-btn::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 20px;
  background: transparent;
  z-index: -1;
  transition: all 0.3s ease-in-out;
  opacity: 0;
}

aside:hover .sidebar-toggle-btn {
  opacity: 0.9;
  transform: translateX(0);
}

.sidebar-toggle-btn:hover {
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 0 12px var(--theme-glow) !important;
}

.sidebar-toggle-btn:hover::after {
  background: radial-gradient(circle, var(--theme-glow) 0%, transparent 70%);
  opacity: 0.6;
}

/* Make the toggle button always visible on mobile */
@media (max-width: 768px) {
  .sidebar-toggle-btn {
    opacity: 0.9;
  }
}

:root {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px; /* Base font size */
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.011em; /* Slight letter spacing adjustment for Inter */
}

a {
  font-weight: 500;
  color: var(--theme-accent); /* Use theme accent color */
  text-decoration: inherit;
  transition: all 0.2s ease-in-out;
  position: relative;
}
a:hover {
  color: var(--theme-button-hover); /* Use theme hover color */
}

/* Special links with glow effect */
a.glow-link {
  position: relative;
  color: var(--theme-accent);
  text-decoration: none;
  padding: 0 2px;
}
a.glow-link:hover {
  color: var(--theme-button-hover);
  text-shadow: 0 0 8px var(--theme-glow);
}
a.glow-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--theme-accent);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
  box-shadow: 0 0 8px var(--theme-glow);
}
a.glow-link:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--theme-text) !important;
}

/* Page title */
h1 {
  font-size: 1.75rem; /* 28px */
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* Section title */
h2 {
  font-size: 1.5rem; /* 24px */
  line-height: 1.3;
  letter-spacing: -0.025em;
}

/* Card title */
h3 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.4;
  letter-spacing: -0.015em;
}

/* Subsection title */
h4 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.5;
  letter-spacing: -0.015em;
}

/* Small title */
h5 {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
}

/* Tiny title */
h6 {
  font-size: 0.875rem; /* 14px */
  line-height: 1.5;
}

/* Body text */
p {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* Small text */
.text-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.5;
}

/* Large text */
.text-lg {
  font-size: 1.125rem; /* 18px */
  line-height: 1.5;
}

/* Extra large text */
.text-xl {
  font-size: 1.375rem; /* 22px */
  line-height: 1.3;
}

/* Label text */
label {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
  font-weight: 500;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--theme-button);
  color: white;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  letter-spacing: -0.011em; /* Slight letter spacing adjustment for Inter */
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

button:hover {
  background-color: var(--theme-button-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button:active {
  transform: translateY(0);
}

button:focus,
button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--theme-glow);
}

/* Primary button */
button.primary {
  background-color: var(--theme-button);
  color: white;
  border: none;
}

button.primary:hover {
  background-color: var(--theme-button-hover);
  box-shadow: 0 0 15px var(--theme-glow);
}

/* Secondary button */
button.secondary {
  background-color: var(--theme-bg-card);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}

button.secondary:hover {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-accent);
  box-shadow: 0 0 10px var(--theme-glow);
}

/* Glassmorphism utility classes with browser compatibility */
.glass {
  background: rgba(255, 255, 255, 0.7);
  /* Fallback for older browsers */
  background: var(--theme-bg-card);
  /* Modern browsers */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  -moz-backdrop-filter: blur(10px);
  -ms-backdrop-filter: blur(10px);
  border: 1px solid var(--theme-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  /* Enhanced support for different browsers */
  -webkit-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Browser-specific glassmorphism support */
@supports (backdrop-filter: blur(10px)) {
  .glass {
    background: rgba(255, 255, 255, 0.7);
  }
}

@supports (-webkit-backdrop-filter: blur(10px)) {
  .glass {
    background: rgba(255, 255, 255, 0.7);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(10px)) {
  .glass {
    background: var(--theme-bg-card);
    opacity: 0.95;
  }
}

/* Shadow effects with browser compatibility */
.shadow-glow {
  box-shadow: 0 0 10px var(--theme-glow);
  -webkit-box-shadow: 0 0 10px var(--theme-glow);
  -moz-box-shadow: 0 0 10px var(--theme-glow);
}

.shadow-glow-sm {
  box-shadow: 0 0 5px var(--theme-glow);
  -webkit-box-shadow: 0 0 5px var(--theme-glow);
  -moz-box-shadow: 0 0 5px var(--theme-glow);
}

.shadow-glow-lg {
  box-shadow: 0 0 15px var(--theme-glow), 0 0 30px var(--theme-glow);
  -webkit-box-shadow: 0 0 15px var(--theme-glow), 0 0 30px var(--theme-glow);
  -moz-box-shadow: 0 0 15px var(--theme-glow), 0 0 30px var(--theme-glow);
}

/* Transform utilities with browser compatibility */
.transform-hover {
  transition: transform 0.2s ease-in-out;
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  -moz-transition: -moz-transform 0.2s ease-in-out;
  -ms-transition: -ms-transform 0.2s ease-in-out;
}

.transform-hover:hover {
  transform: translateY(-2px) scale(1.02);
  -webkit-transform: translateY(-2px) scale(1.02);
  -moz-transform: translateY(-2px) scale(1.02);
  -ms-transform: translateY(-2px) scale(1.02);
}

/* Border radius with browser compatibility */
.rounded-modern {
  border-radius: 0.5rem;
  -webkit-border-radius: 0.5rem;
  -moz-border-radius: 0.5rem;
}

/* Flexbox fallbacks for older browsers */
.flex-fallback {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
}

.flex-center-fallback {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}








