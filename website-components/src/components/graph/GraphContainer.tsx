import React, { useEffect, useCallback, useState, useRef } from 'react';
import ReactFlow, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  NodeTypes,
  EdgeTypes,
  Panel,
  ConnectionLineType,
  NodeChange,
  applyNodeChanges
} from 'reactflow';
import 'reactflow/dist/style.css';

// Import modular components
import { TransactionNode } from './nodes/TransactionNode';
import { MetadataNode } from './nodes/MetadataNode';
import { CustomEdge } from './edges/CustomEdge';
import { GraphLayoutManager } from './layout/GraphLayoutManager';
import { GraphExporter } from './export/GraphExporter';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { useAlert } from '../../context/TailwindAlertContext';


// Define node and edge types outside component to prevent React Flow warnings
const nodeTypes: NodeTypes = {
  transaction: TransactionNode,
  metadata: MetadataNode
};

const edgeTypes: EdgeTypes = {
  default: CustomEdge,
  custom: CustomEdge
};

interface GraphContainerProps {
  complaintData: any;
  complaintId: string;
  onExportStateChange?: (isExporting: boolean) => void;
  onExportProgressChange?: (progress: { current: number; total: number; currentPage?: string }) => void;
}

export const GraphContainer: React.FC<GraphContainerProps> = ({
  complaintData,
  complaintId,
  onExportStateChange,
  onExportProgressChange
}) => {
  const { isDark: isDarkMode } = useThemeContext();
  const { showError } = useAlert();

  const [nodes, setNodes] = useNodesState([]);
  const [edges, setEdges] = useEdgesState([]);

  // Simple collapse/expand state - just track which nodes are hidden
  const [hiddenNodeIds, setHiddenNodeIds] = React.useState<Set<string>>(new Set());
  const [isGloballyCollapsed, setIsGloballyCollapsed] = React.useState(false);

  // Use refs to access current state in useEffect without causing re-renders
  const hiddenNodeIdsRef = React.useRef(hiddenNodeIds);
  const isGloballyCollapsedRef = React.useRef(isGloballyCollapsed);
  const reactFlowInstanceRef = React.useRef<any>(null);

  // Performance monitoring state
  const [lastWarningType, setLastWarningType] = useState<string | null>(null);

  // Performance limits - increased by 50 as requested
  const MAX_SAFE_NODES = 250; // Increased from 200
  const MAX_SAFE_EDGES = 350; // Increased from 300

  // Flag to prevent infinite loops in node changes
  const isProcessingChangesRef = useRef(false);

  // Update refs when state changes
  React.useEffect(() => {
    hiddenNodeIdsRef.current = hiddenNodeIds;
  }, [hiddenNodeIds]);

  React.useEffect(() => {
    isGloballyCollapsedRef.current = isGloballyCollapsed;
  }, [isGloballyCollapsed]);

  // Add a ref for nodes to avoid stale closure issues
  const nodesRef = React.useRef(nodes);
  React.useEffect(() => {
    nodesRef.current = nodes;
  }, [nodes]);

  // Simple warning system - show warning for 3 seconds when graph loads with large dataset
  React.useEffect(() => {
    const nodeCount = nodes.length;
    const edgeCount = edges.length;

    // Only check if we have nodes and haven't shown this warning type before
    if (nodeCount > 0) {
      let warning: string | null = null;
      let warningType: string | null = null;

      // Determine warning based on thresholds
      if (nodeCount > MAX_SAFE_NODES) {
        warningType = 'nodes';
        warning = `Large dataset detected: ${nodeCount} nodes. Performance may be affected.`;
      } else if (edgeCount > MAX_SAFE_EDGES) {
        warningType = 'edges';
        warning = `Large dataset detected: ${edgeCount} connections. Performance may be affected.`;
      }

      // Only show warning if the type has changed and we have a warning
      if (warning && warningType !== lastWarningType) {
        // Show warning for 3 seconds
        showError(warning);
        setLastWarningType(warningType);

        // Clear the warning type after 3 seconds to allow future warnings
        setTimeout(() => {
          setLastWarningType(null);
        }, 3000);
      }
    }
  }, [nodes.length, edges.length, lastWarningType, showError]);

  // Handle node changes (dragging, selection)
  const onNodesChange = useCallback((changes: NodeChange[]) => {
    // Prevent infinite loops
    if (isProcessingChangesRef.current) {
      return;
    }

    // Filter out selection changes for now to fix selection issues
    const nonSelectionChanges = changes.filter(change =>
      change.type !== 'select' || (change.type === 'select' && change.selected !== undefined)
    );

    if (nonSelectionChanges.length > 0) {
      isProcessingChangesRef.current = true;
      // First apply the changes to get the updated nodes
      setNodes((nds) => {
        const updatedNodes = applyNodeChanges(nonSelectionChanges, nds);

        // Check if any of the changes are position changes
        const positionChanges = nonSelectionChanges.filter(change =>
          change.type === 'position' && change.dragging === false
        );

        // If there are position changes and dragging has completed
        if (positionChanges.length > 0) {
          // For each moved node, update its child nodes
          const movedNodeIds = positionChanges.map(change => (change as any).id);

          // Create a map of parent nodes to their new positions
          const parentPositions = new Map();
          movedNodeIds.forEach(id => {
            const node = updatedNodes.find(n => n.id === id);
            if (node) {
              parentPositions.set(id, node.position);
            }
          });

          // Now update child nodes to follow their parents
          return updatedNodes.map(node => {
            // If this node has a parentId and that parent was moved
            if (node.data?.parentId && parentPositions.has(node.data.parentId)) {
              const parentPos = parentPositions.get(node.data.parentId);
              const parentNode = updatedNodes.find(n => n.id === node.data.parentId);

              // Calculate the relative position to maintain
              const offsetX = node.position.x - (parentNode?.position?.x || 0);
              const offsetY = node.position.y - (parentNode?.position?.y || 0);

              // Update the node's position to maintain the same relative position
              return {
                ...node,
                position: {
                  x: parentPos.x + offsetX,
                  y: parentPos.y + offsetY
                }
              };
            }
            return node;
          });
        }

        return updatedNodes;
      });

      // Reset the flag after processing
      setTimeout(() => {
        isProcessingChangesRef.current = false;
      }, 0);
    }
  }, [setNodes]);

  // Handle node selection separately
  const onNodeClick = useCallback((_: React.MouseEvent, node: any) => {
    // Update selection state for the clicked node
    setNodes(nodes => nodes.map(n => ({
      ...n,
      selected: n.id === node.id
    })));
  }, [setNodes]);

  // Callback to capture ReactFlow instance
  const onInit = useCallback((reactFlowInstance: any) => {
    reactFlowInstanceRef.current = reactFlowInstance;
  }, []);

  // Helper function to get all child node IDs recursively
  const getChildNodeIds = useCallback((parentId: string, allNodes: any[]): string[] => {
    const childIds: string[] = [];
    const directChildren = allNodes.filter(node => node.data?.parentId === parentId);

    directChildren.forEach(child => {
      childIds.push(child.id);
      // Recursively get grandchildren
      const grandChildren = getChildNodeIds(child.id, allNodes);
      childIds.push(...grandChildren);
    });

    return childIds;
  }, []);

  // Helper function to get direct child node IDs only (not recursive)
  const getDirectChildNodeIds = useCallback((parentId: string, allNodes: any[]): string[] => {
    return allNodes
      .filter(node => node.data?.parentId === parentId)
      .map(node => node.id);
  }, []);

  // Helper function to check if a node has children
  const nodeHasChildren = useCallback((nodeId: string, allNodes: any[]): boolean => {
    return allNodes.some(node => node.data?.parentId === nodeId);
  }, []);

  // Simple function to toggle global collapse/expand
  const toggleGlobalCollapse = useCallback(() => {
    const newGlobalState = !isGloballyCollapsed;
    setIsGloballyCollapsed(newGlobalState);

    if (newGlobalState) {
      // Collapse: hide all nodes except metadata and sender nodes
      setNodes(currentNodes => {
        const nodesToHide = currentNodes
          .filter(node => node.type !== 'metadata' && !node.data?.isSenderNode)
          .map(node => node.id);
        setHiddenNodeIds(new Set(nodesToHide));

        return currentNodes.map(node => ({
          ...node,
          hidden: node.type !== 'metadata' && !node.data?.isSenderNode,
          data: {
            ...node.data,
            isCollapsed: node.type !== 'metadata' && !node.data?.isSenderNode
          }
        }));
      });
    } else {
      // Expand: show all nodes
      setHiddenNodeIds(new Set());

      setNodes(currentNodes => {
        return currentNodes.map(node => ({
          ...node,
          hidden: false,
          data: {
            ...node.data,
            isCollapsed: false
          }
        }));
      });
    }
  }, [isGloballyCollapsed, setNodes]);

  // Simple function to toggle individual node collapse/expand
  const toggleIndividualNodeCollapse = useCallback((nodeId: string) => {
    // First, update the hiddenNodeIds state
    setHiddenNodeIds(currentHiddenIds => {

      // We need to get the current nodes to find children
      // Use the ref to get the current nodes state
      const currentNodes = nodesRef.current;

      // Get only direct children (not recursive) from current nodes
      const directChildIds = currentNodes
        .filter(node => node.data?.parentId === nodeId)
        .map(node => node.id);

      if (directChildIds.length === 0) {
        return currentHiddenIds; // No children to toggle
      }

      // Check if ALL direct children are currently hidden
      const allDirectChildrenHidden = directChildIds.every(id => currentHiddenIds.has(id));

      const newHiddenIds = new Set(currentHiddenIds);

      if (allDirectChildrenHidden) {
        // All direct children are hidden, so expand (show) them and their subtrees
        const childrenToShow = getChildNodeIds(nodeId, currentNodes);
        childrenToShow.forEach(id => newHiddenIds.delete(id));
      } else {
        // Some or no direct children are hidden, so collapse (hide) all of them and their subtrees
        const childrenToHide = getChildNodeIds(nodeId, currentNodes);
        childrenToHide.forEach(id => newHiddenIds.add(id));
      }

      // Update the nodes state after updating hidden IDs
      // Use requestAnimationFrame instead of setTimeout to avoid potential loops
      requestAnimationFrame(() => {
        setNodes(currentNodes => {
          return currentNodes.map(node => {
            const isHidden = newHiddenIds.has(node.id);
            const hasDirectChildren = currentNodes.some(n => n.data?.parentId === node.id);
            const directChildren = currentNodes.filter(n => n.data?.parentId === node.id);
            const areDirectChildrenHidden = hasDirectChildren &&
              directChildren.every(child => newHiddenIds.has(child.id));

            return {
              ...node,
              hidden: isHidden,
              data: {
                ...node.data,
                isCollapsed: areDirectChildrenHidden,
                hasChildren: hasDirectChildren
              }
            };
          });
        });
      });

      return newHiddenIds;
    });
  }, [getChildNodeIds, setNodes]);

  // Function to temporarily expand all nodes for export
  const temporarilyExpandAllNodes = useCallback(() => {
    setNodes(currentNodes => {
      return currentNodes.map(node => ({
        ...node,
        hidden: false,
        data: {
          ...node.data,
          isCollapsed: false
        }
      }));
    });
    setHiddenNodeIds(new Set());
  }, [setNodes]);

  // Function to restore original state after export
  const restoreOriginalState = useCallback(() => {
    // The state will be restored automatically by the existing logic
    // since we're not permanently changing the hiddenNodeIds state
  }, []);

  // Process graph data when complaint data is loaded
  useEffect(() => {
    if (!complaintData) return;

    try {

      // Extract transactions and metadata
      let transactions = [];
      let metadata = {};

      // Case 1: We have transactions and metadata directly
      if (complaintData.transactions && complaintData.metadata) {
        transactions = complaintData.transactions;
        metadata = complaintData.metadata;
      }
      // Case 2: We have graph_data already generated
      else if (complaintData.graph_data) {
        let graphData = complaintData.graph_data;
        if (typeof graphData === 'string') {
          try {
            graphData = JSON.parse(graphData);
          } catch (e) {
            showError('Invalid graph data format');
            return;
          }
        }

        if (graphData.transactions && graphData.metadata) {
          transactions = graphData.transactions;
          metadata = graphData.metadata;
        }
      }
      // Case 3: We have raw data
      else if (complaintData.data) {
        const rawData = complaintData.data;

        if (rawData.transactions && Array.isArray(rawData.transactions)) {
          transactions = rawData.transactions;
        }

        if (rawData.metadata) {
          metadata = rawData.metadata;
        } else {
          metadata = {
            complaint_number: rawData.complaint_number || complaintId || 'Unknown',
            complainant_name: rawData.complainant_name || 'Unknown',
            date_of_complaint: rawData.date || new Date().toISOString().split('T')[0],
            total_amount: rawData.amount || '0'
          };
        }
      }

      if (transactions.length === 0) {
        showError('No transaction data found');
        return;
      }

      // Process transactions for graph using the layout manager (TB layout only)
      const layoutManager = new GraphLayoutManager();
      const { nodes: graphNodes, edges: graphEdges } = layoutManager.processTransactionsForGraph(
        transactions,
        metadata,
        complaintData,
        complaintId,
        isDarkMode,
        false // Always use TB (Top-Bottom) layout
      );

      // Apply performance limits - limit nodes if too many
      let limitedNodes = graphNodes;
      let limitedEdges = graphEdges;

      if (graphNodes.length > MAX_SAFE_NODES) {
        // Prioritize important nodes: metadata, high-value transactions, and central nodes
        const metadataNodes = graphNodes.filter(node => node.type === 'metadata');
        const otherNodes = graphNodes.filter(node => node.type !== 'metadata');

        // Sort other nodes by importance (amount, connections, etc.)
        const sortedNodes = otherNodes.sort((a, b) => {
          // Prioritize by transaction amount if available
          const amountA = parseFloat(a.data?.amount || '0');
          const amountB = parseFloat(b.data?.amount || '0');
          if (amountA !== amountB) return amountB - amountA;

          // Then by node type priority
          const typePriority = { 'account': 3, 'transaction': 2, 'person': 1 };
          const priorityA = typePriority[a.type as keyof typeof typePriority] || 0;
          const priorityB = typePriority[b.type as keyof typeof typePriority] || 0;
          return priorityB - priorityA;
        });

        limitedNodes = [
          ...metadataNodes,
          ...sortedNodes.slice(0, MAX_SAFE_NODES - metadataNodes.length)
        ];

        // Filter edges to only include those between remaining nodes
        const nodeIds = new Set(limitedNodes.map(node => node.id));
        limitedEdges = graphEdges.filter(edge =>
          nodeIds.has(edge.source) && nodeIds.has(edge.target)
        );

        // Show informative warning about optimization
        showError(`Performance optimization: Displaying ${limitedNodes.length} most important nodes out of ${graphNodes.length} total. High-value transactions and key accounts are prioritized.`);
      }

      // Add toggle function to node data and apply current visibility state
      const nodesWithToggle = limitedNodes.map(node => {
        const hasDirectChildren = nodeHasChildren(node.id, limitedNodes);
        const directChildren = getDirectChildNodeIds(node.id, limitedNodes);
        const areDirectChildrenHidden = hasDirectChildren &&
          directChildren.every(childId =>
            isGloballyCollapsedRef.current ?
              (limitedNodes.find(n => n.id === childId)?.type !== 'metadata' && !limitedNodes.find(n => n.id === childId)?.data?.isSenderNode) :
              hiddenNodeIdsRef.current.has(childId)
          );

        return {
          ...node,
          hidden: isGloballyCollapsedRef.current ? (node.type !== 'metadata' && !node.data?.isSenderNode) : hiddenNodeIdsRef.current.has(node.id),
          data: {
            ...node.data,
            hasChildren: hasDirectChildren,
            isCollapsed: areDirectChildrenHidden,
            onToggleCollapse: () => toggleIndividualNodeCollapse(node.id)
          }
        };
      });

      setNodes(nodesWithToggle);
      setEdges(limitedEdges);

    } catch (error) {
      showError('Failed to process graph data: ' + (error instanceof Error ? error.message : String(error)));
    }
  }, [complaintData, complaintId, showError, isDarkMode, toggleIndividualNodeCollapse, nodeHasChildren, getDirectChildNodeIds]);

  return (
    <div className="w-full h-full relative">
      {/* SVG definitions for markers */}
      <svg style={{ position: 'absolute', top: 0, left: 0, width: 0, height: 0 }}>
        <defs>
          <marker
            id="edgeMarker"
            markerWidth="20"
            markerHeight="20"
            refX="10"
            refY="10"
            orient="auto"
            markerUnits="strokeWidth"
          >
            <circle
              cx="10"
              cy="10"
              r="3"
              fill={isDarkMode ? '#00aaff' : '#6366f1'}
              stroke={isDarkMode ? '#00aaff' : '#6366f1'}
              strokeWidth="1"
            />
          </marker>
        </defs>
      </svg>

      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        onNodesChange={onNodesChange}
        onNodeClick={onNodeClick}
        onInit={onInit}
        fitView={false}
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
          minZoom: 0.1,
          maxZoom: 1.5
        }}
        nodesDraggable={true}
        nodesConnectable={false}
        elementsSelectable={true}
        selectNodesOnDrag={false}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        zoomOnDoubleClick={false}
        deleteKeyCode={null}
        multiSelectionKeyCode={null}
        selectionKeyCode={null}
        edgesFocusable={false}
        edgesUpdatable={false}
        defaultEdgeOptions={{
          style: {
            strokeWidth: 2,
            strokeDasharray: 'none', // Solid lines
            fill: 'none'
          },
          animated: false // We handle animation in CustomEdge
        }}
        connectionLineType={ConnectionLineType.SmoothStep}
        proOptions={{ hideAttribution: true }}
        className={isDarkMode ? 'dark-flow' : 'light-flow'}
        style={{
          background: isDarkMode ? '#0a0a0f' : '#f7f7f7',
          width: '100%',
          height: '100%'
        }}
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 0.6 }}
      >
        <Background
          color={isDarkMode ? '#00aaff' : '#aaa'}
          gap={16}
          size={1}
        />
        <Controls className={isDarkMode ? 'dark-controls' : 'light-controls'} showInteractive={true} />

        <Panel position="top-right" className="p-1 rounded shadow-md" style={{
          backgroundColor: 'var(--theme-bg-card)',
          color: 'var(--theme-text)',
          border: '1px solid var(--theme-border)'
        }}>
          <div className="flex gap-1 items-center">
            {/* Global Collapse/Expand Control */}
            <button
              className="p-0.5 rounded transition-colors duration-200"
              style={{
                backgroundColor: 'var(--theme-button)',
                color: 'white'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-button-hover)';
                e.currentTarget.style.boxShadow = '0 0 8px var(--theme-glow)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-button)';
                e.currentTarget.style.boxShadow = 'none';
              }}
              onClick={toggleGlobalCollapse}
              title={isGloballyCollapsed ? "Expand All Nodes" : "Collapse to Sender Nodes"}
            >
              {isGloballyCollapsed ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              )}
            </button>
            <GraphExporter
              nodes={nodes}
              edges={edges}
              complaintId={complaintId}
              isDarkMode={isDarkMode}
              onTemporaryExpand={temporarilyExpandAllNodes}
              onRestoreState={restoreOriginalState}
              onExportStateChange={onExportStateChange}
              onExportProgressChange={onExportProgressChange}
              reactFlowInstance={reactFlowInstanceRef.current}
            />
          </div>
        </Panel>
      </ReactFlow>


    </div>
  );
};
