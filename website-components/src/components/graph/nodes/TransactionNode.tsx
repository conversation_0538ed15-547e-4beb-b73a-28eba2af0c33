import React, { useState } from 'react';
import { NodeProps, Handle, Position, useReactFlow } from 'reactflow';
import { useThemeContext } from '../../../context/TailwindThemeContext';

export const TransactionNode: React.FC<NodeProps> = ({ data, id }) => {
  const { isDark: isDarkMode } = useThemeContext();
  const [isExpanded, setIsExpanded] = useState(true);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isMultipleTransactionsExpanded, setIsMultipleTransactionsExpanded] = useState(true);
  const [isSubTransactionsExpanded, setIsSubTransactionsExpanded] = useState(true);
  // const [isWordWrapEnabled, setIsWordWrapEnabled] = useState(true);
  const { setNodes } = useReactFlow();

  // Respect global expansion state if provided
  React.useEffect(() => {
    if (data.forceExpanded !== undefined) {
      setIsExpanded(data.forceExpanded);
    }
  }, [data.forceExpanded]);

  // Determine node type - sender, main, sub, or special
  const isSenderNode = data.isSenderNode === true;
  const isMainTransaction = data.isMainTransaction === true;
  const isSubTransaction = data.isSubTransaction === true;
  const isSpecialTxn = data.isSpecial === true;

  // Simple styling based on transaction type
  const getNodeStyle = () => {
    // Sender node - blue
    if (isSenderNode) {
      return {
        bg: isDarkMode ? 'bg-blue-900/30' : 'bg-blue-50',
        border: isDarkMode ? 'border-blue-700' : 'border-blue-300',
        text: isDarkMode ? 'text-blue-400' : 'text-blue-700'
      };
    }

    // Special transaction - red
    if (isSpecialTxn) {
      return {
        bg: isDarkMode ? 'bg-red-900/30' : 'bg-red-50',
        border: isDarkMode ? 'border-red-700' : 'border-red-300',
        text: isDarkMode ? 'text-red-400' : 'text-red-700'
      };
    }

    // Sub-transaction - purple
    if (isSubTransaction) {
      return {
        bg: isDarkMode ? 'bg-purple-900/30' : 'bg-purple-50',
        border: isDarkMode ? 'border-purple-700' : 'border-purple-300',
        text: isDarkMode ? 'text-purple-400' : 'text-purple-700'
      };
    }

    // Main transaction - green
    if (isMainTransaction || data.type === 'Money Transfer to') {
      return {
        bg: isDarkMode ? 'bg-green-900/30' : 'bg-green-50',
        border: isDarkMode ? 'border-green-700' : 'border-green-300',
        text: isDarkMode ? 'text-green-400' : 'text-green-700'
      };
    }

    // Default - amber
    return {
      bg: isDarkMode ? 'bg-amber-900/30' : 'bg-amber-50',
      border: isDarkMode ? 'border-amber-700' : 'border-amber-300',
      text: isDarkMode ? 'text-amber-400' : 'text-amber-700'
    };
  };

  const nodeStyle = getNodeStyle();

  // Function to delete this node
  const handleDeleteNode = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setShowDeleteConfirm(false);
  };

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Function to toggle individual node collapse (hide/show children)
  const toggleNodeCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (data.onToggleCollapse) {
      data.onToggleCollapse();
    }
  };

  // Check if this node has children - use the hasChildren property from data
  const hasChildren = data.hasChildren === true;

  return (
    <div
      className={`p-2 rounded-lg border shadow-md w-[300px] ${nodeStyle.bg} ${nodeStyle.border} transition-all duration-200`}
      style={{ cursor: 'move' }}
    >
      {/* Add source and target handles without specific IDs */}
      <Handle
        type="target"
        position={Position.Top}
        style={{ background: isDarkMode ? '#6366f1' : '#3b82f6', width: '8px', height: '8px' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ background: isDarkMode ? '#6366f1' : '#3b82f6', width: '8px', height: '8px' }}
      />

      {/* Header with controls */}
      <div className="flex justify-between items-center mb-1">
        <div
          className={`font-medium text-base pb-1 border-b border-gray-200 dark:border-gray-700 ${nodeStyle.text} flex-grow cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? "Click to collapse" : "Click to expand"}
        >
          {data.label}
          {!isExpanded && data.hasSubTransactions && data.subTransactions && (
            <span className={`ml-2 text-xs ${isDarkMode ? 'text-purple-400' : 'text-purple-700'}`}>
              ({data.subTransactions.length} sub-txns
              {data.subTransactions[0]?.type && `: ${data.subTransactions[0].type.substring(0, 15)}${data.subTransactions[0].type.length > 15 ? '...' : ''}`})
            </span>
          )}
          <span className="ml-2 text-xs">
            {isExpanded ?
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              :
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            }
          </span>
        </div>
        <div className="flex gap-1 ml-2">
          {/* Individual Node Collapse/Expand Button - only show for non-leaf nodes */}
          {hasChildren && (
            <button
              onClick={toggleNodeCollapse}
              className={`p-0.5 rounded ${isDarkMode ? 'hover:bg-blue-900/50 text-blue-400' : 'hover:bg-blue-100 text-blue-600'}`}
              title={data.isCollapsed ? "Expand children" : "Collapse children"}
            >
              {data.isCollapsed ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
                </svg>
              )}
            </button>
          )}
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className={`p-0.5 rounded ${isDarkMode ? 'hover:bg-red-900/50 text-red-400' : 'hover:bg-red-100 text-red-600'}`}
            title="Delete node"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      {showDeleteConfirm && (
        <div className={`absolute top-0 left-0 w-full h-full ${nodeStyle.bg} rounded-lg border ${nodeStyle.border} z-10 flex flex-col justify-center items-center p-2`}>
          <p className={`text-center mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Delete this node?</p>
          <div className="flex gap-2">
            <button
              onClick={handleDeleteNode}
              className={`px-2 py-1 rounded ${isDarkMode ? 'bg-red-700 text-white' : 'bg-red-500 text-white'}`}
            >
              Yes
            </button>
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className={`px-2 py-1 rounded ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-800'}`}
            >
              No
            </button>
          </div>
        </div>
      )}

      {/* Always show essential information */}
      <div className="flex flex-col gap-1 text-sm">
        <div className="grid grid-cols-2 gap-x-1 gap-y-1">
          {/* Account Details - Always visible with word wrap */}
          <div className="col-span-2">
            <div className="flex justify-between items-start">
              <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Account:</span>
              <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[180px]`}>
                {data.account}
              </span>
            </div>
          </div>

          {/* Amount - Always visible */}
          {data.amount && (
            <div className="flex justify-between col-span-2">
              <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>
                {data.isConsolidated ? 'Total Amount:' : 'Amount:'}
              </span>
              <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>₹{data.amount}</span>
            </div>
          )}

          {/* Transaction ID - prominently display for Money Transfer nodes (only if NOT consolidated) with word wrap */}
          {(data.type === 'Money Transfer to' || data.type === 'Money transfer') && (data.txn_id || data.transaction_id) && !data.isConsolidated && (
            <div className="col-span-2 mb-1">
              <div className="flex justify-between items-start">
                <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Txn ID:</span>
                <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} font-mono text-xs text-right break-all leading-tight max-w-[180px]`}>
                  {data.txn_id || data.transaction_id}
                </span>
              </div>
            </div>
          )}

          {/* Additional details - only show if expanded */}
          {isExpanded && (
            <>
              {data.date && (
                <div className="flex justify-between col-span-2">
                  <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Date:</span>
                  <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{data.date}</span>
                </div>
              )}

              {/* Sender Details - only show for receiver nodes with word wrap */}
              {!data.isSenderNode && data.sender_account && (
                <div className="col-span-2">
                  <div className="flex justify-between items-start">
                    <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>From:</span>
                    <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[180px]`}>
                      {data.sender_account}
                    </span>
                  </div>
                </div>
              )}

              {/* Transaction Type - directly from CSV */}
              {data.type && (
                <div className="flex justify-between col-span-2">
                  <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Type:</span>
                  <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{data.type}</span>
                </div>
              )}

              {/* Reference field (receiver_info) - with word wrap */}
              {data.receiver_info && (
                <div className="col-span-2">
                  <div className="flex justify-between items-start">
                    <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Reference:</span>
                    <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-xs text-right break-words leading-tight max-w-[180px]`}>
                      {data.receiver_info}
                    </span>
                  </div>
                </div>
              )}

              {/* Layer */}
              <div className="flex justify-between col-span-2 mt-1">
                <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Layer:</span>
                <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{data.layer}</span>
              </div>

              {/* Consolidated transactions section */}
              {data.isConsolidated && data.consolidatedTransactions && data.consolidatedTransactions.length > 0 && (
                <div className="col-span-2 mt-1 border-t border-gray-200 dark:border-gray-700 pt-1">
                  <div
                    className={`font-medium text-sm mb-0.5 ${nodeStyle.text} cursor-pointer flex items-center`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsMultipleTransactionsExpanded(!isMultipleTransactionsExpanded);
                    }}
                    title={isMultipleTransactionsExpanded ? "Click to collapse" : "Click to expand"}
                  >
                    Multiple Transactions ({data.consolidatedTransactions.length})
                    <span className="ml-1 text-xs">
                      {isMultipleTransactionsExpanded ?
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                        :
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      }
                    </span>
                  </div>

                  {isMultipleTransactionsExpanded && data.consolidatedTransactions.map((consolidatedTxn: any, index: number) => (
                    <div
                      key={`consolidated-txn-${index}`}
                      className={`p-0.5 mb-0.5 rounded ${isDarkMode ? 'bg-blue-900/30 border border-blue-700/50' : 'bg-blue-50 border border-blue-300'}`}
                    >
                      {/* Consolidated transaction header */}
                      <div className={`font-medium ${isDarkMode ? 'text-blue-400' : 'text-blue-700'} border-b border-blue-200 dark:border-blue-800 pb-0.5 mb-0.5 text-xs`}>
                        Transaction {index + 1}
                      </div>

                      {/* Consolidated transaction details */}
                      <div className="grid grid-cols-2 gap-x-0.5 gap-y-0.5 text-xs">
                        {/* Transaction ID */}
                        {(consolidatedTxn.txn_id || consolidatedTxn.transaction_id) && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Txn ID:</span>
                            <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} font-mono text-xs`} title={consolidatedTxn.txn_id || consolidatedTxn.transaction_id}>
                              {consolidatedTxn.txn_id || consolidatedTxn.transaction_id}
                            </span>
                          </div>
                        )}

                        {/* Individual Amount */}
                        {consolidatedTxn.amount && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Amount:</span>
                            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>₹{consolidatedTxn.amount}</span>
                          </div>
                        )}

                        {/* Reference */}
                        {consolidatedTxn.reference && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Reference:</span>
                            <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-xs`} title={consolidatedTxn.reference}>
                              {consolidatedTxn.reference}
                            </span>
                          </div>
                        )}

                        {/* Date */}
                        {consolidatedTxn.date && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Date:</span>
                            <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{consolidatedTxn.date}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Sub-transactions section */}
              {data.hasSubTransactions && data.subTransactions && data.subTransactions.length > 0 && (
                <div className="col-span-2 mt-1 border-t border-gray-200 dark:border-gray-700 pt-1">
                  <div
                    className={`font-medium text-sm mb-0.5 ${nodeStyle.text} cursor-pointer flex items-center`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsSubTransactionsExpanded(!isSubTransactionsExpanded);
                    }}
                    title={isSubTransactionsExpanded ? "Click to collapse" : "Click to expand"}
                  >
                    Sub-Transactions ({data.subTransactions.length})
                    <span className="ml-1 text-xs">
                      {isSubTransactionsExpanded ?
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                        :
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      }
                    </span>
                  </div>

                  {isSubTransactionsExpanded && data.subTransactions.map((subTxn: any, index: number) => (
                    <div
                      key={`sub-txn-${index}`}
                      className={`p-0.5 mb-0.5 rounded ${isDarkMode ? 'bg-purple-900/30 border border-purple-700/50' : 'bg-purple-50 border border-purple-300'}`}
                    >
                      {/* Sub-transaction header - just a simple divider without bank name */}
                      <div className={`font-medium ${isDarkMode ? 'text-purple-400' : 'text-purple-700'} border-b border-purple-200 dark:border-purple-800 pb-0.5 mb-0.5 text-xs`}>
                        Sub-Transaction {index + 1}
                      </div>

                      {/* Sub-transaction details - only date, amount, type, and reference */}
                      <div className="grid grid-cols-2 gap-x-0.5 gap-y-0.5 text-xs">
                        {/* Date */}
                        {subTxn.date && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Date:</span>
                            <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{subTxn.date}</span>
                          </div>
                        )}

                        {/* Amount */}
                        {subTxn.amount && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Amount:</span>
                            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>₹{subTxn.amount}</span>
                          </div>
                        )}

                        {/* Type */}
                        {subTxn.type && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Type:</span>
                            <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{subTxn.type}</span>
                          </div>
                        )}

                        {/* Reference field (receiver_info) - with word wrap */}
                        {subTxn.receiver_info && (
                          <div className="col-span-2">
                            <div className="flex justify-between items-start">
                              <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Reference:</span>
                              <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-xs text-right break-words leading-tight max-w-[140px]`}>
                                {subTxn.receiver_info}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
