import React from 'react';
import { EdgeProps, Position } from 'reactflow';
import { useThemeContext } from '../../../context/TailwindThemeContext';
import './EdgeAnimations.css';

export const CustomEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  style = {}
}) => {
  const { isDark: isDarkMode } = useThemeContext();

  // Get path for a smoother, more direct connection
  const getSmoothPath = () => {
    // Calculate the vertical and horizontal distances
    const verticalDistance = Math.abs(targetY - sourceY);
    const horizontalDistance = Math.abs(targetX - sourceX);

    // Check if this is a parent-child connection (based on data or positions)
    const isParentChildConnection = data?.isParentChild ||
      (sourcePosition === Position.Bottom && targetPosition === Position.Top &&
       Math.abs(sourceX - targetX) < 175); // Half of NODE_WIDTH (350)

    // For sender node connections (layer 0 to layer 1), ensure direct vertical path
    const isSenderConnection = data?.source?.includes('sender_') ||
                              (sourcePosition === Position.Bottom &&
                               targetPosition === Position.Top &&
                               Math.abs(sourceY - targetY) > 500);

    // For parent-child connections, create a more direct vertical path
    if (isParentChildConnection || isSenderConnection) {
      // Use a simple bezier curve for parent-child connections
      // This creates a straighter path with minimal horizontal deviation
      const offsetY = verticalDistance * 0.15; // Small offset for a slight curve

      // If source and target are nearly aligned vertically, use an even straighter path
      if (Math.abs(sourceX - targetX) < 50) {
        return `
          M${sourceX},${sourceY}
          C${sourceX},${sourceY + offsetY} ${targetX},${targetY - offsetY} ${targetX},${targetY}
        `.trim().replace(/\s+/g, ' ');
      } else {
        // For slightly offset nodes, create a path that moves vertically first, then horizontally
        const midY = sourceY + (targetY - sourceY) * 0.5;
        return `
          M${sourceX},${sourceY}
          C${sourceX},${midY} ${targetX},${midY} ${targetX},${targetY}
        `.trim().replace(/\s+/g, ' ');
      }
    }

    // For other connections, determine if primarily vertical or horizontal
    const isVertical = verticalDistance > horizontalDistance;

    // Use different control points based on direction
    let path;

    if (isVertical && sourceY < targetY) {
      // Downward connection - straighter path
      const controlPointY1 = sourceY + verticalDistance * 0.2;
      const controlPointY2 = targetY - verticalDistance * 0.2;

      path = `
        M${sourceX},${sourceY}
        C${sourceX},${controlPointY1} ${targetX},${controlPointY2} ${targetX},${targetY}
      `;
    } else if (isVertical && sourceY > targetY) {
      // Upward connection - less common, but handle it
      const controlPointY1 = sourceY - verticalDistance * 0.2;
      const controlPointY2 = targetY + verticalDistance * 0.2;

      path = `
        M${sourceX},${sourceY}
        C${sourceX},${controlPointY1} ${targetX},${controlPointY2} ${targetX},${targetY}
      `;
    } else {
      // Horizontal connection - smoother S-curve
      // Adjust control points based on source and target positions
      const sourceOffset = sourcePosition === Position.Bottom ? 0.3 : 0.1;
      const targetOffset = targetPosition === Position.Top ? 0.3 : 0.1;

      path = `
        M${sourceX},${sourceY}
        C${sourceX + (targetX - sourceX) * sourceOffset},${sourceY}
         ${targetX - (targetX - sourceX) * targetOffset},${targetY}
         ${targetX},${targetY}
      `;
    }

    return path.trim().replace(/\s+/g, ' ');
  };

  const edgePath = getSmoothPath();
  const edgeColor = isDarkMode ? '#6366f1' : '#3b82f6';
  const dotColor = isDarkMode ? '#818cf8' : '#60a5fa';

  // Create unique animation timing based on edge id for variety
  const animationDuration = 2.5 + (parseInt(id.slice(-1)) || 0) * 0.2; // 2.5-4.3s range
  const secondDotDelay = animationDuration * 0.4; // Stagger the second dot

  return (
    <>
      {/* Main edge path - solid line */}
      <path
        id={id}
        className="react-flow__edge-path"
        d={edgePath}
        style={{
          ...style,
          strokeWidth: 2,
          stroke: edgeColor,
          fill: 'none',
          strokeDasharray: 'none' // Ensure solid line
        }}
        markerEnd="url(#edgeMarker)"
      />

      {/* Animated dot flowing along the edge */}
      <circle
        r="4"
        fill={dotColor}
        className="edge-dot"
        style={{
          opacity: 0.8
        }}
      >
        <animateMotion
          dur={`${animationDuration}s`}
          repeatCount="indefinite"
          path={edgePath}
        />
      </circle>

      {/* Additional smaller dots for enhanced flow effect */}
      <circle
        r="3"
        fill={dotColor}
        className="edge-dot-small"
        style={{
          opacity: 0.6
        }}
      >
        <animateMotion
          dur={`${animationDuration}s`}
          repeatCount="indefinite"
          path={edgePath}
          begin={`${secondDotDelay}s`}
        />
      </circle>
    </>
  );
};
