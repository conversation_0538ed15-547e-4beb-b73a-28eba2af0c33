import { MarkerType } from 'reactflow';
import dagre from 'dagre';



// Define types for graph nodes and edges
type GraphNode = {
  id: string;
  type: string;
  data: any;
  position: { x: number; y: number };
  layer?: number;
};

// Define a type that matches ReactFlow's Edge type but with our custom properties
type GraphEdge = {
  id: string;
  source: string;
  target: string;
  type?: string;
  animated?: boolean;
  label?: string;
  data?: any;
  markerEnd?: {
    type: MarkerType;
    width: number;
    height: number;
    color: string;
  };
};

export class GraphLayoutManager {
  private dagreGraph: dagre.graphlib.Graph;
  private readonly NODE_WIDTH = 350; // Increased width for better readability

  constructor() {
    // Setup the dagre graph
    this.dagreGraph = new dagre.graphlib.Graph();
    this.dagreGraph.setDefaultEdgeLabel(() => ({}));
  }

  // Analyze sender trees to determine complexity and optimal ordering
  private analyzeSenderTrees(transactions: any[], transactionsByLayer: { [key: number]: any[] }): {
    senderAccounts: string[];
    senderComplexity: { [key: string]: { totalNodes: number; maxDepth: number; directChildren: number } };
    orderedSenders: string[];
  } {
    const senderComplexity: { [key: string]: { totalNodes: number; maxDepth: number; directChildren: number } } = {};
    const uniqueSenderAccounts = new Set<string>();

    // Extract unique sender accounts from Layer 1 transactions
    if (transactionsByLayer[1]) {
      transactionsByLayer[1].forEach(txn => {
        const isMainTransactionType = txn.type === 'Money Transfer to' || txn.isMainTransaction === true;
        if (isMainTransactionType && txn.sender_account) {
          uniqueSenderAccounts.add(txn.sender_account);
        }
      });
    }

    // Analyze each sender's tree complexity
    Array.from(uniqueSenderAccounts).forEach(senderAccount => {
      const analysis = this.analyzeSenderTreeComplexity(senderAccount, transactions, transactionsByLayer);
      senderComplexity[senderAccount] = analysis;
    });

    // Order senders by complexity (least complex first)
    const orderedSenders = Array.from(uniqueSenderAccounts).sort((a, b) => {
      const complexityA = senderComplexity[a];
      const complexityB = senderComplexity[b];

      // Primary sort: by total nodes (ascending - least complex first)
      if (complexityA.totalNodes !== complexityB.totalNodes) {
        return complexityA.totalNodes - complexityB.totalNodes;
      }

      // Secondary sort: by max depth (ascending)
      if (complexityA.maxDepth !== complexityB.maxDepth) {
        return complexityA.maxDepth - complexityB.maxDepth;
      }

      // Tertiary sort: by direct children (ascending)
      return complexityA.directChildren - complexityB.directChildren;
    });

    return {
      senderAccounts: Array.from(uniqueSenderAccounts),
      senderComplexity,
      orderedSenders
    };
  }

  // Analyze the complexity of a single sender's tree
  private analyzeSenderTreeComplexity(
    senderAccount: string,
    _transactions: any[],
    transactionsByLayer: { [key: number]: any[] }
  ): { totalNodes: number; maxDepth: number; directChildren: number } {
    let totalNodes = 0;
    let maxDepth = 0;
    let directChildren = 0;

    // Count direct children (layer 1)
    if (transactionsByLayer[1]) {
      directChildren = transactionsByLayer[1].filter(txn =>
        txn.sender_account === senderAccount &&
        (txn.isMainTransaction === true || txn.type === 'Money Transfer to')
      ).length;
      totalNodes += directChildren;
    }

    // Recursively count nodes in deeper layers
    const countNodesInSubtree = (accountsToCheck: string[], currentLayer: number): number => {
      if (currentLayer > 10 || !transactionsByLayer[currentLayer]) return 0; // Prevent infinite recursion

      let nodeCount = 0;
      const nextLayerAccounts: string[] = [];

      transactionsByLayer[currentLayer].forEach(txn => {
        if (accountsToCheck.includes(txn.sender_account || '')) {
          nodeCount++;
          const receiverAccount = txn.receiver_account || txn.account;
          if (receiverAccount && !nextLayerAccounts.includes(receiverAccount)) {
            nextLayerAccounts.push(receiverAccount);
          }
        }
      });

      if (nodeCount > 0) {
        maxDepth = Math.max(maxDepth, currentLayer);
      }

      if (nextLayerAccounts.length > 0) {
        nodeCount += countNodesInSubtree(nextLayerAccounts, currentLayer + 1);
      }

      return nodeCount;
    };

    // Get receiver accounts from layer 1 to start the recursive count
    const layer1ReceiverAccounts: string[] = [];
    if (transactionsByLayer[1]) {
      transactionsByLayer[1].forEach(txn => {
        if (txn.sender_account === senderAccount) {
          const receiverAccount = txn.receiver_account || txn.account;
          if (receiverAccount && !layer1ReceiverAccounts.includes(receiverAccount)) {
            layer1ReceiverAccounts.push(receiverAccount);
          }
        }
      });
    }

    // Count nodes in layers 2 and beyond
    if (layer1ReceiverAccounts.length > 0) {
      totalNodes += countNodesInSubtree(layer1ReceiverAccounts, 2);
    }

    return { totalNodes, maxDepth: Math.max(maxDepth, 1), directChildren };
  }

  // Consolidate matching money transfer transactions and attach sub-transactions
  private consolidateMoneyTransferTransactions(transactions: any[]): any[] {
    const consolidatedTransactions: any[] = [];
    const processedTransactions = new Set<string>();

    // Group transactions by layer first
    const transactionsByLayer: { [key: number]: any[] } = {};
    transactions.forEach(txn => {
      const layer = txn.layer !== undefined ? Number(txn.layer) : 0;
      if (!transactionsByLayer[layer]) {
        transactionsByLayer[layer] = [];
      }
      transactionsByLayer[layer].push(txn);
    });

    // First, identify all main transactions and their potential sub-transactions
    const mainTransactionMap: { [key: string]: any } = {};
    const subTransactionMap: { [key: string]: any[] } = {};

    // Process each layer
    Object.keys(transactionsByLayer)
      .map(Number)
      .sort((a, b) => a - b)
      .forEach(layer => {
        const layerTxns = transactionsByLayer[layer];

        // Separate main transactions and sub-transactions
        const mainTransactions: any[] = [];
        const subTransactions: any[] = [];

        layerTxns.forEach(txn => {
          const txnId = `${txn.sender_account || ''}_${txn.txn_id || ''}_${txn.layer || 0}`;

          // Skip if already processed
          if (processedTransactions.has(txnId)) {
            return;
          }

          const isMoneyTransfer = txn.type === 'Money transfer' || txn.type === 'Money Transfer to';

          if (isMoneyTransfer) {
            mainTransactions.push(txn);
            // Create a key for this main transaction for sub-transaction matching
            const mainTxnKey = `${txn.receiver_account || txn.account || ''}_${txn.txn_id || ''}`;
            mainTransactionMap[mainTxnKey] = txn;
          } else {
            // This is a potential sub-transaction
            subTransactions.push(txn);
          }
        });

        // Match sub-transactions to main transactions with duplicate detection
        subTransactions.forEach(subTxn => {
          const senderAccount = subTxn.sender_account || '';
          const senderTxnId = subTxn.txn_id || '';

          // Try to find matching main transaction
          // Match by account and transaction ID
          const matchKey1 = `${senderAccount}_${senderTxnId}`;
          // Match by account only (fallback)
          const matchKey2 = senderAccount;

          let matchedMainTxn = null;

          // First try exact match with transaction ID
          if (mainTransactionMap[matchKey1]) {
            matchedMainTxn = mainTransactionMap[matchKey1];
          } else {
            // Try to find by account only
            Object.keys(mainTransactionMap).forEach(key => {
              if (key.startsWith(matchKey2 + '_')) {
                matchedMainTxn = mainTransactionMap[key];
              }
            });
          }

          if (matchedMainTxn) {
            const mainTxnKey = `${matchedMainTxn.receiver_account || matchedMainTxn.account || ''}_${matchedMainTxn.txn_id || ''}`;
            if (!subTransactionMap[mainTxnKey]) {
              subTransactionMap[mainTxnKey] = [];
            }

            // Check for duplicates before adding sub-transaction
            const isDuplicate = subTransactionMap[mainTxnKey].some(existingSubTxn => {
              return (
                existingSubTxn.type === subTxn.type &&
                existingSubTxn.sender_account === subTxn.sender_account &&
                existingSubTxn.date === subTxn.date &&
                existingSubTxn.amount === subTxn.amount &&
                existingSubTxn.receiver_info === subTxn.receiver_info
              );
            });

            // Only add if not a duplicate
            if (!isDuplicate) {
              subTransactionMap[mainTxnKey].push(subTxn);
            } else {
            }

            // Mark sub-transaction as processed
            const subTxnId = `${subTxn.sender_account || ''}_${subTxn.txn_id || ''}_${subTxn.layer || 0}`;
            processedTransactions.add(subTxnId);
          } else {
            // No matching main transaction found, treat as standalone
            consolidatedTransactions.push(subTxn);
            const subTxnId = `${subTxn.sender_account || ''}_${subTxn.txn_id || ''}_${subTxn.layer || 0}`;
            processedTransactions.add(subTxnId);
          }
        });

        // Group main transactions for consolidation
        const consolidationGroups: { [key: string]: any[] } = {};

        mainTransactions.forEach(txn => {
          if (layer > 0) {
            // Create consolidation key: parent_account + receiver_account + transaction_type
            const consolidationKey = `${txn.sender_account || ''}_${txn.receiver_account || txn.account || ''}_${txn.type}`;

            if (!consolidationGroups[consolidationKey]) {
              consolidationGroups[consolidationKey] = [];
            }
            consolidationGroups[consolidationKey].push(txn);
          } else {
            // Layer 0 transactions (sender nodes) are not consolidated
            consolidatedTransactions.push(txn);
            const txnId = `${txn.sender_account || ''}_${txn.txn_id || ''}_${txn.layer || 0}`;
            processedTransactions.add(txnId);
          }
        });

        // Process consolidation groups and attach sub-transactions
        Object.entries(consolidationGroups).forEach(([, groupTxns]) => {
          if (groupTxns.length === 1) {
            // Single transaction, add as-is with sub-transactions
            const singleTxn = groupTxns[0];
            const mainTxnKey = `${singleTxn.receiver_account || singleTxn.account || ''}_${singleTxn.txn_id || ''}`;

            // Attach sub-transactions if any
            if (subTransactionMap[mainTxnKey]) {
              singleTxn.subTransactions = subTransactionMap[mainTxnKey];
              singleTxn.hasSubTransactions = true;
            }

            consolidatedTransactions.push(singleTxn);
            const txnId = `${singleTxn.sender_account || ''}_${singleTxn.txn_id || ''}_${singleTxn.layer || 0}`;
            processedTransactions.add(txnId);
          } else if (groupTxns.length > 1) {
            // Multiple transactions with same parent and receiver account - consolidate
            const firstTxn = groupTxns[0];
            const totalAmount = groupTxns.reduce((sum, txn) => sum + (parseFloat(txn.amount) || 0), 0);

            // Collect all sub-transactions from all consolidated transactions with duplicate removal
            const allSubTransactions: any[] = [];
            const seenSubTransactions = new Set<string>();

            groupTxns.forEach(txn => {
              const mainTxnKey = `${txn.receiver_account || txn.account || ''}_${txn.txn_id || ''}`;
              if (subTransactionMap[mainTxnKey]) {
                subTransactionMap[mainTxnKey].forEach(subTxn => {
                  // Create a unique key for duplicate detection
                  const subTxnKey = `${subTxn.type}_${subTxn.sender_account}_${subTxn.date}_${subTxn.amount}_${subTxn.receiver_info}`;

                  if (!seenSubTransactions.has(subTxnKey)) {
                    seenSubTransactions.add(subTxnKey);
                    allSubTransactions.push(subTxn);
                  }
                });
              }
            });

            // Create consolidated transaction
            const consolidatedTxn = {
              ...firstTxn,
              amount: totalAmount.toString(),
              isConsolidated: true,
              consolidatedTransactions: groupTxns.map(txn => ({
                txn_id: txn.txn_id || txn.transaction_id || '',
                transaction_id: txn.transaction_id || txn.txn_id || '',
                amount: txn.amount,
                reference: txn.receiver_info || txn.remarks || '',
                date: txn.date
              })),
              // Attach all collected sub-transactions
              subTransactions: allSubTransactions.length > 0 ? allSubTransactions : undefined,
              hasSubTransactions: allSubTransactions.length > 0
            };



            consolidatedTransactions.push(consolidatedTxn);

            // Mark all transactions in this group as processed
            groupTxns.forEach(txn => {
              const txnId = `${txn.sender_account || ''}_${txn.txn_id || ''}_${txn.layer || 0}`;
              processedTransactions.add(txnId);
            });
          }
        });
      });


    return consolidatedTransactions;
  }

  // Process transactions to create graph nodes and edges with TB layout only
  processTransactionsForGraph(
    transactions: any[],
    metadata: any,
    complaintData: any,
    complaintId: string,
    isDarkMode: boolean,
    _horizontalLayout: boolean = false // Only TB layout supported now
  ): { nodes: GraphNode[], edges: GraphEdge[] } {

    // Performance optimization: limit transaction processing for large datasets
    let processedTransactions = transactions;
    if (transactions.length > 500) {
      // For very large datasets, process only the first 500 transactions
      processedTransactions = transactions.slice(0, 500);
    }

    // First, consolidate matching money transfer transactions
    const consolidatedTransactions = this.consolidateMoneyTransferTransactions(processedTransactions);

    // Use TB layout method only
    return this.processTransactionsWithTBLayout(
      consolidatedTransactions,
      metadata,
      complaintData,
      complaintId,
      isDarkMode
    );
  }

  // TB Layout method with proper hierarchy
  private processTransactionsWithTBLayout(
    transactions: any[],
    metadata: any,
    complaintData: any,
    _complaintId: string,
    isDarkMode: boolean
  ): { nodes: GraphNode[], edges: GraphEdge[] } {
    const nodes: GraphNode[] = [];
    const edges: GraphEdge[] = [];

    // Configure Dagre graph for TB layout only
    this.dagreGraph.setGraph({
      rankdir: 'TB',
      // Horizontal spacing between nodes in the same rank/layer
      nodesep: 100,
      // Vertical spacing between ranks/layers
      ranksep: 200,
      // Margins around the entire graph
      marginx: 50,
      marginy: 50,
      // Alignment of nodes within ranks
      align: 'UL'
    });

    // Ensure metadata has required fields
    const processedMetadata = {
      complaint_number: metadata.complaint_number || metadata.COMPLAINT_NUMBER || complaintData?.complaint_number || 'Unknown',
      complainant_name: metadata.complainant_name || metadata.VICTIM_NAME || complaintData?.complainant_name || 'Unknown',
      date_of_complaint: metadata.date || metadata.date_of_complaint || metadata.COMPLAINT_DATE ||
                        (complaintData?.metadata?.date) ||
                        (complaintData?.original_complaint_date) ||
                        (complaintData?.date_of_complaint) ||
                        (complaintData?.date) ||
                        'Unknown',
      total_amount: metadata.total_amount || metadata.amount || metadata.TOTAL_FRAUD_AMOUNT || complaintData?.total_amount || '0',
      ...metadata
    };

    // Group transactions by layer
    const transactionsByLayer: { [key: number]: any[] } = {};
    transactions.forEach(txn => {
      const layer = txn.layer !== undefined ? Number(txn.layer) : 0;
      if (!transactionsByLayer[layer]) {
        transactionsByLayer[layer] = [];
      }
      transactionsByLayer[layer].push(txn);
    });

    // Analyze sender trees for optimal ordering
    const senderAnalysis = this.analyzeSenderTrees(transactions, transactionsByLayer);
    const orderedSenders = senderAnalysis.orderedSenders;

    // Track nodes by account for edge creation
    const nodesByAccount: { [key: string]: string } = {};

    // Create metadata node (root)
    const metadataNodeId = 'metadata_node';
    this.dagreGraph.setNode(metadataNodeId, {
      width: this.NODE_WIDTH,
      height: 120
    });

    nodes.push({
      id: metadataNodeId,
      type: 'metadata',
      data: {
        label: `Complaint: ${processedMetadata.complaint_number}`,
        account: processedMetadata.complainant_name,
        complaint_number: processedMetadata.complaint_number,
        date: processedMetadata.date_of_complaint,
        total_amount: processedMetadata.total_amount,
        complainant_name: processedMetadata.complainant_name,
        COMPLAINT_NUMBER: processedMetadata.complaint_number,
        VICTIM_NAME: processedMetadata.complainant_name,
        COMPLAINT_DATE: processedMetadata.date_of_complaint,
        TOTAL_FRAUD_AMOUNT: processedMetadata.total_amount
      },
      position: { x: 0, y: 0 } // Will be updated by Dagre
    });

    // Create sender nodes
    const senderAccountNodes: { [key: string]: string } = {};
    orderedSenders.forEach(account => {
      const sampleTxn = transactionsByLayer[1]?.find(txn =>
        txn.sender_account === account &&
        (txn.type === 'Money Transfer to' || txn.isMainTransaction === true)
      );
      const bankName = sampleTxn?.sender_bank || 'Unknown Bank';
      const nodeId = `sender_${account.replace(/[^a-zA-Z0-9]/g, '')}`;

      // Add to Dagre graph
      this.dagreGraph.setNode(nodeId, {
        width: this.NODE_WIDTH,
        height: 120
      });
      this.dagreGraph.setEdge(metadataNodeId, nodeId);

      if (!nodesByAccount[account]) {
        nodes.push({
          id: nodeId,
          type: 'transaction',
          data: {
            label: bankName,
            account: account,
            isSenderNode: true,
            isVictim: true,
            sender_account: account,
            sender_bank: bankName,
            type: sampleTxn?.type || 'Money Transfer to',
            layer: 0
          },
          position: { x: 0, y: 0 } // Will be updated by Dagre
        });

        // Create edge to metadata
        edges.push({
          id: `edge_metadata_${nodeId}`,
          source: metadataNodeId,
          target: nodeId,
          type: 'custom',
          animated: false, // We handle animation in CustomEdge
          data: { isParentChild: true },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 20,
            height: 20,
            color: isDarkMode ? '#818cf8' : '#6366f1'
          }
        });

        senderAccountNodes[account] = nodeId;
        nodesByAccount[account] = nodeId;
      }
    });

    return this.processLayersWithTBDagre(
      transactionsByLayer,
      nodes,
      edges,
      nodesByAccount,
      senderAccountNodes,
      isDarkMode
    );
  }

  // Process layers with TB Dagre layout
  private processLayersWithTBDagre(
    transactionsByLayer: { [key: number]: any[] },
    nodes: GraphNode[],
    edges: GraphEdge[],
    nodesByAccount: { [key: string]: string },
    _senderAccountNodes: { [key: string]: string },
    isDarkMode: boolean
  ): { nodes: GraphNode[], edges: GraphEdge[] } {

    // Process each layer starting from layer 1
    Object.keys(transactionsByLayer)
      .map(Number)
      .filter(layer => layer > 0)
      .sort((a, b) => a - b)
      .forEach(layer => {
        const layerTxns = transactionsByLayer[layer];

        // Group transactions by parent for processing
        const nodesByParent: { [key: string]: any[] } = {};
        layerTxns.forEach(txn => {
          const isMainTransaction = txn.isMainTransaction === true || txn.type === 'Money Transfer to' || txn.type === 'Money transfer';
          if (isMainTransaction) {
            const senderAccount = txn.sender_account || '';
            if (senderAccount && nodesByAccount[senderAccount]) {
              if (!nodesByParent[senderAccount]) {
                nodesByParent[senderAccount] = [];
              }
              nodesByParent[senderAccount].push(txn);
            }
          }
        });

        // Create nodes for each transaction
        Object.entries(nodesByParent).forEach(([parentAccount, childTxns]) => {
          const parentNodeId = nodesByAccount[parentAccount];
          if (!parentNodeId) return;

          childTxns.forEach((txn, index) => {
            const nodeId = `layer${layer}_${parentAccount.replace(/[^a-zA-Z0-9]/g, '')}_${index}`;
            const receiverAccount = txn.receiver_account || txn.account;

            // Calculate dynamic height based on content
            const nodeHeight = this.calculateNodeHeight(txn, false); // TB layout

            // Add to Dagre graph with dynamic height
            this.dagreGraph.setNode(nodeId, {
              width: this.NODE_WIDTH,
              height: nodeHeight
            });
            this.dagreGraph.setEdge(parentNodeId, nodeId);

            // Create the node data with all required fields
            const nodeData: any = {
              label: txn.receiver_bank || 'Unknown Bank',
              account: receiverAccount,
              amount: txn.amount,
              date: txn.date,
              txn_id: txn.txn_id || txn.transaction_id || '', // Ensure transaction ID is included
              sender_account: txn.sender_account,
              receiver_info: txn.receiver_info,
              type: txn.type,
              isMainTransaction: true,
              layer: layer,
              parentId: parentNodeId,
              // Add additional fields that might be useful
              sender_bank: txn.sender_bank,
              receiver_bank: txn.receiver_bank
            };

            // Handle consolidated transactions
            if (txn.isConsolidated) {
              nodeData.isConsolidated = true;
              nodeData.consolidatedTransactions = txn.consolidatedTransactions;
              nodeData.totalAmount = txn.amount;
            }

            // Handle sub-transactions
            if (txn.hasSubTransactions && txn.subTransactions && txn.subTransactions.length > 0) {
              nodeData.hasSubTransactions = true;
              nodeData.subTransactions = txn.subTransactions;
            }

            nodes.push({
              id: nodeId,
              type: 'transaction',
              data: nodeData,
              position: { x: 0, y: 0 } // Will be updated by Dagre
            });

            // Store this node for future reference
            if (receiverAccount) {
              nodesByAccount[receiverAccount] = nodeId;
            }

            // Create edge
            edges.push({
              id: `edge_${parentNodeId}_${nodeId}`,
              source: parentNodeId,
              target: nodeId,
              type: 'custom',
              animated: false, // We handle animation in CustomEdge
              data: { isParentChild: true },
              markerEnd: {
                type: MarkerType.ArrowClosed,
                width: 20,
                height: 20,
                color: isDarkMode ? '#818cf8' : '#6366f1'
              }
            });
          });
        });
      });

    // Apply Dagre layout
    dagre.layout(this.dagreGraph);

    // Update node positions based on Dagre layout with proper spacing
    const layoutedNodes = this.applyTBSpacing(nodes);

    return { nodes: layoutedNodes, edges };
  }

  // Apply proper TB spacing based on node content
  private applyTBSpacing(nodes: GraphNode[]): GraphNode[] {
    // Get initial positions from Dagre
    const nodesWithPositions = nodes.map(node => {
      const nodeWithPosition = this.dagreGraph.node(node.id);
      return {
        ...node,
        position: {
          x: nodeWithPosition.x - this.NODE_WIDTH / 2,
          y: nodeWithPosition.y - 60
        },
        dagreY: nodeWithPosition.y // Store original Dagre Y for layer grouping
      };
    });

    // Group nodes by layer (Y position)
    const nodesByLayer: { [key: number]: any[] } = {};

    nodesWithPositions.forEach(node => {
      const layerKey = Math.round(node.dagreY / 100);
      if (!nodesByLayer[layerKey]) {
        nodesByLayer[layerKey] = [];
      }
      nodesByLayer[layerKey].push(node);
    });

    // Sort layers
    const sortedLayers = Object.keys(nodesByLayer)
      .map(Number)
      .sort((a, b) => a - b);

    let cumulativeOffset = 0;

    // Apply dynamic spacing layer by layer with improved calculation
    sortedLayers.forEach((layerKey, layerIndex) => {
      const layerNodes = nodesByLayer[layerKey];

      // Calculate maximum expanded height in this layer using the new method
      let maxExpandedHeight = 0;
      layerNodes.forEach(node => {
        const expandedHeight = this.calculateMaxExpandedHeight(node.data, false); // TB layout
        maxExpandedHeight = Math.max(maxExpandedHeight, expandedHeight);
      });

      // Calculate additional spacing needed for this layer based on expanded content
      // Use a more aggressive spacing calculation to prevent overlapping for complex nodes
      const baseSpacing = 250; // Increased minimum spacing between layers
      const contentBasedSpacing = Math.max(0, maxExpandedHeight - 300); // Additional spacing for content beyond base height
      const additionalSpacing = Math.min(baseSpacing + contentBasedSpacing * 0.8, 600); // Increased multiplier and cap

      // Apply offset to all nodes in this layer and subsequent layers
      if (layerIndex > 0) {
        cumulativeOffset += additionalSpacing;
      }

      // Update positions for nodes in this layer
      layerNodes.forEach(node => {
        node.position.y += cumulativeOffset;
      });
    });

    // Apply hierarchical centering after spacing adjustments
    const centeredNodes = this.applyCenteringLogic(nodesWithPositions);

    return centeredNodes.map(node => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position
    }));
  }

  // Apply hierarchical centering logic to position parent nodes in the center of their children
  private applyCenteringLogic(nodes: any[]): any[] {
    // Create a map of all edges to track parent-child relationships
    const parentChildMap: { [parentId: string]: string[] } = {};
    const childParentMap: { [childId: string]: string } = {};

    // Build parent-child relationships from the Dagre graph edges
    this.dagreGraph.edges().forEach(edge => {
      const parentId = edge.v;
      const childId = edge.w;

      if (!parentChildMap[parentId]) {
        parentChildMap[parentId] = [];
      }
      parentChildMap[parentId].push(childId);
      childParentMap[childId] = parentId;
    });

    // Create a copy of nodes to modify
    const centeredNodes = [...nodes];
    const nodeMap = new Map(centeredNodes.map(node => [node.id, node]));

    // Function to center a parent node relative to its children
    const centerParentNode = (parentId: string) => {
      const children = parentChildMap[parentId];
      if (!children || children.length === 0) return;

      const parentNode = nodeMap.get(parentId);
      if (!parentNode) return;

      // Get all child nodes
      const childNodes = children
        .map(childId => nodeMap.get(childId))
        .filter(node => node !== undefined);

      if (childNodes.length === 0) return;

      // Calculate the horizontal center of all children
      const childXPositions = childNodes.map(child => child.position.x);
      const minX = Math.min(...childXPositions);
      const maxX = Math.max(...childXPositions);
      const centerX = (minX + maxX) / 2;

      // Update parent's X position to be centered
      parentNode.position.x = centerX;
    };

    // Process nodes layer by layer from bottom to top to ensure proper centering
    // Group nodes by their Y position (layer)
    const nodesByY: { [y: number]: any[] } = {};
    centeredNodes.forEach(node => {
      const yKey = Math.round(node.position.y / 50); // Group by approximate Y position
      if (!nodesByY[yKey]) {
        nodesByY[yKey] = [];
      }
      nodesByY[yKey].push(node);
    });

    // Sort layers from bottom to top (highest Y to lowest Y)
    const sortedYLayers = Object.keys(nodesByY)
      .map(Number)
      .sort((a, b) => b - a);

    // Process each layer from bottom to top
    sortedYLayers.forEach(yLayer => {
      const layerNodes = nodesByY[yLayer];

      // For each node in this layer, center its parent if it has one
      layerNodes.forEach(node => {
        const parentId = childParentMap[node.id];
        if (parentId) {
          centerParentNode(parentId);
        }
      });
    });

    return centeredNodes;
  }



  // Calculate dynamic node height based on content - improved for better spacing
  private calculateNodeHeight(txn: any, horizontalLayout: boolean): number {
    const baseHeight = horizontalLayout ? 120 : 180;
    let additionalHeight = 0;

    // Add height for consolidated transactions (more accurate calculation)
    if (txn.isConsolidated && txn.consolidatedTransactions) {
      // Each consolidated transaction needs more space for txn_id, amount, reference, date
      additionalHeight += txn.consolidatedTransactions.length * 60; // Increased from 40px to 60px
    }

    // Add height for sub-transactions (more accurate calculation)
    if (txn.hasSubTransactions && txn.subTransactions) {
      // Each sub-transaction needs space for date, amount, type, reference
      additionalHeight += txn.subTransactions.length * 50; // Increased from 35px to 50px
    }

    // Add extra padding for txn_id display in Money Transfer nodes (simple inline layout)
    if ((txn.type === 'Money Transfer to' || txn.type === 'Money transfer') && (txn.txn_id || txn.transaction_id) && !txn.isConsolidated) {
      additionalHeight += 25; // Extra space for prominent txn_id display with inline layout
    }

    return baseHeight + additionalHeight;
  }

  // Calculate the maximum expanded height for a node (used for spacing calculations)
  private calculateMaxExpandedHeight(nodeData: any, horizontalLayout: boolean): number {
    const baseHeight = horizontalLayout ? 120 : 180;
    let expandedHeight = baseHeight;

    // Add height for basic expanded content (date, sender details, type, reference, layer)
    expandedHeight += 120; // Base expanded content

    // Add height for txn_id display in Money Transfer nodes (simple inline layout, only for non-consolidated)
    if ((nodeData.type === 'Money Transfer to' || nodeData.type === 'Money transfer') && (nodeData.txn_id || nodeData.transaction_id) && !nodeData.isConsolidated) {
      expandedHeight += 30; // Space for txn_id with inline layout
    }

    // Add height for consolidated transactions when expanded - more accurate calculation
    if (nodeData.isConsolidated && nodeData.consolidatedTransactions) {
      expandedHeight += 30; // Reduced header space for "Multiple Transactions" section
      // More accurate per-transaction calculation: txn_id + amount + reference + date
      expandedHeight += nodeData.consolidatedTransactions.length * 100; // Increased space per consolidated transaction
    }

    // Add height for sub-transactions when expanded - more accurate calculation
    if (nodeData.hasSubTransactions && nodeData.subTransactions) {
      expandedHeight += 30; // Reduced header space for "Sub-Transactions" section
      // More accurate per-sub-transaction calculation: date + amount + type + reference
      expandedHeight += nodeData.subTransactions.length * 90; // Increased space per sub-transaction
    }

    // Add extra spacing if node has both consolidated and sub-transactions
    if (nodeData.isConsolidated && nodeData.hasSubTransactions) {
      expandedHeight += 50; // Extra spacing for complex nodes
    }

    return expandedHeight;
  }
}