import React, { useState, useEffect } from 'react';
import authService from '../services/authService';
import { useAlert } from '../context/TailwindAlertContext';
import { FiLock } from 'react-icons/fi';


interface OTPVerificationProps {
  email: string;
  onVerified: (otp: string) => void;
  onCancel: () => void;
}

const OTPVerification: React.FC<OTPVerificationProps> = ({ email, onVerified, onCancel }) => {
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [remainingTime, setRemainingTime] = useState<number>(5 * 60); // 5 minutes in seconds for resend countdown
  const [canResend, setCanResend] = useState<boolean>(false);
  const [otpError, setOtpError] = useState<string>('');
  const { showSuccess, showError, showInfo } = useAlert();

  // Timer for OTP expiration
  useEffect(() => {
    if (remainingTime > 0 && !canResend) {
      const timer = setTimeout(() => {
        setRemainingTime(prev => prev - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (remainingTime <= 0 && !canResend) {
      setCanResend(true);
      showInfo('OTP has expired. Please request a new one.');
    }
  }, [remainingTime, canResend, showInfo]);

  // Format remaining time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      onVerified(otp);
    } catch (error) {
      showError('Failed to verify OTP');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    setOtpError('');

    try {
      // Show loading message
      showInfo('Sending new OTP...');

      const response = await authService.api.post('/auth/resend-otp', null, {
        params: { email },
        timeout: 30000 // 30 seconds timeout for email sending
      });

      if (response.data && response.data.message) {
        // Reset the timer to 5 minutes for resend countdown
        setRemainingTime(5 * 60); // 5 minutes
        setCanResend(false);

        // Clear the OTP input
        setOtp('');

        showSuccess('New OTP has been sent to your email');
      } else {
        showError('Unexpected response from server. Please try again.');
      }
    } catch (error: any) {

      // Handle different error types
      if (error.response) {
        const status = error.response.status;
        const detail = error.response.data?.detail || '';

        if (status === 404) {
          setOtpError('User not found. Please check your email address.');
          showError('User not found. Please check your email address.');
        } else if (status === 429) {
          setOtpError('Too many requests. Please wait before requesting another OTP.');
          showError('Too many requests. Please wait before requesting another OTP.');
        } else if (status === 500) {
          setOtpError('Server error. Please try again later.');
          showError('Server error. Please try again later.');
        } else {
          setOtpError(detail || 'Failed to resend OTP. Please try again.');
          showError(detail || 'Failed to resend OTP. Please try again.');
        }
      } else if (error.request) {
        setOtpError('No response from server. Please check your internet connection.');
        showError('No response from server. Please check your internet connection.');
      } else {
        setOtpError('Error setting up request. Please try again.');
        showError('Error setting up request. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white/80 dark:bg-[rgba(16,16,30,0.6)] backdrop-blur-md p-6 rounded-lg shadow-md w-full max-w-md mx-auto border border-blue-200/50 dark:border-[rgba(0,170,255,0.3)] dark:shadow-glow-blue-sm">
      <h2 className="text-2xl font-bold mb-6 text-center text-gray-800 dark:text-white">
        Verify Your Email
      </h2>

      <p className="text-gray-600 dark:text-gray-300 mb-4 text-center">
        We've sent a verification code to <strong>{email}</strong>.
        Please enter the code below to verify your email address.
      </p>

      {/* OTP Timer */}
      <div className="mb-4 text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {remainingTime > 0 ? (
            <>Code expires in: <span className="font-medium text-blue-600 dark:text-blue-400">{formatTime(remainingTime)}</span></>
          ) : (
            <span className="text-red-500 dark:text-red-400">Code has expired. Please request a new one.</span>
          )}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="otp" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Verification Code
          </label>
          <div className="relative">
            <div className="absolute top-1/2 -translate-y-1/2 left-0 pl-2.5 flex items-center pointer-events-none" style={{ paddingLeft: 'calc(var(--input-icon-spacing, 10px) / 4)' }}>
              <span className="flex items-center justify-center w-4 h-4">
                <FiLock className="h-3.5 w-3.5 text-gray-400" />
              </span>
            </div>
            <input
              id="otp"
              type="text"
              value={otp}
              onChange={(e) => {
                // Allow alphanumeric characters and limit to 6 characters
                const value = e.target.value.replace(/[^A-Z0-9]/gi, '').slice(0, 6).toUpperCase();
                setOtp(value);
                // Clear error when user types
                if (otpError) setOtpError('');
              }}
              placeholder="Enter 6-character code"
              className={`block w-full pl-10 pr-3 py-2 rounded-md border ${
                otpError ? 'border-red-500 dark:border-red-500' : 'border-blue-200/50 dark:border-[rgba(0,170,255,0.3)]'
              } focus:ring-blue-500 focus:border-blue-500 bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-2 tracking-wider font-mono backdrop-blur-sm`}
              style={{
                letterSpacing: '0.25em',
                paddingLeft: 'var(--input-icon-spacing, 10px)'
              }}
              maxLength={6}
              autoComplete="one-time-code"
              inputMode="text"
              autoCapitalize="characters"
              spellCheck="false"
              autoCorrect="off"
              required
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Code contains both letters and numbers. Enter exactly as shown in the email.
            </p>
          </div>
          {/* Error message */}
          {otpError && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{otpError}</p>
          )}
        </div>

        <div className="flex flex-col space-y-2">
          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center items-center py-2.5 px-4 rounded-md shadow-md text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-500/20 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : 'Verify Email'}
          </button>

          <div className="flex justify-between items-center">
            <div className="flex flex-col">
              <button
                type="button"
                onClick={handleResendOTP}
                disabled={isLoading || (!canResend && remainingTime > 0)}
                className={`text-sm font-medium ${
                  isLoading || (!canResend && remainingTime > 0)
                    ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                    : 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
                }`}
              >
                {isLoading ? 'Sending...' : 'Resend Code'}
              </button>
              {!canResend && remainingTime > 0 && (
                <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Wait {Math.floor(remainingTime / 60)}:{(remainingTime % 60).toString().padStart(2, '0')} to resend
                </span>
              )}
            </div>

            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                disabled={isLoading}
                className={`text-sm font-medium ${
                  isLoading
                    ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                    : 'text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                Back to Login
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default OTPVerification;
