import React from 'react';
import { FiSettings } from 'react-icons/fi';
import { twMerge } from 'tailwind-merge';

interface ThemeToggleProps {
  className?: string;
  variant?: 'icon' | 'button';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  variant = 'icon',
  size = 'md',
  onClick,
  ...props
}) => {
  // Size styles
  const sizeStyles = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  // Icon sizes
  const iconSizes = {
    sm: { width: 14, height: 14 },
    md: { width: 16, height: 16 },
    lg: { width: 20, height: 20 },
  };

  // Render icon variant
  if (variant === 'icon') {
    return (
      <button
        type="button"
        onClick={onClick}
        className={twMerge(
          'p-1.5 rounded-full transition-all duration-200 transform hover:scale-110',
          'bg-transparent hover:shadow-sm',
          sizeStyles[size],
          className
        )}
        style={{
          color: 'var(--theme-text)',
          backgroundColor: 'var(--theme-bg-card)',
          borderColor: 'var(--theme-border)',
          boxShadow: '0 0 5px var(--theme-glow)'
        }}
        aria-label="Theme settings"
        {...props}
      >
        <FiSettings className="w-3.5 h-3.5" style={{ color: 'var(--theme-accent)' }} />
      </button>
    );
  }

  // Render button variant
  return (
    <button
      type="button"
      onClick={onClick}
      className={twMerge(
        'flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-200',
        'shadow-sm hover:shadow',
        'transform hover:translate-y-[-1px]',
        sizeStyles[size],
        className
      )}
      style={{
        backgroundColor: 'var(--theme-bg-card)',
        color: 'var(--theme-text)',
        borderColor: 'var(--theme-border)',
        border: '1px solid var(--theme-border)'
      }}
      aria-label="Theme settings"
      {...props}
    >
      <FiSettings style={iconSizes[size]} />
      <span>Theme Settings</span>
    </button>
  );
};

export default ThemeToggle;
