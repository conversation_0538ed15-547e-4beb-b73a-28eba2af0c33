import React from 'react';
import { twMerge } from 'tailwind-merge';
import Spinner from '../common/Spinner';

interface LoadingDialogProps {
  open: boolean;
  message: string;
  progress?: number;
  className?: string;
}

const LoadingDialog: React.FC<LoadingDialogProps> = ({
  open,
  message,
  progress,
  className,
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm animate-fade-in">
      <div
        className={twMerge(
          'bg-white/90 dark:bg-[rgba(16,16,30,0.8)] backdrop-blur-lg rounded-xl shadow-xl dark:shadow-glow-blue-sm w-full max-w-md border border-gray-100/50 dark:border-[rgba(0,170,255,0.3)] animate-slide-up p-6',
          className
        )}
      >
        {/* Subtle scan line effect in dark mode */}
        <div className="absolute inset-0 overflow-hidden rounded-xl opacity-0 dark:opacity-10 pointer-events-none">
          <div className="w-full h-full animate-scan-line bg-gradient-to-b from-transparent via-primary-400/20 to-transparent"></div>
        </div>

        <div className="flex flex-col items-center justify-center text-center">
          <div className="mb-4">
            <Spinner size="lg" color="primary" />
          </div>
          
          <h3 className="text-xl font-bold mb-2 dark:cyber-text-gradient">
            Processing Complaint
          </h3>
          
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            {message}
          </p>
          
          {progress !== undefined && (
            <div className="w-full mt-2">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
                <div
                  className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                {progress}% Complete
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoadingDialog;
