import React, { useEffect, useRef } from 'react';
import { FiX } from 'react-icons/fi';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { twMerge } from 'tailwind-merge';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'sm',
  className
}) => {
  const { isDark } = useThemeContext();
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Close modal on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  // Size classes
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-md animate-fade-in">
      <div
        ref={modalRef}
        className={twMerge(
          `w-full ${sizeClasses[size]} rounded-xl shadow-xl overflow-hidden animate-slide-up`,
          isDark
            ? 'bg-[rgba(16,16,30,0.8)] text-white backdrop-blur-lg border border-[rgba(0,170,255,0.3)] dark:shadow-glow-blue-sm'
            : 'bg-white/90 text-gray-900 backdrop-blur-md border border-gray-100/50',
          className
        )}
      >
        {/* Subtle scan line effect in dark mode */}
        {isDark && (
          <div className="absolute inset-0 overflow-hidden opacity-10 pointer-events-none">
            <div className="w-full h-full animate-scan-line bg-gradient-to-b from-transparent via-primary-400/20 to-transparent"></div>
          </div>
        )}

        {title && (
          <div className={`px-6 py-4 border-b ${
            isDark ? 'border-[rgba(0,170,255,0.2)] bg-[rgba(16,16,30,0.5)]' : 'border-gray-200/30 bg-gray-50/50'
          }`}>
            <div className="flex justify-between items-center">
              <h3 className={`text-lg font-bold ${isDark ? 'cyber-text-gradient' : ''}`}>{title}</h3>
              <button
                onClick={onClose}
                className={`p-1.5 rounded-full transition-all duration-200 transform hover:scale-110 ${
                  isDark
                    ? 'text-primary-400 hover:text-primary-300 hover:bg-[rgba(0,170,255,0.1)] hover:shadow-glow-blue-sm'
                    : 'text-gray-400 hover:text-gray-500 hover:bg-gray-100/80'
                }`}
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
        <div className="p-6">{children}</div>
      </div>
    </div>
  );
};

export default Modal;
