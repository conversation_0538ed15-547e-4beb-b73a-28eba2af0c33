import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import authService from '../services/authService';
import { twMerge } from 'tailwind-merge';

interface ResendVerificationButtonProps {
  email?: string;
  className?: string;
}

const ResendVerificationButton: React.FC<ResendVerificationButtonProps> = ({
  email,
  className = '',
}) => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { showSuccess, showError } = useAlert();

  const handleResendVerification = async () => {
    if (loading) return;

    setLoading(true);

    try {
      // If email is provided, use it directly
      if (email) {
        await authService.api.post('/auth/resend-verification', { email }, {
          timeout: 30000 // 30 seconds timeout for email sending
        });
        showSuccess('Verification email sent! Please check your inbox.');
      } else {
        // If no email provided, redirect to the resend verification page
        navigate('/resend-verification');
        return;
      }
    } catch (error: any) {
      showError(error.response?.data?.detail || 'Failed to resend verification email');
    } finally {
      setLoading(false);
    }
  };

  const buttonClasses = twMerge(
    'inline-block px-4 py-2 bg-blue-600 text-white font-medium rounded hover:bg-blue-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed',
    className
  );

  return (
    <button
      onClick={handleResendVerification}
      disabled={loading}
      className={buttonClasses}
    >
      {loading ? (
        <span className="flex items-center justify-center">
          <span className="mr-2 h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></span>
          Sending...
        </span>
      ) : (
        'Resend Verification Email'
      )}
    </button>
  );
};

export default ResendVerificationButton;
