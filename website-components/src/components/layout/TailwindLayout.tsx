import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PageTransition from '../PageTransition';
import { useLayout } from '../../context/LayoutContext';
import { useAuth } from '../../context/AuthContext';
import { useAlert } from '../../context/TailwindAlertContext';
import { useSubscriptionGuard } from '../../hooks/useSubscriptionGuard';
import logoImage from '../../assets/logo.webp';

// Icons
import {
  FiHome,
  FiUpload,
  FiUser,
  FiSearch,
  // FiDatabase,
  FiSettings,
  FiLogOut,
  FiLink2,
} from 'react-icons/fi';

interface LayoutProps {
  children: React.ReactNode;
}

const TailwindLayout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { layoutConfig } = useLayout();
  const { logout, user } = useAuth();
  const { showInfo, showError } = useAlert();
  const { guardedNavigate } = useSubscriptionGuard();

  const [sidebarOpen, setSidebarOpen] = useState(!layoutConfig.hideSidebar);
  const [isMobile, setIsMobile] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Check if subscription is expired
  const isSubscriptionExpired = user ? (() => {
    const now = new Date();
    const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;
    return !user.paid || (subscriptionExpires && subscriptionExpires < now);
  })() : false;

  // Check if mobile on mount and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarOpen(false);
      }
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Update sidebar state when layout config changes
  useEffect(() => {
    if (layoutConfig.hideSidebar) {
      setSidebarOpen(false);
    } else if (!isMobile) {
      setSidebarOpen(true);
    }
  }, [layoutConfig.hideSidebar, isMobile]);

  // Get active route
  const getActiveRoute = (path: string) => {
    return location.pathname === path;
  };



  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Sign out handler
  const handleSignOut = async () => {
    try {
      setIsLoggingOut(true);
      showInfo('Signing out...');

      // The logout function will handle the redirect to login page
      await logout();

      // If for some reason we're still here, manually navigate
      // This is a fallback that likely won't execute
      setTimeout(() => {
        navigate('/login', { replace: true });
      }, 500);
    } catch (error) {
      showError('Failed to sign out. Please try again.');
      setIsLoggingOut(false);
    }
  };

  // Navigation items
  const navItems = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: <FiHome className="w-4 h-4" />,
    },
    {
      name: 'Upload Complaints',
      path: '/complaint-upload',
      icon: <FiUpload className="w-4 h-4" />,
    },
    {
      name: 'Complaint Analysis',
      path: '/complaint-analysis',
      icon: <FiLink2 className="w-4 h-4" />,
    },
    {
      name: 'Nodal/ATM Search',
      path: '/information-search',
      icon: <FiSearch className="w-4 h-4" />,
    },

  ];

  const bottomNavItems = [
    {
      name: 'Profile',
      path: '/profile',
      icon: <FiUser className="w-4 h-4" />,
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: <FiSettings className="w-4 h-4" />,
    },
  ];

  return (
    <div className="flex h-screen w-full overflow-hidden relative">
      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 z-10 flex flex-col flex-shrink-0 transition-all duration-300 ease-in-out transform ${
          sidebarOpen ? 'w-52' : 'w-12'
        } ${
          isMobile ? (sidebarOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'
        } shadow-xl group`}
        style={{
          backgroundColor: 'var(--theme-sidebar-darker)',
          borderRight: '2px solid var(--theme-border)',
          color: 'var(--theme-text)',
          boxShadow: '0 0 20px var(--theme-glow)'
        }}
      >
        {/* Sidebar header without toggle button */}
        <div
          className={`flex items-center ${sidebarOpen ? 'justify-start' : 'justify-center'} h-14 px-3 border-b`}
          style={{
            borderColor: 'var(--theme-border)',
            backgroundColor: 'var(--theme-sidebar-darker)',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}
        >
          {sidebarOpen ? (
            <div className="flex items-center">
              <div
                className="w-9 h-9 rounded-full flex items-center justify-center shadow-md mr-3"
                style={{
                  backgroundColor: 'var(--theme-button)',
                  boxShadow: '0 0 10px var(--theme-glow)'
                }}
              >
                <img
                  src={logoImage}
                  alt="NCRP Matrix Logo"
                  className="w-6 h-6 object-contain rounded-full"
                />
              </div>
              <span className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
                NCRP Matrix
              </span>
            </div>
          ) : (
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center shadow-md"
              style={{
                backgroundColor: 'var(--theme-button)',
                boxShadow: '0 0 10px var(--theme-glow)'
              }}
            >
              <img
                src={logoImage}
                alt="NCRP Matrix Logo"
                className="w-5 h-5 object-contain rounded-full"
              />
            </div>
          )}
        </div>

        {/* Google Maps style toggle button - positioned in the middle of the sidebar edge */}
        <button
          onClick={toggleSidebar}
          className={`absolute top-1/2 ${sidebarOpen ? '-right-3' : '-right-3'} transform -translate-y-1/2 rounded-full
          flex items-center justify-center w-7 h-14 shadow-md sidebar-toggle-btn`}
          style={{
            backgroundColor: 'var(--theme-bg-card)',
            color: 'var(--theme-text)',
            border: '1px solid var(--theme-border)',
            zIndex: 30,
            boxShadow: '0 0 8px var(--theme-glow)'
          }}
          aria-label={sidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="m15 18-6-6 6-6" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="m9 18 6-6-6-6" />
            </svg>
          )}
        </button>

        {/* Sidebar content */}
        <div className="flex flex-col flex-1 overflow-y-auto pt-4">
          <nav className={`flex-1 ${sidebarOpen ? 'px-2' : 'px-1'} space-y-2`}>
            {navItems.map((item) => {
              const isDisabled = isSubscriptionExpired;

              return (
                <div
                  key={item.path}
                  onClick={() => {
                    if (isDisabled) {
                      showError('Your subscription has expired. Please renew to continue using the application.');
                      return;
                    }
                    guardedNavigate(item.path);
                  }}
                  className={`flex items-center ${sidebarOpen ? 'px-3' : 'px-1'} py-2 rounded-lg transition-all duration-300 ${
                    !isDisabled ? 'hover:-translate-y-0.5 cursor-pointer' : 'cursor-not-allowed opacity-50'
                  } ${
                    getActiveRoute(item.path) ? 'font-medium shadow-md' : ''
                  }`}
                  style={{
                    backgroundColor: getActiveRoute(item.path) ? 'var(--theme-bg-primary)' : 'transparent',
                    color: isDisabled ? 'var(--theme-text-muted)' : 'var(--theme-text)',
                    boxShadow: getActiveRoute(item.path) ? '0 0 12px var(--theme-glow)' : 'none',
                    borderLeft: getActiveRoute(item.path) ? '3px solid var(--theme-button)' : 'none'
                  }}
                >
                  <div className={`flex items-center w-full ${sidebarOpen ? '' : 'justify-center'}`}>
                    <div
                      className={`flex items-center justify-center w-5 h-5 transition-all duration-300 ${getActiveRoute(item.path) ? 'transform scale-110' : ''}`}
                      style={{ color: isDisabled ? 'var(--theme-text-muted)' : 'var(--theme-text)' }}
                    >
                      {item.icon}
                    </div>
                    {sidebarOpen && (
                      <span className={`ml-2 transition-all duration-300 ${getActiveRoute(item.path) ? 'font-medium' : ''}`}
                      style={{ color: isDisabled ? 'var(--theme-text-muted)' : 'var(--theme-text)' }}>
                        {item.name}
                        {isDisabled && (
                          <span className="ml-1 text-xs opacity-75">🔒</span>
                        )}
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </nav>

          {/* Bottom Navigation */}
          <div className="mt-auto">
            <div className={`${sidebarOpen ? 'px-2' : 'px-1'} py-3 mb-2`}>
              <div className="border-t my-2" style={{ borderColor: 'var(--theme-border)' }}></div>
              <nav className="space-y-2 mt-2">
                {bottomNavItems.map((item) => {
                  // Profile and Settings should still be accessible even with expired subscription
                  const isDisabled = false; // Allow access to profile/settings

                  return (
                    <div
                      key={item.path}
                      onClick={() => {
                        if (isDisabled) {
                          showError('Your subscription has expired. Please renew to continue using the application.');
                          return;
                        }
                        guardedNavigate(item.path);
                      }}
                      className={`flex items-center ${sidebarOpen ? 'px-3' : 'px-1'} py-2 rounded-lg transition-all duration-300 ${
                        !isDisabled ? 'hover:-translate-y-0.5 cursor-pointer' : 'cursor-not-allowed opacity-50'
                      } ${
                        getActiveRoute(item.path) ? 'font-medium shadow-md' : ''
                      }`}
                      style={{
                        backgroundColor: getActiveRoute(item.path) ? 'var(--theme-bg-primary)' : 'transparent',
                        color: isDisabled ? 'var(--theme-text-muted)' : 'var(--theme-text)',
                        boxShadow: getActiveRoute(item.path) ? '0 0 12px var(--theme-glow)' : 'none',
                        borderLeft: getActiveRoute(item.path) ? '3px solid var(--theme-button)' : 'none'
                      }}
                    >
                      <div className={`flex items-center w-full ${sidebarOpen ? '' : 'justify-center'}`}>
                        <div
                          className={`flex items-center justify-center w-5 h-5 transition-all duration-300 ${getActiveRoute(item.path) ? 'transform scale-110' : ''}`}
                          style={{ color: isDisabled ? 'var(--theme-text-muted)' : 'var(--theme-text)' }}
                        >
                          {item.icon}
                        </div>
                        {sidebarOpen && (
                          <span className={`ml-2 transition-all duration-300 ${getActiveRoute(item.path) ? 'font-medium' : ''}`}
                          style={{ color: isDisabled ? 'var(--theme-text-muted)' : 'var(--theme-text)' }}>
                            {item.name}
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}

                {/* Sign Out Button */}
                <button
                  onClick={handleSignOut}
                  disabled={isLoggingOut}
                  className={`flex items-center w-full ${sidebarOpen ? 'px-3' : 'px-1'} py-2 rounded-lg transition-all duration-300 hover:-translate-y-0.5 hover:bg-red-100/20 dark:hover:bg-red-900/20 ${isLoggingOut ? 'opacity-70 cursor-not-allowed' : ''}`}
                  style={{
                    color: 'var(--theme-text)',
                  }}
                >
                  <div className={`flex items-center w-full ${sidebarOpen ? '' : 'justify-center'}`}>
                    <div
                      className={`flex items-center justify-center w-5 h-5 transition-all duration-300 ${isLoggingOut ? 'animate-pulse' : ''}`}
                      style={{ color: 'var(--theme-text)' }}
                    >
                      <FiLogOut className="w-4 h-4" />
                    </div>
                    {sidebarOpen && (
                      <span className="ml-2 transition-all duration-300"
                      style={{ color: 'var(--theme-text)' }}>
                        {isLoggingOut ? 'Signing Out...' : 'Sign Out'}
                      </span>
                    )}
                  </div>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </aside>

      {/* Mobile sidebar backdrop */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 z-5 bg-black/60 backdrop-blur-sm"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Main content - Fixed layout issues */}
      <div
        className="flex flex-col flex-1 transition-all duration-300 h-screen"
        style={{
          // Use customLeftMargin if provided, otherwise use the default logic
          marginLeft: layoutConfig.customLeftMargin !== undefined
            ? layoutConfig.customLeftMargin
            : (layoutConfig.fullWidth || isMobile ? '0' : (sidebarOpen ? '14rem' : '3.5rem')),
          width: layoutConfig.fullWidth || isMobile ? '100%' : `calc(100% - ${sidebarOpen ? '14rem' : '3.5rem'})`,
          maxWidth: layoutConfig.fullWidth || isMobile ? '100vw' : `calc(100vw - ${sidebarOpen ? '14rem' : '3.5rem'})`
        }}
      >
        {/* Page content with improved transitions */}
        <main className="flex-1 overflow-y-auto w-full min-w-0 max-w-full h-screen">
          <div
            className="w-full h-full min-w-0 max-w-full transition-all duration-300 ease-in-out"
            style={{
              padding: layoutConfig.customPadding || '0',
              background: layoutConfig.customBackground || 'transparent',
              // Remove will-change to fix memory consumption warning
              willChange: 'auto'
            }}
          >
            <PageTransition>
              {children}
            </PageTransition>
          </div>
        </main>
      </div>

      {/* Decorative elements removed to fix layout issues */}
    </div>
  );
};

export default TailwindLayout;
