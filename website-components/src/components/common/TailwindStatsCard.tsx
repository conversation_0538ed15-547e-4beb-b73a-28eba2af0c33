import React from 'react';
import { twMerge } from 'tailwind-merge';
import { FiArrowUp, FiArrowDown } from 'react-icons/fi';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: 'blue' | 'green' | 'amber' | 'red' | 'purple' | 'indigo';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

const TailwindStatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color = 'blue',
  trend,
  className
}) => {
  // Color variants
  const colorVariants = {
    blue: {
      iconBg: 'bg-blue-100 dark:bg-blue-900/30',
      iconText: 'text-blue-600 dark:text-blue-400',
      border: 'border-blue-200 dark:border-blue-800/50',
      gradient: 'bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/10 dark:to-gray-800/5',
      shadow: 'hover:shadow-blue-500/10',
    },
    green: {
      iconBg: 'bg-green-100 dark:bg-green-900/30',
      iconText: 'text-green-600 dark:text-green-400',
      border: 'border-green-200 dark:border-green-800/50',
      gradient: 'bg-gradient-to-br from-green-50 to-white dark:from-green-900/10 dark:to-gray-800/5',
      shadow: 'hover:shadow-green-500/10',
    },
    amber: {
      iconBg: 'bg-amber-100 dark:bg-amber-900/30',
      iconText: 'text-amber-600 dark:text-amber-400',
      border: 'border-amber-200 dark:border-amber-800/50',
      gradient: 'bg-gradient-to-br from-amber-50 to-white dark:from-amber-900/10 dark:to-gray-800/5',
      shadow: 'hover:shadow-amber-500/10',
    },
    red: {
      iconBg: 'bg-red-100 dark:bg-red-900/30',
      iconText: 'text-red-600 dark:text-red-400',
      border: 'border-red-200 dark:border-red-800/50',
      gradient: 'bg-gradient-to-br from-red-50 to-white dark:from-red-900/10 dark:to-gray-800/5',
      shadow: 'hover:shadow-red-500/10',
    },
    purple: {
      iconBg: 'bg-purple-100 dark:bg-purple-900/30',
      iconText: 'text-purple-600 dark:text-purple-400',
      border: 'border-purple-200 dark:border-purple-800/50',
      gradient: 'bg-gradient-to-br from-purple-50 to-white dark:from-purple-900/10 dark:to-gray-800/5',
      shadow: 'hover:shadow-purple-500/10',
    },
    indigo: {
      iconBg: 'bg-indigo-100 dark:bg-indigo-900/30',
      iconText: 'text-indigo-600 dark:text-indigo-400',
      border: 'border-indigo-200 dark:border-indigo-800/50',
      gradient: 'bg-gradient-to-br from-indigo-50 to-white dark:from-indigo-900/10 dark:to-gray-800/5',
      shadow: 'hover:shadow-indigo-500/10',
    }
  };

  const colorStyle = colorVariants[color];

  return (
    <div
      className={twMerge(
        'p-5 rounded-xl h-full flex items-center border backdrop-blur-sm',
        colorStyle.border,
        colorStyle.gradient,
        'transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg',
        colorStyle.shadow,
        className
      )}
    >
      <div
        className={twMerge(
          'mr-4 p-3 rounded-full',
          colorStyle.iconBg
        )}
      >
        {React.cloneElement(icon as React.ReactElement, {
          className: twMerge('w-6 h-6', colorStyle.iconText)
        } as any)}
      </div>

      <div>
        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
          {title}
        </p>

        <div className="flex items-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mr-2">
            {value}
          </h3>

          {trend && (
            <div
              className={twMerge(
                'flex items-center text-xs font-medium rounded-full px-2 py-0.5',
                trend.isPositive
                  ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30'
                  : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
              )}
            >
              {trend.isPositive ? (
                <FiArrowUp className="w-3 h-3 mr-1" />
              ) : (
                <FiArrowDown className="w-3 h-3 mr-1" />
              )}
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TailwindStatsCard;
