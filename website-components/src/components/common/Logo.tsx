import React from 'react';
import { useThemeContext } from '../../context/TailwindThemeContext';
import logoImage from '../../assets/logo.webp';

interface LogoProps {
  size?: number;
  withGlow?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 40, withGlow = false, className = '' }) => {
  const { isDark } = useThemeContext();

  // Convert size to string with px for inline styles
  const sizeStyle = `${size}px`;

  return (
    <div
      className={`flex items-center justify-center relative ${className}`}
      style={{ width: sizeStyle, height: sizeStyle }}
    >
      {withGlow && (
        <div
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full -z-10 animate-pulse ${
            isDark
              ? 'bg-gradient-radial from-blue-400/20 to-blue-400/0'
              : 'bg-gradient-radial from-blue-500/10 to-blue-500/0'
          }`}
          style={{
            width: `${size * 1.5}px`,
            height: `${size * 1.5}px`
          }}
        />
      )}

      <img
        src={logoImage}
        alt="NCRP Matrix Logo"
        className={`object-contain rounded-full ${isDark ? 'drop-shadow-blue-light' : 'drop-shadow-blue'}`}
        style={{ width: sizeStyle, height: sizeStyle }}
      />
    </div>
  );
};

export default Logo;
