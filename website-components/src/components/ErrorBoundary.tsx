import { Component, ErrorInfo, ReactNode } from 'react';
import Tai<PERSON><PERSON><PERSON>ler<PERSON> from './TailwindAlert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Global Error Boundary component to catch and handle React errors
 * This prevents the entire application from crashing when an error occurs in a component
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to our logging service

    this.setState({
      error,
      errorInfo
    });

    // You could also send this to an error reporting service
    // Example: errorReportingService.captureError(error, errorInfo);
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
          <div className="w-full max-w-md">
            <TailwindAlert
              open={true}
              severity="error"
              message={
                <div className="space-y-4">
                  <p>
                    We're sorry, but an error occurred while rendering this component.
                  </p>
                  <div className="text-sm bg-red-50 dark:bg-red-900/30 p-3 rounded-md overflow-auto max-h-40">
                    <p className="font-mono">{this.state.error?.toString()}</p>
                  </div>
                  <div className="flex justify-end">
                    <button
                      onClick={this.resetError}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              }
            />
          </div>
        </div>
      );
    }

    // If no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
