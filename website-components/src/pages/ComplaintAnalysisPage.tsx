import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import { withLayout } from '../context/LayoutContext';
import { useAuth } from '../context/AuthContext';
import complaintService from '../services/complaintService';
import { getComplaints } from '../services/api'; // Import the cached API function
import TailwindPageHeader from '../components/common/TailwindPageHeader';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import TextField from '../components/ui/TextField';
import LoadingDialog from '../components/ui/LoadingDialog';
import { FiSearch, FiPlus, FiX, FiLink, FiExternalLink, FiFileText } from 'react-icons/fi';

// Types
interface Complaint {
  _id?: string;
  complaint_number: string;
  complainant_name: string;
  category?: string;
  amount?: string | number;
  date?: string;
  status?: string;
  metadata?: any; // Add metadata for type safety
}

interface Transaction {
  complaint_id: string;
  complaint_number: string;
  transaction_ids: string[];
  amount: number;
  sender_account?: string;
  sender_bank?: string;
  receiver_account?: string;
  receiver_bank?: string;
  date?: string;
  receiver_info?: string;
  txn_type?: string;
  txn_id?: string;
  [key: string]: any; // Allow any additional fields
}

interface CommonAccount {
  account: string;
  account_type: string;
  bank: string;
  total_occurrences: number;
  transactions: Transaction[];
}

interface AnalysisResult {
  common_accounts: CommonAccount[];
  complaint_details: Record<string, Complaint>;
  total_complaints_analyzed: number;
  total_common_accounts: number;
}

// CSS styles for fixing table layout issues and styling
const tableStyles = `
  /* Force standard table layout */
  .analysis-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #e5e7eb;
    font-size: 0.875rem;
  }

  /* Dark mode table border */
  .dark .analysis-table {
    border-color: #374151;
  }

  /* Cell styling */
  .analysis-table th,
  .analysis-table td {
    padding: 8px;
    border: 1px solid #e5e7eb;
    text-align: left;
    vertical-align: middle;
    line-height: 1.4;
    box-sizing: border-box;
    height: 40px;
    position: relative;
  }

  /* Force cells to display content */
  .analysis-table td:empty::after {
    content: "\\00a0";
    visibility: hidden;
  }

  /* Ensure each cell takes up the right amount of space */
  .analysis-table th:nth-child(1),
  .analysis-table td:nth-child(1) { width: 10%; }

  .analysis-table th:nth-child(2),
  .analysis-table td:nth-child(2) { width: 10%; }

  .analysis-table th:nth-child(3),
  .analysis-table td:nth-child(3) { width: 12%; }

  .analysis-table th:nth-child(4),
  .analysis-table td:nth-child(4) { width: 8%; }

  .analysis-table th:nth-child(5),
  .analysis-table td:nth-child(5) { width: 8%; }

  .analysis-table th:nth-child(6),
  .analysis-table td:nth-child(6) { width: 8%; }

  .analysis-table th:nth-child(7),
  .analysis-table td:nth-child(7) { width: 12%; }

  .analysis-table th:nth-child(8),
  .analysis-table td:nth-child(8) { width: 20%; }

  .analysis-table th:nth-child(9),
  .analysis-table td:nth-child(9) { width: 12%; }

  /* Dark mode cell borders */
  .dark .analysis-table th,
  .dark .analysis-table td {
    border-color: #374151;
  }

  /* Header styling */
  .analysis-table th {
    background-color: var(--theme-accent) !important;
    font-weight: 600;
    color: var(--theme-text) !important;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  /* Row styling */
  .analysis-table tbody tr:nth-child(even) {
    background-color: var(--theme-bg-lighter) !important;
  }

  .analysis-table tbody tr {
    background-color: var(--theme-bg-card) !important;
    color: var(--theme-text) !important;
    transition: transform 0.15s ease, box-shadow 0.15s ease;
  }

  .analysis-table tbody tr:hover {
    transform: translateY(-1px) scale(1.002);
    box-shadow: 0 1px 4px var(--theme-glow);
  }

  /* Ensure text wrapping for long content */
  .analysis-table td {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* Ensure all cells have proper spacing */
  .analysis-table td > div,
  .analysis-table td > span {
    margin-bottom: 4px;
  }

  .analysis-table td > div:last-child,
  .analysis-table td > span:last-child {
    margin-bottom: 0;
  }

  /* Fix for mobile */
  @media (max-width: 768px) {
    .analysis-table {
      display: block;
      overflow-x: auto;
      white-space: nowrap;
    }
  }
`;

const ComplaintAnalysisPage: React.FC = () => {
  const navigate = useNavigate();
  const { showError, showSuccess } = useAlert();
  const { isAuthenticated, user } = useAuth();

  // State for complaints
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [searchResults, setSearchResults] = useState<Complaint[]>([]);
  const [selectedComplaints, setSelectedComplaints] = useState<Complaint[]>([]);
  const [allComplaints, setAllComplaints] = useState<Complaint[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);

  // State for analysis
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  // Check authentication and paid status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!user?.paid) {
      showError('This feature requires a paid subscription');
      navigate('/dashboard');
      return;
    }
  }, [isAuthenticated, user, navigate, showError]);

  // Fetch all complaints on mount with lazy loading
  useEffect(() => {
    // Use a flag to track component mount state
    let isMounted = true;

    // Implement lazy loading with a small delay to improve perceived performance
    const timer = setTimeout(() => {
      const fetchComplaints = async () => {
        try {
          // Use cached data if available (from the API service)
          const response = await getComplaints(false); // false = don't force refresh

          // Only update state if component is still mounted
          if (!isMounted) return;

          if (response.success && response.data) {
            // Handle the new paginated response format
            if (response.data.data && Array.isArray(response.data.data)) {
              setAllComplaints(response.data.data);
            }
            // Handle direct array response (for backward compatibility)
            else if (Array.isArray(response.data)) {
              setAllComplaints(response.data);
            }
            // Handle unexpected format
            else {
              setAllComplaints([]);
            }
          } else {
            // Fallback to empty array if no data or success is false
            setAllComplaints([]);
          }
        } catch (error: any) {
          // Only update state if component is still mounted
          if (!isMounted) return;

          showError(error.message || 'Failed to fetch complaints');
          setAllComplaints([]); // Set to empty array on error
        }
      };

      fetchComplaints();
    }, 100); // Small delay for better UX

    // Cleanup function to prevent memory leaks and state updates after unmount
    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, [showError]);

  // Search complaints
  const handleSearch = useCallback(() => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    try {
      // Make sure allComplaints is an array before filtering
      if (!Array.isArray(allComplaints)) {
        setSearchResults([]);
        return;
      }

      const searchTermLower = searchTerm.toLowerCase().trim();
      const searchWords = searchTermLower.split(/\s+/);

      // Filter complaints based on search term with improved matching
      const results = allComplaints.filter(complaint => {
        // Skip undefined or null values
        if (!complaint || !complaint.complaint_number || !complaint.complainant_name) {
          return false;
        }

        const complaintNumber = complaint.complaint_number.toLowerCase();
        const complainantName = complaint.complainant_name.toLowerCase();

        // Check if all search words match the beginning of any word in the complaint number or name
        return searchWords.every(word => {
          // Match at the beginning of the complaint number
          if (complaintNumber.startsWith(word)) {
            return true;
          }

          // Match at the beginning of any word in the complaint number
          if (complaintNumber.split(/\s+/).some(part => part.startsWith(word))) {
            return true;
          }

          // Match at the beginning of the complainant name
          if (complainantName.startsWith(word)) {
            return true;
          }

          // Match at the beginning of any word in the complainant name
          if (complainantName.split(/\s+/).some(part => part.startsWith(word))) {
            return true;
          }

          // If no matches at word beginnings, fall back to includes for better UX
          return complaintNumber.includes(word) || complainantName.includes(word);
        });
      });

      // Filter out complaints that are already selected
      const filteredResults = results.filter(
        result => !selectedComplaints.some(selected => selected._id === result._id)
      );

      // Sort results to prioritize matches at the beginning of words
      const sortedResults = filteredResults.sort((a, b) => {
        const aNumber = a.complaint_number.toLowerCase();
        const bNumber = b.complaint_number.toLowerCase();

        // Prioritize exact matches at the beginning
        if (aNumber.startsWith(searchTermLower) && !bNumber.startsWith(searchTermLower)) {
          return -1;
        }
        if (!aNumber.startsWith(searchTermLower) && bNumber.startsWith(searchTermLower)) {
          return 1;
        }

        // Then prioritize matches at the beginning of any word
        const aStartsWithWord = aNumber.split(/\s+/).some(part => part.startsWith(searchTermLower));
        const bStartsWithWord = bNumber.split(/\s+/).some(part => part.startsWith(searchTermLower));

        if (aStartsWithWord && !bStartsWithWord) {
          return -1;
        }
        if (!aStartsWithWord && bStartsWithWord) {
          return 1;
        }

        // Default to alphabetical order
        return aNumber.localeCompare(bNumber);
      });

      setSearchResults(sortedResults);
    } catch (error) {
    } finally {
      setIsSearching(false);
    }
  }, [searchTerm, allComplaints, selectedComplaints]);

  // Trigger search when search term changes
  useEffect(() => {
    const delaySearch = setTimeout(() => {
      handleSearch();
    }, 300);

    return () => clearTimeout(delaySearch);
  }, [searchTerm, handleSearch]);

  // Add complaint to selected list
  const handleAddComplaint = (complaint: Complaint) => {
    setSelectedComplaints(prev => [...prev, complaint]);
    setSearchResults(prev => prev.filter(c => c._id !== complaint._id));
    setSearchTerm('');
  };

  // Remove complaint from selected list
  const handleRemoveComplaint = (complaintId: string) => {
    const removedComplaint = selectedComplaints.find(c => c._id === complaintId);

    setSelectedComplaints(prev => prev.filter(c => c._id !== complaintId));

    if (removedComplaint) {
      // Add back to search results if it matches the current search term
      if (
        searchTerm &&
        (removedComplaint.complaint_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
         removedComplaint.complainant_name.toLowerCase().includes(searchTerm.toLowerCase()))
      ) {
        setSearchResults(prev => [...prev, removedComplaint]);
      }
    }
  };

  // Analyze selected complaints using the optimized method with lazy loading
  const handleAnalyze = async () => {
    if (selectedComplaints.length < 2) {
      showError('Please select at least two complaints for analysis');
      return;
    }

    if (selectedComplaints.length > 10) {
      showError('Maximum 10 complaints can be analyzed at once');
      return;
    }

    setIsAnalyzing(true);

    try {
      const complaintIds = selectedComplaints.map(c => c._id as string);


      // Create a promise that resolves after a small delay to improve UI responsiveness
      const analysisPromise = new Promise<any>(async (resolve) => {
        // Small delay to allow the UI to update before starting the heavy operation
        setTimeout(async () => {
          try {
            // Use the optimized method that only fetches transaction data
            const result = await complaintService.analyzeCommonAccountsOptimized(complaintIds);
            resolve(result);
          } catch (error) {
            resolve(null); // Resolve with null to handle errors in the main try/catch block
          }
        }, 100);
      });

      // Wait for the analysis to complete
      const result = await analysisPromise;

      // Handle error case
      if (!result) {
        throw new Error('Analysis failed');
      }
      // Analysis completed successfully

      setAnalysisResult(result);
      showSuccess('Analysis completed successfully');
    } catch (error: any) {
      showError(error.message || 'Failed to analyze complaints');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Clear analysis results
  const handleClearAnalysis = () => {
    setAnalysisResult(null);
  };

  // View complaint details
  const handleViewComplaint = (complaintId: string) => {
    navigate(`/complaint/${complaintId}`);
  };

  // View complaint summary
  const handleViewSummary = (complaintId: string) => {
    navigate(`/complaint-summary/${complaintId}`);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Apply the CSS styles */}
      <style>{tableStyles}</style>

      <TailwindPageHeader
        title="Complaint Analysis"
        subtitle="Find connections between complaints by analyzing common accounts"
        breadcrumbs={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Complaint Analysis' }
        ]}
      />

      <LoadingDialog open={isAnalyzing} message="Analyzing complaints..." />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Search and Selection */}
        <div className="lg:col-span-1">
          <Card className="mb-6">
            <Card.Header>
              <h2 className="text-xl font-bold" style={{ color: 'var(--theme-text)' }}>
                Select Complaints
              </h2>
            </Card.Header>
            <Card.Content>
              <div className="mb-4">
                <TextField
                  placeholder="Search by complaint number or name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  startAdornment={<FiSearch className="w-3.5 h-3.5" style={{ color: 'var(--theme-accent)' }} />}
                  className="w-full"
                />
              </div>

              {searchTerm && (
                <div className="mb-4 max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                  {isSearching ? (
                    <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                      Searching...
                    </div>
                  ) : searchResults.length === 0 ? (
                    <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                      No matching complaints found
                    </div>
                  ) : (
                    <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                      {searchResults.map((complaint) => (
                        <li key={complaint._id} className="p-2 hover:bg-gray-50 dark:hover:bg-gray-800">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium" style={{ color: 'var(--theme-text)' }}>
                                {complaint.complaint_number}
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {complaint.metadata?.complainant_name || complaint.complainant_name}
                              </p>
                            </div>
                            <Button
                              variant="text"
                              size="sm"
                              onClick={() => handleAddComplaint(complaint)}
                              className="p-1 min-w-0"
                              aria-label="Add complaint"
                            >
                              <FiPlus className="w-4 h-4" />
                            </Button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}

              <div className="mt-4">
                <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--theme-text)' }}>
                  Selected Complaints ({selectedComplaints.length})
                </h3>

                {selectedComplaints.length === 0 ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
                    No complaints selected
                  </div>
                ) : (
                  <ul className="space-y-2">
                    {selectedComplaints.map((complaint) => (
                      <li
                        key={complaint._id}
                        className="p-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-600/50"
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium" style={{ color: 'var(--theme-text)' }}>
                              {complaint.complaint_number}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {complaint.metadata?.complainant_name || complaint.complainant_name}
                            </p>
                          </div>
                          <Button
                            variant="text"
                            size="sm"
                            onClick={() => handleRemoveComplaint(complaint._id as string)}
                            className="p-1 min-w-0 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 dark:text-red-400"
                            aria-label="Remove complaint"
                          >
                            <FiX className="w-4 h-4" />
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}

                <div className="mt-4">
                  <Button
                    variant="primary"
                    onClick={handleAnalyze}
                    disabled={selectedComplaints.length < 2 || isAnalyzing}
                    className="w-full"
                    startIcon={<FiLink className="w-4 h-4" />}
                  >
                    {isAnalyzing ? 'Analyzing...' : 'Analyze Connections'}
                  </Button>
                </div>
              </div>
            </Card.Content>
          </Card>
        </div>

        {/* Right Column - Analysis Results */}
        <div className="lg:col-span-2">
          {analysisResult ? (
            <div className="space-y-6">
              {/* Analysis Summary */}
              <Card className="mb-4">
                <div className="flex items-center justify-between px-2 py-1.5 bg-gray-50 dark:bg-gray-800/70 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-sm font-medium" style={{ color: 'var(--theme-text)' }}>
                    Analysis Results
                  </h2>
                  <Button
                    variant="text"
                    size="sm"
                    onClick={handleClearAnalysis}
                    className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-xs py-0.5 px-1.5"
                  >
                    Clear
                  </Button>
                </div>
                <div className="p-2">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-100 dark:border-blue-800/30">
                      <p className="text-xs text-gray-500 dark:text-gray-400">Complaints Analyzed</p>
                      <p className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
                        {analysisResult.total_complaints_analyzed}
                      </p>
                    </div>
                    <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-100 dark:border-green-800/30">
                      <p className="text-xs text-gray-500 dark:text-gray-400">Common Accounts Found</p>
                      <p className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
                        {analysisResult.total_common_accounts}
                      </p>
                    </div>
                    <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-100 dark:border-purple-800/30">
                      <p className="text-xs text-gray-500 dark:text-gray-400">Total Transactions</p>
                      <p className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
                        {analysisResult.common_accounts.reduce(
                          (sum, account) => sum + account.transactions.length, 0
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Common Accounts */}
              {analysisResult.common_accounts.length === 0 ? (
                <Card>
                  <Card.Content>
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">
                        No common accounts found across the selected complaints.
                      </p>
                    </div>
                  </Card.Content>
                </Card>
              ) : (
                <div className="space-y-4">
                  {analysisResult.common_accounts.map((account, index) => (
                    <Card key={index} className="overflow-hidden">
                      {/* Account Header - Compact */}
                      <div className="flex items-center justify-between px-3 py-1.5 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800/30">
                        <div className="flex items-baseline space-x-2">
                          <h3 className="text-md font-bold" style={{ color: 'var(--theme-text)' }}>
                            Account Number: {account.account}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {account.bank || 'Unknown Bank'} • {account.total_occurrences} complaints
                          </p>
                        </div>
                      </div>

                      {/* Transactions Table - Simple HTML Table */}
                      <div className="overflow-x-auto" style={{ maxWidth: '100%' }}>
                        <div className="p-2">
                          <table style={{ width: '100%', borderCollapse: 'collapse', tableLayout: 'fixed' }}>
                            <thead>
                              <tr>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '10%', wordBreak: 'break-word' }}>Complaint</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '10%', wordBreak: 'break-word' }}>Transaction ID</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '12%', wordBreak: 'break-word' }}>Sender</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '8%', wordBreak: 'break-word' }}>Amount</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '8%', wordBreak: 'break-word' }}>Type</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '8%', wordBreak: 'break-word' }}>Date</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '12%', wordBreak: 'break-word' }}>Sub-Type</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '20%', wordBreak: 'break-word' }}>Reference</th>
                                <th style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'left', backgroundColor: '#f2f2f2', fontSize: '12px', fontWeight: '600', width: '12%', wordBreak: 'break-word' }}>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {(() => {
                                // Define transaction group interface
                                interface TransactionGroup {
                                  mainTransaction: Transaction | null;
                                  subTransactions: Transaction[];
                                }

                                // Group transactions by complaint_id and txn_id to identify main transactions and their sub-transactions
                                const transactionGroups: Record<string, TransactionGroup> = {};

                                // First, identify main transactions and sub-transactions
                                account.transactions.forEach(transaction => {
                                  const txnType = transaction.txn_type || '';
                                  const isMainTransaction = txnType.toLowerCase() === 'money transfer to';
                                  const txnId = transaction.txn_id || (transaction.transaction_ids && transaction.transaction_ids.length > 0 ? transaction.transaction_ids[0] : '');
                                  const complaintId = transaction.complaint_id;

                                  // Create a unique key for each transaction group
                                  const groupKey = `${complaintId}_${txnId}`;

                                  if (!transactionGroups[groupKey]) {
                                    transactionGroups[groupKey] = {
                                      mainTransaction: null,
                                      subTransactions: []
                                    };
                                  }

                                  if (isMainTransaction) {
                                    transactionGroups[groupKey].mainTransaction = transaction;
                                  } else {
                                    transactionGroups[groupKey].subTransactions.push(transaction);
                                  }
                                });

                                // Now render each transaction group
                                return Object.values(transactionGroups).map((group: TransactionGroup, groupIndex: number) => {
                                  const transaction = group.mainTransaction || group.subTransactions[0];
                                  if (!transaction) return null;

                                  // Extract transaction information
                                  const txnType = transaction.txn_type || '';
                                  let displayType = txnType ? txnType.charAt(0).toUpperCase() + txnType.slice(1).toLowerCase() : 'Transaction';
                                  let typeColor = 'gray';

                                  // Extract reference information
                                  const reference = transaction.receiver_info || '';

                                  // Determine if this is a sub-transaction (anything that's not "Money Transfer to")
                                  const isMainTransaction = txnType.toLowerCase() === 'money transfer to';

                                  // Determine type color based on known transaction types
                                  const lowerType = displayType.toLowerCase();
                                  if (lowerType.includes('hold')) {
                                    displayType = 'Hold';
                                    typeColor = 'green';
                                  } else if (lowerType.includes('withdrawal') || lowerType.includes('debit')) {
                                    displayType = 'Withdrawal';
                                    typeColor = 'red';
                                  } else if (lowerType.includes('credit') || lowerType.includes('deposit')) {
                                    displayType = 'Deposit';
                                    typeColor = 'blue';
                                  } else if (lowerType.includes('transfer')) {
                                    displayType = 'Transfer';
                                    typeColor = 'purple';
                                  }

                                  // Get background color based on row index
                                  const bgColor = groupIndex % 2 === 0 ? '#ffffff' : '#f9fafb';

                                  // No need to prepare sub-transaction types here as we'll handle them directly in the render

                                  return (
                                    <tr key={groupIndex} style={{ backgroundColor: bgColor }}>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', wordBreak: 'break-word', width: '10%' }}>
                                        <span style={{ fontWeight: '500' }}>{transaction.complaint_number || '\u00A0'}</span>
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', wordBreak: 'break-word', width: '10%' }}>
                                        {transaction.txn_id || (transaction.transaction_ids && transaction.transaction_ids.length > 0 ? transaction.transaction_ids[0] : '\u00A0')}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', wordBreak: 'break-word', width: '12%' }}>
                                        {transaction.sender_account ? (
                                          <div>
                                            <div style={{ wordBreak: 'break-word' }}>{transaction.sender_account}</div>
                                            {transaction.sender_bank && (
                                              <div style={{ fontSize: '10px', color: '#6b7280', wordBreak: 'break-word' }}>
                                                {transaction.sender_bank}
                                              </div>
                                            )}
                                          </div>
                                        ) : '\u00A0'}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', fontWeight: '500', wordBreak: 'break-word', width: '8%' }}>
                                        {typeof transaction.amount === 'number' ?
                                          `₹${transaction.amount.toLocaleString('en-IN')}` :
                                          transaction.amount ? `₹${transaction.amount}` : '\u00A0'}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', wordBreak: 'break-word', width: '8%' }}>
                                        {displayType ? (
                                          <span style={{
                                            padding: '2px 6px',
                                            borderRadius: '4px',
                                            fontSize: '11px',
                                            display: 'inline-block',
                                            wordBreak: 'break-word',
                                            backgroundColor: typeColor === 'green' ? '#dcfce7' :
                                                            typeColor === 'red' ? '#fee2e2' :
                                                            typeColor === 'blue' ? '#dbeafe' :
                                                            typeColor === 'purple' ? '#f3e8ff' : '#f3f4f6',
                                            color: typeColor === 'green' ? '#166534' :
                                                  typeColor === 'red' ? '#991b1b' :
                                                  typeColor === 'blue' ? '#1e40af' :
                                                  typeColor === 'purple' ? '#6b21a8' : '#374151'
                                          }}>
                                            {displayType}
                                          </span>
                                        ) : '\u00A0'}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', color: '#6b7280', wordBreak: 'break-word', width: '8%' }}>
                                        {transaction.date || '\u00A0'}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', color: '#6b7280', wordBreak: 'break-word', width: '12%' }}>
                                        {group.subTransactions.length > 0 ? (
                                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                                            {group.subTransactions.map((subTx: Transaction, subIndex: number) => {
                                              const subTxnType = subTx.txn_type || '';
                                              const subDisplayType = subTxnType ? subTxnType.charAt(0).toUpperCase() + subTxnType.slice(1).toLowerCase() : '';

                                              // Determine color for sub-transaction type
                                              let subTypeColor = 'gray';
                                              if (subDisplayType.toLowerCase().includes('hold')) {
                                                subTypeColor = 'green';
                                              } else if (subDisplayType.toLowerCase().includes('withdrawal') || subDisplayType.toLowerCase().includes('debit')) {
                                                subTypeColor = 'red';
                                              } else if (subDisplayType.toLowerCase().includes('credit') || subDisplayType.toLowerCase().includes('deposit')) {
                                                subTypeColor = 'blue';
                                              } else if (subDisplayType.toLowerCase().includes('transfer')) {
                                                subTypeColor = 'purple';
                                              }

                                              return (
                                                <div key={subIndex} style={{
                                                  padding: '2px 4px',
                                                  borderRadius: '2px',
                                                  fontSize: '11px',
                                                  backgroundColor: subTypeColor === 'green' ? '#dcfce7' :
                                                                  subTypeColor === 'red' ? '#fee2e2' :
                                                                  subTypeColor === 'blue' ? '#dbeafe' :
                                                                  subTypeColor === 'purple' ? '#f3e8ff' : '#f3f4f6',
                                                  color: subTypeColor === 'green' ? '#166534' :
                                                        subTypeColor === 'red' ? '#991b1b' :
                                                        subTypeColor === 'blue' ? '#1e40af' :
                                                        subTypeColor === 'purple' ? '#6b21a8' : '#374151',
                                                  borderBottom: subIndex < group.subTransactions.length - 1 ? '1px dashed #e5e7eb' : 'none'
                                                }}>
                                                  {subDisplayType}
                                                </div>
                                              );
                                            })}
                                          </div>
                                        ) : isMainTransaction ? '\u00A0' : (
                                          <div style={{ fontWeight: '500', wordBreak: 'break-word' }}>{displayType}</div>
                                        )}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', color: '#6b7280', wordBreak: 'break-word', width: '20%' }}>
                                        {group.subTransactions.length > 0 ? (
                                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                                            {group.subTransactions.map((subTx: Transaction, subIndex: number) => {
                                              const subReference = subTx.receiver_info || '';
                                              return (
                                                <div key={subIndex} style={{
                                                  padding: '2px 0',
                                                  fontSize: '11px',
                                                  borderBottom: subIndex < group.subTransactions.length - 1 ? '1px dashed #e5e7eb' : 'none',
                                                  wordBreak: 'break-word'
                                                }} title={subReference}>
                                                  {subReference}
                                                </div>
                                              );
                                            })}
                                          </div>
                                        ) : (
                                          <div style={{ wordBreak: 'break-word' }} title={reference}>
                                            {reference}
                                          </div>
                                        )}
                                      </td>
                                      <td style={{ padding: '4px', border: '1px solid #ddd', fontSize: '12px', width: '12%' }}>
                                        <div style={{ display: 'flex', gap: '4px' }}>
                                          <Button
                                            variant="text"
                                            size="sm"
                                            onClick={() => handleViewComplaint(transaction.complaint_id)}
                                            className="p-0.5 min-w-0 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded"
                                            aria-label="View complaint"
                                            title="View complaint details"
                                          >
                                            <FiExternalLink className="w-3 h-3" />
                                          </Button>
                                          <Button
                                            variant="text"
                                            size="sm"
                                            onClick={() => handleViewSummary(transaction.complaint_id)}
                                            className="p-0.5 min-w-0 hover:bg-purple-50 dark:hover:bg-purple-900/20 text-purple-600 dark:text-purple-400 rounded"
                                            aria-label="View complaint summary"
                                            title="View complaint summary"
                                          >
                                            <FiFileText className="w-3 h-3" />
                                          </Button>
                                        </div>
                                      </td>
                                    </tr>
                                  );
                                });
                              })()}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <Card>
              <Card.Content>
                <div className="text-center py-12">
                  <div className="mb-4">
                    <FiLink className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600" />
                  </div>
                  <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--theme-text)' }}>
                    No Analysis Results
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                    Select at least two complaints from the left panel and click "Analyze Connections" to find common accounts between them.
                  </p>
                </div>
              </Card.Content>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default withLayout(ComplaintAnalysisPage, {
  fullWidth: false,
  hideSidebar: false,
  customPadding: '0 1rem'
});
