import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useComplaintData } from '../hooks/useComplaintData';
import { useAlert } from '../context/TailwindAlertContext';

import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import authService from '../services/authService';
import { FiDownload, FiArrowLeft, FiChevronDown, FiChevronUp } from 'react-icons/fi';

interface BankAccount {
  account_no: string;
  account_holder: string;
  transactions: any[];
  selected: boolean;
  total_transactions?: number;
}

interface Bank {
  bank_name: string;
  accounts: BankAccount[];
  selected: boolean;
  expanded?: boolean;
  noticePoints?: NoticePoint[];
}

interface NoticeSubPoint {
  id: string;
  text: string;
  selected: boolean;
}

interface NoticePoint {
  id: string;
  text: string;
  selected: boolean;
  expanded?: boolean;
  subPoints?: NoticeSubPoint[];
}

const DEFAULT_NOTICE_POINTS: NoticePoint[] = [
  {
    id: 'account_info',
    text: 'Account Holder Information',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'account_info_name', text: 'Full name with registered and communication address of the bank account holder.', selected: true },
      { id: 'account_info_contact', text: 'Contact number and email address linked to the account/wallet.', selected: true },
      { id: 'account_info_digital', text: 'Digital footprints of account opening journey if the account is opened digitally.', selected: true },
      { id: 'account_info_kyc', text: 'Account Opening Form (AOF), KYC documents, and suspicious activity flags.', selected: true }
    ]
  },
  {
    id: 'linked_accounts',
    text: 'Linked Accounts and Cards',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'linked_accounts_wallet', text: 'Details of linked bank account, credit/debit cards or UPI/VPAs with wallet', selected: true },
      { id: 'linked_accounts_same_kyc', text: 'Details of bank accounts opened with same KYC and details in you bank.', selected: true },
      { id: 'linked_accounts_card', text: 'ATM-cum-debit card details (card number, issue date, delivery address).', selected: true }
    ]
  },
  {
    id: 'ip_device',
    text: 'IP and Device Records',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'ip_device_addresses', text: 'IP addresses and login/logout history used for net banking or account access.', selected: true },
      { id: 'ip_device_details', text: 'Login/Logout IP address along with Device IDs and operating systems of devices used for account activity, geolocation coordinates (latitude/longitude) for transaction.', selected: true }
    ]
  },
  {
    id: 'auth_logs',
    text: 'Authentication and Access Logs',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'auth_logs_otp', text: 'Mobile numbers and email used for OTP or authentication during transactions and logins.', selected: true },
      { id: 'auth_logs_history', text: 'Mobile number or email changing history of the account.', selected: true }
    ]
  },
  {
    id: 'beneficiary',
    text: 'Beneficiary and Transaction History',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'beneficiary_receiver', text: 'Receiver\'s name, account number/VPA and IFSC code for fund transfers.', selected: true },
      { id: 'beneficiary_history', text: 'Complete transaction history from account opening/creation to till date.', selected: true },
      { id: 'beneficiary_flags', text: 'Internal flags and NCRP complaints received about the account/Wallet.', selected: true }
    ]
  },
  {
    id: 'wallet_status',
    text: 'Wallet/Bank account status and Balance',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'wallet_status_current', text: 'Current status (active, frozen), frozen amount, and last available balance.', selected: true },
      { id: 'wallet_status_hold', text: 'Put an hold on the amount mentioned in above transactions.', selected: true }
    ]
  },
  {
    id: 'ecommerce',
    text: 'E-commerce or Delivery Information',
    selected: false,
    expanded: false,
    subPoints: [
      { id: 'ecommerce_recipient', text: 'Recepient\'s name, contact number, courier details, tracking number.', selected: true },
      { id: 'ecommerce_address', text: 'Delivery/shipping address, Merchant details (name, address).', selected: true },
      { id: 'ecommerce_history', text: 'Any order history of the same user with address, product and contact details.', selected: true }
    ]
  },
  {
    id: 'atm_cctv',
    text: 'ATM CCTV footage',
    selected: false,
    expanded: false,
    subPoints: [
      { id: 'atm_cctv_footage', text: 'CCTV footage (external or Internal) of the ATM mentioned above of same day of transaction in case the ATM doesn\'t belong to your bank then provide the point of contact.', selected: true },
      { id: 'atm_cctv_credentials', text: 'Debit/Credit or Card less transaction credentials used for the transaction.', selected: true }
    ]
  },
  {
    id: 'bsa',
    text: 'BSA Certification',
    selected: true,
    expanded: false,
    subPoints: [
      { id: 'bsa_certificate', text: 'Section 63(4)(c) BSA certificate to authenticate electronic records.', selected: true }
    ]
  }
];

const NoticeGenerationPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError, showInfo } = useAlert();

  const [banks, setBanks] = useState<Bank[]>([]);
  const [globalNoticePoints, setGlobalNoticePoints] = useState<NoticePoint[]>(DEFAULT_NOTICE_POINTS);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectAll, setSelectAll] = useState(true);
  // State to track indeterminate checkboxes
  const [indeterminatePoints, setIndeterminatePoints] = useState<{[key: number]: boolean}>({});

  // Use our custom hook to fetch complaint data
  const { data: complaintData, loading, error } = useComplaintData(id as string, true);

  // Initialize indeterminate state for checkboxes
  useEffect(() => {
    const newIndeterminateState: {[key: number]: boolean} = {};

    globalNoticePoints.forEach((point, index) => {
      if (point.subPoints && point.subPoints.length > 0) {
        const allSelected = point.subPoints.every(subPoint => subPoint.selected);
        const anySelected = point.subPoints.some(subPoint => subPoint.selected);
        newIndeterminateState[index] = anySelected && !allSelected;
      }
    });

    setIndeterminatePoints(newIndeterminateState);
  }, [globalNoticePoints]);

  // Process bank data when complaint data is loaded
  useEffect(() => {
    if (complaintData && !loading) {
      try {
        // Parse bank notice data
        let bankData = complaintData.bank_notice_data;


        // Check if bank data exists
        if (!bankData) {

          // If we have transactions but no bank data, try to generate bank data from transactions
          if (complaintData.transactions && Array.isArray(complaintData.transactions) && complaintData.transactions.length > 0) {

            // Simple function to structure transactions by bank
            const structureTransactionsByBank = (transactions: any[]) => {
              const bankMap: Record<string, any> = {};

              transactions.forEach(txn => {
                const bankName = txn.receiver_bank || 'Unknown Bank';

                if (!bankMap[bankName]) {
                  bankMap[bankName] = {
                    accounts: {}
                  };
                }

                const accountNo = txn.receiver_account || 'Unknown Account';

                if (!bankMap[bankName].accounts[accountNo]) {
                  bankMap[bankName].accounts[accountNo] = {
                    account_no: accountNo,
                    account_holder: '',
                    transactions: []
                  };
                }

                bankMap[bankName].accounts[accountNo].transactions.push(txn);
              });

              return bankMap;
            };

            try {
              bankData = structureTransactionsByBank(complaintData.transactions);
            } catch (error) {
              setBanks([]);
              return;
            }
          } else {
            setBanks([]);
            return;
          }
        }

        // Handle string format (JSON string)
        if (typeof bankData === 'string') {
          try {
            bankData = JSON.parse(bankData);
          } catch (parseError) {
            setBanks([]);
            return;
          }
        }

        // Check if bankData is null or undefined after parsing
        if (!bankData) {
          setBanks([]);
          return;
        }

        // Check if data is in the new bank-structured format (object with bank names as keys)
        // or the old format (array of bank objects)
        const processedBanks = [];

        if (Array.isArray(bankData)) {
          // Old format - array of bank objects
          processedBanks.push(...bankData.map((bank: any) => ({
            ...bank,
            selected: true,
            expanded: false,
            noticePoints: [...DEFAULT_NOTICE_POINTS],
            accounts: Array.isArray(bank.accounts) ? bank.accounts.map((account: any) => ({
              ...account,
              selected: true
            })) : []
          })));
        } else if (typeof bankData === 'object' && bankData !== null) {
          // New format - object with bank names as keys
          for (const [bankName, accounts] of Object.entries(bankData)) {
            // Skip if accounts is null or not an object
            if (!accounts || typeof accounts !== 'object') {
              continue;
            }

            // Convert accounts object to array format for UI
            const accountsArray = [];

            for (const [accountNo, accountData] of Object.entries(accounts as Record<string, any>)) {
              // Skip if accountData is null or not an object
              if (!accountData || typeof accountData !== 'object') {
                continue;
              }

              const totalTransfers = accountData.transfers?.length || 0;
              const totalSpecialTxns = accountData.special_transactions?.length || 0;

              accountsArray.push({
                account_no: accountNo,
                account_holder: 'Account Holder', // This info might not be available in new format
                selected: true,
                transactions: [
                  ...(Array.isArray(accountData.transfers) ? accountData.transfers : []),
                  ...(Array.isArray(accountData.special_transactions) ? accountData.special_transactions : [])
                ],
                total_transactions: totalTransfers + totalSpecialTxns
              });
            }

            processedBanks.push({
              bank_name: bankName,
              selected: true,
              expanded: false,
              noticePoints: [...DEFAULT_NOTICE_POINTS],
              accounts: accountsArray
            });
          }
        }

        setBanks(processedBanks);
      } catch (error) {
        showError('Failed to process bank data');
        // Set empty banks array to prevent further errors
        setBanks([]);
      }
    }
  }, [complaintData, loading, showError]);

  // Toggle bank selection - optimized to minimize re-renders
  const toggleBankSelection = React.useCallback((index: number) => {
    setBanks(prevBanks => {
      const updatedBanks = [...prevBanks];
      const newSelectedState = !updatedBanks[index].selected;
      updatedBanks[index] = {
        ...updatedBanks[index],
        selected: newSelectedState,
        // We still update accounts selection state for data consistency
        // even though we no longer show the account selection UI
        accounts: updatedBanks[index].accounts.map(account => ({
          ...account,
          selected: newSelectedState
        }))
      };
      return updatedBanks;
    });
  }, []);

  // We've removed the toggleAccountSelection function since it's no longer needed

  // We've removed the toggleBankExpansion function since it's no longer needed

  // Toggle main notice point selection - optimized with useCallback
  const toggleMainPointSelection = React.useCallback((index: number) => {
    setGlobalNoticePoints(prevPoints => {
      const updatedPoints = [...prevPoints];
      const mainPoint = updatedPoints[index];
      const newSelectedState = !mainPoint.selected;

      // Update all sub-points based on the new state of the main point
      const updatedSubPoints = mainPoint.subPoints?.map(subPoint => ({
        ...subPoint,
        selected: newSelectedState
      }));

      // Update the main point
      updatedPoints[index] = {
        ...mainPoint,
        selected: newSelectedState,
        subPoints: updatedSubPoints
      };

      // Clear the indeterminate state for this point
      setIndeterminatePoints(prev => ({
        ...prev,
        [index]: false
      }));

      // Always update all banks with the global points
      setBanks(prevBanks =>
        prevBanks.map(bank => ({
          ...bank,
          noticePoints: [...updatedPoints]
        }))
      );

      return updatedPoints;
    });
  }, [indeterminatePoints]);

  // Toggle sub-point selection
  const toggleSubPointSelection = React.useCallback((mainIndex: number, subIndex: number) => {
    setGlobalNoticePoints(prevPoints => {
      const updatedPoints = [...prevPoints];
      const mainPoint = updatedPoints[mainIndex];

      if (!mainPoint.subPoints) return updatedPoints;

      // Update the specific sub-point
      const updatedSubPoints = [...mainPoint.subPoints];
      updatedSubPoints[subIndex] = {
        ...updatedSubPoints[subIndex],
        selected: !updatedSubPoints[subIndex].selected
      };

      // Check if all sub-points are selected
      const allSubPointsSelected = updatedSubPoints.every(subPoint => subPoint.selected);

      // Check if any sub-points are selected
      const anySubPointSelected = updatedSubPoints.some(subPoint => subPoint.selected);

      // Check if some but not all sub-points are selected (indeterminate state)
      const someSubPointsSelected = anySubPointSelected && !allSubPointsSelected;

      // Update indeterminate state
      setIndeterminatePoints(prev => ({
        ...prev,
        [mainIndex]: someSubPointsSelected
      }));

      // Update the main point
      updatedPoints[mainIndex] = {
        ...mainPoint,
        selected: anySubPointSelected, // Main point is selected if any sub-point is selected
        subPoints: updatedSubPoints
      };

      // Always update all banks with the global points
      setBanks(prevBanks =>
        prevBanks.map(bank => ({
          ...bank,
          noticePoints: [...updatedPoints]
        }))
      );

      return updatedPoints;
    });
  }, []);

  // Toggle point expansion
  const togglePointExpansion = React.useCallback((index: number) => {
    setGlobalNoticePoints(prevPoints => {
      const updatedPoints = [...prevPoints];
      updatedPoints[index] = {
        ...updatedPoints[index],
        expanded: !updatedPoints[index].expanded
      };
      return updatedPoints;
    });
  }, []);

  // Toggle select all banks
  const toggleSelectAllBanks = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    const updatedBanks = banks.map(bank => ({
      ...bank,
      selected: newSelectAll,
      // We still update accounts selection state for data consistency
      // even though we no longer show the account selection UI
      accounts: bank.accounts.map(account => ({
        ...account,
        selected: newSelectAll
      }))
    }));

    setBanks(updatedBanks);
  };

  // No longer using preview template function

  // Generate and download notices
  const generateNotices = async () => {
    try {
      setIsGenerating(true);

      // Validate complaint data
      if (!complaintData) {
        showError('Complaint data is not available');
        setIsGenerating(false);
        return;
      }

      // Get selected banks
      const selectedBanks = banks.filter(bank => bank.selected);

      if (selectedBanks.length === 0) {
        showError('Please select at least one bank');
        setIsGenerating(false);
        return;
      }

      // Check if each bank has at least one selected notice point
      for (const bank of selectedBanks) {
        const bankPoints = bank.noticePoints || [];
        if (bankPoints.filter(point => point.selected).length === 0) {
          showError(`Please select at least one notice point for ${bank.bank_name}`);
          setIsGenerating(false);
          return;
        }
      }

      // For each selected bank, generate a notice
      for (const bank of selectedBanks) {
        try {
          // No need to check for token as the API client handles authentication

          // Get all selected points including sub-points
          const selectedPoints: string[] = [];

          // Process each main point
          const bankPoints = bank.noticePoints || [];
          bankPoints.forEach(point => {
            if (point.selected) {
              // Add the main point ID
              selectedPoints.push(point.id);

              // Add all selected sub-points
              if (point.subPoints) {
                point.subPoints.forEach(subPoint => {
                  if (subPoint.selected) {
                    selectedPoints.push(subPoint.id);
                  }
                });
              }
            }
          });

          const pointsParam = selectedPoints.join(',');

          // Use the secure API client instead of fetch
          const response = await authService.api.get(`/notices/generate/${id}`, {
            params: {
              bank: bank.bank_name,
              points: pointsParam
            },
            responseType: 'blob',
            headers: {
              'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            }
          });

          // Get the blob from the response
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          if (!blob || blob.size === 0) {
            throw new Error('Received empty response from server');
          }

          // Create a download link for the blob
          const objectUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = objectUrl;
          link.setAttribute('download', `Notice_${complaintData?.complaint_number || id}_${bank.bank_name}.docx`);

          // Append to body, click, and remove
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the URL object
          window.URL.revokeObjectURL(objectUrl);

          // Add a small delay between downloads to prevent browser issues
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (bankError: any) {
          showError(`Failed to generate notice for ${bank.bank_name}: ${bankError.message || 'Unknown error'}`);
          // Continue with other banks instead of stopping completely
        }
      }

      showSuccess(`Generated notice(s) successfully`);
    } catch (error: any) {
      showError(`Failed to generate notices: ${error.message || 'Unknown error'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // Generate and download all notices as a batch
  const generateAllNotices = async () => {
    try {
      setIsGenerating(true);
      showInfo('Generating all notices, please wait...');

      // Validate complaint data
      if (!complaintData) {
        showError('Complaint data is not available');
        setIsGenerating(false);
        return;
      }

      // Get selected banks
      const selectedBanks = banks.filter(bank => bank.selected);

      if (selectedBanks.length === 0) {
        showError('Please select at least one bank');
        setIsGenerating(false);
        return;
      }

      // Check if each bank has at least one selected notice point
      for (const bank of selectedBanks) {
        const bankPoints = bank.noticePoints || [];
        if (bankPoints.filter(point => point.selected).length === 0) {
          showError(`Please select at least one notice point for ${bank.bank_name}`);
          setIsGenerating(false);
          return;
        }
      }

      // For batch generation, we'll use the first bank's notice points
      // This is a simplification - ideally we'd handle per-bank points in the backend
      const firstBankPoints = selectedBanks[0].noticePoints || [];

      // No need to check for token as the API client handles authentication

      // Get all selected points including sub-points
      const selectedPoints: string[] = [];

      // Process each main point
      firstBankPoints.forEach(point => {
        if (point.selected) {
          // Add the main point ID
          selectedPoints.push(point.id);

          // Add all selected sub-points
          if (point.subPoints) {
            point.subPoints.forEach(subPoint => {
              if (subPoint.selected) {
                selectedPoints.push(subPoint.id);
              }
            });
          }
        }
      });

      const banksParam = selectedBanks.map(b => b.bank_name).join(',');
      const pointsParam = selectedPoints.join(',');

      // Use the secure API client instead of fetch
      const response = await authService.api.get(`/notices/generate-batch/${id}`, {
        params: {
          banks: banksParam,
          points: pointsParam
        },
        responseType: 'blob',
        headers: {
          'Accept': 'application/zip'
        }
      });

      // Get the blob from the response
      const blob = new Blob([response.data], {
        type: 'application/zip'
      });
      if (!blob || blob.size === 0) {
        throw new Error('Received empty response from server');
      }

      // Create a download link for the blob
      const objectUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = objectUrl;
      link.setAttribute('download', `Notices_${complaintData?.complaint_number || id}.zip`);

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      window.URL.revokeObjectURL(objectUrl);

      showSuccess(`Generated batch of notices successfully`);
    } catch (error: any) {
      showError(`Failed to generate batch notices: ${error.message || 'Unknown error'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // No longer using preview notice function

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4 text-red-800 dark:text-red-300">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col overflow-hidden">
      <div className="p-4 flex flex-col sm:flex-row justify-between gap-3">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <h1>Generate Bank Notices</h1>
          </div>
        <Button
          variant="outlined"
          size="sm"
          startIcon={<FiArrowLeft />}
          onClick={() => navigate(`/complaint/${id}`)}
        >
          Back to Complaint
        </Button>
      </div>

      <div className="px-4 pb-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
        {/* Banks Selection */}
        <Card className="mb-4 bg-white/90 dark:bg-[rgba(16,16,30,0.7)] backdrop-blur-md flex flex-col" style={{ height: '600px' }}>
          <Card.Header className="flex-shrink-0">
            <div className="flex items-center justify-between">
              <h2 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>Select Banks</h2>
              <button
                className="text-xs bg-transparent border border-gray-200 dark:border-gray-700 rounded-md px-3 py-1.5 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                onClick={toggleSelectAllBanks}
                style={{ color: 'var(--theme-text)' }}
              >
                {selectAll ? 'Deselect All' : 'Select All'}
              </button>
            </div>
          </Card.Header>
          <Card.Content className="flex-grow overflow-y-auto">
            {banks.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-sm">No bank data available</p>
            ) : (
              <div className="space-y-3">
                {banks.map((bank, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id={`bank-${index}`}
                        checked={bank.selected}
                        onChange={() => toggleBankSelection(index)}
                        className="w-4 h-4 rounded-md"
                        style={{
                          accentColor: 'var(--theme-accent)',
                          borderRadius: '4px',
                          border: '1px solid var(--theme-border)'
                        }}
                      />
                      <label
                        htmlFor={`bank-${index}`}
                        className="ml-2 text-sm font-medium flex items-center"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {bank.bank_name}
                      </label>
                    </div>

                    {/* We've removed the account selection dropdown as requested */}
                  </div>
                ))}
              </div>
            )}
          </Card.Content>
        </Card>

        {/* Notice Points */}
        <Card className="mb-4 bg-white/90 dark:bg-[rgba(16,16,30,0.7)] backdrop-blur-md flex flex-col" style={{ height: '600px' }}>
          <Card.Header className="flex-shrink-0">
            <div className="flex items-center justify-between">
              <h2 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>Notice Points</h2>
            </div>
          </Card.Header>
          <Card.Content className="flex-grow overflow-y-auto">
            <div className="space-y-3">
              {globalNoticePoints.map((point, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`point-${index}`}
                      checked={point.selected}
                      ref={el => {
                        if (el) {
                          el.indeterminate = indeterminatePoints[index] || false;
                        }
                      }}
                      onChange={() => toggleMainPointSelection(index)}
                      className="w-4 h-4 rounded-md"
                      style={{
                        accentColor: 'var(--theme-accent)',
                        borderRadius: '4px',
                        border: '1px solid var(--theme-border)'
                      }}
                    />
                    <label
                      htmlFor={`point-${index}`}
                      className="ml-2 text-sm font-medium flex items-center"
                      style={{ color: 'var(--theme-text)' }}
                    >
                      {point.text}
                      <button
                        onClick={() => togglePointExpansion(index)}
                        className="ml-2 bg-transparent flex items-center justify-center"
                        style={{ color: 'var(--theme-accent)' }}
                      >
                        {point.expanded ? <FiChevronUp size={20} style={{ strokeWidth: 3 }} /> : <FiChevronDown size={20} style={{ strokeWidth: 3 }} />}
                      </button>
                    </label>
                  </div>

                  {/* Sub-points selection */}
                  {point.expanded && point.subPoints && (
                    <div className="ml-6 space-y-1 mt-1">
                      {point.subPoints.map((subPoint, subIndex) => (
                        <div key={subIndex} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`subpoint-${index}-${subIndex}`}
                            checked={subPoint.selected}
                            onChange={() => toggleSubPointSelection(index, subIndex)}
                            className="w-4 h-4 rounded-md"
                            style={{
                              accentColor: 'var(--theme-accent)',
                              borderRadius: '4px',
                              border: '1px solid var(--theme-border)'
                            }}
                          />
                          <label
                            htmlFor={`subpoint-${index}-${subIndex}`}
                            className="ml-2 text-xs"
                            style={{ color: 'var(--theme-text)' }}
                          >
                            {subPoint.text}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>

        {/* Generate Buttons */}
        <div className="lg:col-span-2 mt-3 space-y-3 sticky bottom-4 bg-white/90 dark:bg-[rgba(16,16,30,0.8)] backdrop-blur-md py-3 shadow-md z-10 rounded-md border border-gray-200 dark:border-[rgba(0,170,255,0.3)]">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button
              variant="primary"
              size="sm"
              className="w-full"
              startIcon={<FiDownload />}
              loading={isGenerating}
              disabled={isGenerating || banks.filter(b => b.selected).length === 0}
              onClick={generateNotices}
              style={{ color: 'var(--theme-text)' }}
            >
              Generate Individual Notices
            </Button>

            <Button
              variant="secondary"
              size="sm"
              className="w-full"
              startIcon={<FiDownload />}
              loading={isGenerating}
              disabled={isGenerating || banks.filter(b => b.selected).length === 0}
              onClick={generateAllNotices}
              style={{ color: 'var(--theme-text)' }}
            >
              Download All as ZIP
            </Button>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};


export default NoticeGenerationPage;
