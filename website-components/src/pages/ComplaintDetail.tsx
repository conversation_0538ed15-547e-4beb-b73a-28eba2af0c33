import React, { useState, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import useComplaintDataQuery from '../hooks/useComplaintDataQuery';
import { useUpdateCSVData } from '../hooks/useComplaintMutations';
import { useAlert } from '../context/TailwindAlertContext';
import { useAuth } from '../context/AuthContext';
import { FiArrowLeft, FiFileText, FiBarChart2 } from 'react-icons/fi';
import ReactTableCSVPreview from '../components/ReactTableCSVPreview';
import complaintService from '../services/complaintService';
import Button from '../components/ui/Button';
import { withLayout } from '../context/LayoutContext';
import './ComplaintDetail.css';

const ComplaintDetailComponent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showError, showSuccess } = useAlert();
  const { isAuthenticated, user } = useAuth();

  const [csvData, setCsvData] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);

  // Use our custom hook to fetch complaint data
  const { data: complaintData, loading, error } = useComplaintDataQuery(id as string, true);

  // React Query mutation for CSV updates
  const updateCSVMutation = useUpdateCSVData();

  // Combined loading state
  const isLoading = loading || updateCSVMutation.isPending || isDownloading;

  // Process CSV data when complaint data is loaded
  React.useEffect(() => {
    // Skip if there's no complaint data
    if (!complaintData) {
      return;
    }

    // Don't skip if there's no CSV data - we might have transactions
    if (!complaintData.csv_data_base64 && (!complaintData.transactions || !Array.isArray(complaintData.transactions) || complaintData.transactions.length === 0)) {
      return;
    }

    // Check if we have CSV data before logging its details
    if (complaintData.csv_data_base64) {
    } else {
    }

    try {
      // First, try to use the transactions directly if available
      if (complaintData.transactions && Array.isArray(complaintData.transactions) && complaintData.transactions.length > 0) {

        // Generate CSV from transactions - INCLUDE ALL COLUMNS in the exact order expected by the backend
        const headers = [
          'Layer',
          'Fraud Type',
          'Sender Account',
          'Sender Txn ID',
          'Sender Bank',
          'Receiver Account',
          'Txn ID',
          'Receiver Bank',
          'Txn Type',
          'Txn Date',
          'Amount',
          'Reference'
        ];

        // Create CSV header row
        let csvContent = headers.join(',') + '\n';

        // Add transaction rows - INCLUDE ALL FIELDS in the exact order expected by the backend
        complaintData.transactions.forEach((txn: any) => {
          const row = [
            txn.layer || '',
            txn.fraud_type || 'banking_upi', // Include fraud_type with default value
            txn.sender_account || '',
            txn.sender_transaction_id || '', // Include sender_txn_id
            txn.sender_bank || '',
            txn.receiver_account || '',
            txn.receiver_transaction_id || txn.txn_id || '', // Use receiver_transaction_id or txn_id
            txn.receiver_bank || '',
            txn.type || txn.txn_type || '', // Use type or txn_type
            txn.date || txn.txn_date || '', // Use date or txn_date
            txn.amount || '',
            txn.reference || txn.receiver_info || '' // Use reference or receiver_info
          ];

          // Escape any commas in the fields
          const escapedRow = row.map(field => {
            if (field && field.toString().includes(',')) {
              return `"${field}"`;
            }
            return field;
          });

          csvContent += escapedRow.join(',') + '\n';
        });

        setCsvData(csvContent);
        return;
      }

      // If no transactions, try to decode base64 CSV data
      // Check if the data is already a string (not base64)
      if (typeof complaintData.csv_data_base64 === 'string' &&
          complaintData.csv_data_base64.includes(',') &&
          !complaintData.csv_data_base64.includes('=')) {
        setCsvData(complaintData.csv_data_base64);
        return;
      }

      // Try to decode base64 CSV data
      let decodedData;
      try {
        // Remove any whitespace or newlines that might be in the base64 string
        const cleanBase64 = complaintData.csv_data_base64.trim().replace(/[\r\n\s]/g, '');

        // Check if it's actually base64 encoded
        const isBase64 = /^[A-Za-z0-9+/=]+$/.test(cleanBase64);

        if (isBase64) {
          decodedData = atob(cleanBase64);
        } else {
          decodedData = complaintData.csv_data_base64;
        }
      } catch (decodeError) {
        // If decoding fails, try using the data as-is
        decodedData = complaintData.csv_data_base64;
      }

      // Verify this is actually CSV data (should have commas and newlines)
      if (decodedData.includes(',') && (decodedData.includes('\n') || decodedData.includes('Layer'))) {

        // Clean up any metadata at the top of the CSV
        const lines = decodedData.split('\n');

        let cleanedData = decodedData;

        // If there are lines that look like base64 or dates at the top, remove them
        if (lines.length > 0) {

          const isBase64Line = lines[0].match(/^[A-Za-z0-9+/=]{20,}$/);
          const isDateLine = lines[0].match(/^\d{1,2}\/\d{1,2}\/\d{4}$/);
          const isNotCsvFormat = !lines[0].includes(',');

          if (isBase64Line || isDateLine || isNotCsvFormat) {
            // Find the first line that looks like a CSV header
            const headerIndex = lines.findIndex((line: string) =>
              line.includes(',') &&
              !line.match(/^[A-Za-z0-9+/=]{20,}$/) &&
              !line.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/));

            if (headerIndex > 0) {
              cleanedData = lines.slice(headerIndex).join('\n');
            }
          }
        }

        // Check if the cleaned data has a valid CSV structure
        const cleanedLines = cleanedData.split('\n').filter((line: string) => line.trim() !== '');
        if (cleanedLines.length > 0) {
          setCsvData(cleanedData);
        } else {

          // If we have transactions but no valid CSV, generate CSV from transactions
          if (complaintData.transactions && Array.isArray(complaintData.transactions) && complaintData.transactions.length > 0) {

            // Generate CSV from transactions - INCLUDE ALL COLUMNS in the exact order expected by the backend
            const headers = [
              'Layer',
              'Fraud Type',
              'Sender Account',
              'Sender Txn ID',
              'Sender Bank',
              'Receiver Account',
              'Txn ID',
              'Receiver Bank',
              'Txn Type',
              'Txn Date',
              'Amount',
              'Reference'
            ];

            let csvContent = headers.join(',') + '\n';

            complaintData.transactions.forEach((txn: any) => {
              const row = [
                txn.layer || '',
                txn.fraud_type || 'banking_upi', // Include fraud_type with default value
                txn.sender_account || '',
                txn.sender_transaction_id || '', // Include sender_txn_id
                txn.sender_bank || '',
                txn.receiver_account || '',
                txn.receiver_transaction_id || txn.txn_id || '', // Use receiver_transaction_id or txn_id
                txn.receiver_bank || '',
                txn.type || txn.txn_type || '', // Use type or txn_type
                txn.date || txn.txn_date || '', // Use date or txn_date
                txn.amount || '',
                txn.reference || txn.receiver_info || '' // Use reference or receiver_info
              ];

              const escapedRow = row.map(field => {
                if (field && field.toString().includes(',')) {
                  return `"${field}"`;
                }
                return field;
              });

              csvContent += escapedRow.join(',') + '\n';
            });

            setCsvData(csvContent);
          } else {
            showError('No valid CSV data found');
          }
        }
      } else {

        // Last resort: try to find any CSV-like content in the data
        const csvPattern = /[^,]+,[^,]+,[^,]+/;
        if (csvPattern.test(decodedData)) {
          setCsvData(decodedData);
        } else if (complaintData.transactions && Array.isArray(complaintData.transactions) && complaintData.transactions.length > 0) {
          // If we have transactions but no valid CSV, generate CSV from transactions

          // Generate CSV from transactions - INCLUDE ALL COLUMNS in the exact order expected by the backend
          const headers = [
            'Layer',
            'Fraud Type',
            'Sender Account',
            'Sender Txn ID',
            'Sender Bank',
            'Receiver Account',
            'Txn ID',
            'Receiver Bank',
            'Txn Type',
            'Txn Date',
            'Amount',
            'Reference'
          ];

          let csvContent = headers.join(',') + '\n';

          complaintData.transactions.forEach((txn: any) => {
            const row = [
              txn.layer || '',
              txn.fraud_type || 'banking_upi', // Include fraud_type with default value
              txn.sender_account || '',
              txn.sender_transaction_id || '', // Include sender_txn_id
              txn.sender_bank || '',
              txn.receiver_account || '',
              txn.receiver_transaction_id || txn.txn_id || '', // Use receiver_transaction_id or txn_id
              txn.receiver_bank || '',
              txn.type || txn.txn_type || '', // Use type or txn_type
              txn.date || txn.txn_date || '', // Use date or txn_date
              txn.amount || '',
              txn.reference || txn.receiver_info || '' // Use reference or receiver_info
            ];

            const escapedRow = row.map(field => {
              if (field && field.toString().includes(',')) {
                return `"${field}"`;
              }
              return field;
            });

            csvContent += escapedRow.join(',') + '\n';
          });

          setCsvData(csvContent);
        } else {
          showError('Invalid CSV data format');
        }
      }
    } catch (error) {

      // Last resort: if we have transactions, generate CSV from them
      if (complaintData.transactions && Array.isArray(complaintData.transactions) && complaintData.transactions.length > 0) {

        // Generate CSV from transactions - INCLUDE ALL COLUMNS in the exact order expected by the backend
        const headers = [
          'Layer',
          'Fraud Type',
          'Sender Account',
          'Sender Txn ID',
          'Sender Bank',
          'Receiver Account',
          'Txn ID',
          'Receiver Bank',
          'Txn Type',
          'Txn Date',
          'Amount',
          'Reference'
        ];

        let csvContent = headers.join(',') + '\n';

        complaintData.transactions.forEach((txn: any) => {
          const row = [
            txn.layer || '',
            txn.fraud_type || 'banking_upi', // Include fraud_type with default value
            txn.sender_account || '',
            txn.sender_transaction_id || '', // Include sender_txn_id
            txn.sender_bank || '',
            txn.receiver_account || '',
            txn.receiver_transaction_id || txn.txn_id || '', // Use receiver_transaction_id or txn_id
            txn.receiver_bank || '',
            txn.type || txn.txn_type || '', // Use type or txn_type
            txn.date || txn.txn_date || '', // Use date or txn_date
            txn.amount || '',
            txn.reference || txn.receiver_info || '' // Use reference or receiver_info
          ];

          const escapedRow = row.map(field => {
            if (field && field.toString().includes(',')) {
              return `"${field}"`;
            }
            return field;
          });

          csvContent += escapedRow.join(',') + '\n';
        });

        setCsvData(csvContent);
      } else {
        showError('Failed to process CSV data');
      }
    }
  }, [complaintData, showError]); // Use complaintData to ensure we update when the entire complaint changes

  // Handle CSV download
  const handleDownloadCSV = useCallback(async () => {
    if (!id) return;

    setIsDownloading(true);
    try {
      await complaintService.downloadCSV(id);
      showSuccess('Excel file downloaded successfully');
    } catch (error) {
      showError('Failed to download Excel file');
    } finally {
      setIsDownloading(false);
    }
  }, [id, showSuccess, showError]);

  // Handle CSV data edit using React Query mutation
  const handleCSVEdit = useCallback(async (newCsvData: string, operationMetadata?: any) => {
    if (!id) {
      return null;
    }

    // Check if user is authenticated
    if (!isAuthenticated || !user) {
      showError('You must be logged in to save changes');
      return null;
    }

    // Validate CSV data
    if (!newCsvData || newCsvData.trim() === '') {
      showError('Cannot save empty CSV data');
      return null;
    }

    // Validate that the CSV data contains all required fields
    const lines = newCsvData.split('\n');
    if (lines.length > 0) {
      const headers = lines[0].split(',').map(h => h.trim());
      const requiredFields = ['Txn ID', 'Txn Date', 'Txn Type', 'Amount'];
      const missingFields = requiredFields.filter(field => !headers.includes(field));

      if (missingFields.length > 0) {
        showError(`Missing required fields in CSV data: ${missingFields.join(', ')}`);
        return null;
      }
    }

    try {
      // Test if the mutation function exists
      if (!updateCSVMutation || !updateCSVMutation.mutateAsync) {
        showError('CSV update function is not available');
        return null;
      }

      // Use React Query mutation for CSV updates
      const result = await updateCSVMutation.mutateAsync({
        csvData: newCsvData,
        complaintId: id,
        operationMetadata: {
          ...operationMetadata,
          complaintMetadata: complaintData?.metadata || complaintData?.graph_data?.metadata
          // Note: summary is now part of metadata, no separate summary field
        }
      });

      // Only update local state for full updates, not row operations
      if (!operationMetadata || !operationMetadata.action) {
        setCsvData(newCsvData);
      }

      return result;
    } catch (error: any) {
      // Error handling is done in the mutation hook
      return null;
    }
  }, [id, updateCSVMutation, complaintData, showError]);

  // Navigate to graph visualization
  const handleGraphVisualization = () => {
    navigate(`/graph/${id}`);
  };

  // Navigate to notice generation
  const handleNoticeGeneration = () => {
    navigate(`/notices/generate/${id}`);
  };

  // Use a ref for the timeout instead of window property
  const totalsUpdateTimeoutRef = React.useRef<number | null>(null);

  // Clean up any unwanted strings in the CSV data
  const cleanedCsvData = React.useMemo(() => {
    if (!csvData) {
      return '';
    }

    // First, check if there's any token-like string in the data
    const hasToken = csvData.includes('Z0FBQUFBQm');
    const hasBase64Pattern = !!csvData.match(/^[A-Za-z0-9+/=]{20,}/);

    if (hasToken || hasBase64Pattern) {
      // Split by lines
      const lines = csvData.split('\n');

      // Find the first line that starts with 'Layer' or contains 'Layer,'
      const headerIndex = lines.findIndex((line: string) =>
        line.startsWith('Layer') || line.includes('Layer,'));

      if (headerIndex > 0) {
        // Only keep lines from the header onwards
        const cleanedLines = lines.slice(headerIndex);
        return cleanedLines.join('\n');
      }

      // If we can't find a Layer header, look for any line that has multiple commas (likely a CSV header)
      const csvHeaderIndex = lines.findIndex((line: string) => {
        const commaCount = (line.match(/,/g) || []).length;
        return commaCount >= 2 && !line.match(/^[A-Za-z0-9+/=]{20,}/);
      });

      if (csvHeaderIndex > 0) {
        // Only keep lines from this header onwards
        const cleanedLines = lines.slice(csvHeaderIndex);
        return cleanedLines.join('\n');
      }

      // If we can't find a header, try to remove any lines that look like tokens
      const filteredLines = lines.filter((line: string) =>
        !line.match(/^[A-Za-z0-9+/=]{20,}/) &&
        !line.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/) &&
        line.trim() !== ''
      );

      if (filteredLines.length > 0) {
        return filteredLines.join('\n');
      } else {
        return csvData;
      }
    }

    // If no tokens found, check if the data has a valid CSV structure
    const lines = csvData.split('\n').filter((line: string) => line.trim() !== '');
    if (lines.length === 0) {
      return '';
    }

    // Check if the first line has commas (likely a header)
    if (!lines[0].includes(',')) {
      // Look for the first line with commas
      const headerIndex = lines.findIndex((line: string) => line.includes(','));
      if (headerIndex > 0) {
        return lines.slice(headerIndex).join('\n');
      }
    }

    return csvData;
  }, [csvData]);

  // Add a style to hide any text that matches the token pattern
  React.useEffect(() => {
    // Create a style element
    const style = document.createElement('style');
    style.innerHTML = `
      /* Hide any text that matches the token pattern */
      *:not(style):not(script):not(head):not(meta):not(link):not(title):not(html):not(body):not(#root) {
        position: relative;
      }
      *:not(style):not(script):not(head):not(meta):not(link):not(title):not(html):not(body):not(#root)::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: inherit;
        z-index: 1;
        display: none;
      }
      *:contains('Z0FBQUFBQm')::before {
        display: block;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Cleanup timeouts when component unmounts
  React.useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (totalsUpdateTimeoutRef.current) {
        clearTimeout(totalsUpdateTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="w-full h-full min-w-0 max-w-full overflow-x-hidden complaint-details-container">
      {loading ? (
        <div className="flex justify-center items-center h-full w-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      ) : error ? (
        <div className="p-6 w-full">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4 text-red-800 dark:text-red-300">
            <p>{error?.message || 'An error occurred while loading the complaint'}</p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col w-full h-full min-w-0 max-w-full complaint-details-content">
          {/* Header with complaint info and navigation */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 w-full p-4">
            <div className="flex-shrink-0">
              <div className="flex items-center gap-2 mb-1">
                <Button
                  variant="outlined"
                  size="sm"
                  startIcon={<FiArrowLeft />}
                  onClick={() => navigate('/dashboard')}
                >
                  Back to Dashboard
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 justify-end">
              <Button
                variant="primary"
                startIcon={<FiBarChart2 />}
                onClick={handleGraphVisualization}
                size="sm"
              >
                Graph Analysis
              </Button>
              <Button
                variant="secondary"
                startIcon={<FiFileText />}
                onClick={handleNoticeGeneration}
                size="sm"
              >
                Generate Notices
              </Button>
            </div>
          </div>

          {/* CSV Preview Section - Flex grow to fill available space */}
          <div className="flex-grow w-full max-w-full overflow-x-auto complaint-details-table px-4">
            <ReactTableCSVPreview
              csvData={cleanedCsvData}
              onSave={handleCSVEdit}
              onDownload={handleDownloadCSV}
              isLoading={isLoading}
              hiddenColumns={['Fraud Type', 'Sender Txn ID']}
              complaintMetadata={{
                metadata: complaintData?.metadata || complaintData?.graph_data?.metadata
              }}
              onTotalsCalculated={(hold: number, withdrawal: number) => {
                if (complaintData && id) {
                  if (totalsUpdateTimeoutRef.current) {
                    clearTimeout(totalsUpdateTimeoutRef.current);
                  }

                  totalsUpdateTimeoutRef.current = window.setTimeout(() => {
                    complaintService.updateComplaint(id, {
                      financial_details: {
                        transaction_totals: {
                          total_withdrawal: withdrawal,
                          total_hold: hold
                        }
                      }
                    }).then(() => {
                    }).catch(() => {
                    });
                  }, 1000);
                }
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Apply the layout configuration using the HOC
const ComplaintDetail = withLayout(ComplaintDetailComponent, {
  fullWidth: true,
  customPadding: '0',
  customLeftMargin: '0'
});

export default ComplaintDetail;
