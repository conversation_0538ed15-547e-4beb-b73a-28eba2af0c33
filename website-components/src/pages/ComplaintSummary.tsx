import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import { FiArrowLeft, FiEdit, FiPrinter, FiTrash2 } from 'react-icons/fi';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { withLayout } from '../context/LayoutContext';
import complaintService from '../services/complaintService';
import { useReactToPrint } from 'react-to-print';

interface Note {
  id: string;
  text: string;
  timestamp: string;
}

interface ComplaintSummaryData {
  complaint_details: {
    complaint_number: string;
    date: string;
    category: string;
    subcategory: string;
    status: string;
  };
  complainant_details: {
    name: string;
    gender?: string;
    dob?: string;
    mobile?: string;
    email?: string;
    relationship?: string;
    address?: Record<string, string>;
  };
  financial_details: {
    total_fraudulent_amount: number;
    total_lien_amount: number;
    transaction_totals: {
      atm_withdrawal: number;
      cheque_withdrawal: number;
      online_transfer: number;
      total_withdrawal: number;
    };
  };
  notes: string;
}

const ComplaintSummaryComponent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showError, showSuccess } = useAlert();

  const [loading, setLoading] = useState(true);
  const [summaryData, setSummaryData] = useState<ComplaintSummaryData | null>(null);
  const [notes, setNotes] = useState<Note[]>([]);
  const [currentNote, setCurrentNote] = useState('');
  const [isEditingNote, setIsEditingNote] = useState<string | null>(null);

  // Reference for printing
  const printRef = useRef<HTMLDivElement>(null);

  // Generate a unique ID for notes
  const generateNoteId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Load notes from local storage
  const loadNotesFromLocalStorage = (complaintId: string): Note[] => {
    try {
      const savedNotes = localStorage.getItem(`complaint_notes_${complaintId}`);
      if (savedNotes) {
        return JSON.parse(savedNotes);
      }
      return [];
    } catch (error) {
      return [];
    }
  };

  // Save notes to local storage
  const saveNotesToLocalStorage = (complaintId: string, notesList: Note[]) => {
    try {
      localStorage.setItem(`complaint_notes_${complaintId}`, JSON.stringify(notesList));
    } catch (error) {
    }
  };

  // Add a new note
  const addNote = () => {
    if (!currentNote.trim() || !id) return;

    const newNote: Note = {
      id: generateNoteId(),
      text: currentNote.trim(),
      timestamp: 'Added'
    };

    const updatedNotes = [...notes, newNote];
    setNotes(updatedNotes);
    saveNotesToLocalStorage(id, updatedNotes);
    setCurrentNote('');

    // Try to save to backend as well
    try {
      complaintService.updateComplaintNotes(id, JSON.stringify(updatedNotes));
    } catch (error) {
    }
  };

  // Delete a note
  const deleteNote = (noteId: string) => {
    if (!id) return;

    const updatedNotes = notes.filter(note => note.id !== noteId);
    setNotes(updatedNotes);
    saveNotesToLocalStorage(id, updatedNotes);

    // Try to save to backend as well
    try {
      complaintService.updateComplaintNotes(id, JSON.stringify(updatedNotes));
    } catch (error) {
    }
  };

  // Edit a note
  const startEditingNote = (noteId: string) => {
    setIsEditingNote(noteId);
    const noteToEdit = notes.find(note => note.id === noteId);
    if (noteToEdit) {
      setCurrentNote(noteToEdit.text);
    }
  };

  // Save edited note
  const saveEditedNote = () => {
    if (!isEditingNote || !id) return;

    const updatedNotes = notes.map(note =>
      note.id === isEditingNote
        ? { ...note, text: currentNote.trim(), timestamp: 'Edited' }
        : note
    );

    setNotes(updatedNotes);
    saveNotesToLocalStorage(id, updatedNotes);
    setIsEditingNote(null);
    setCurrentNote('');

    // Try to save to backend as well
    try {
      complaintService.updateComplaintNotes(id, JSON.stringify(updatedNotes));
    } catch (error) {
    }
  };

  // Handle printing
  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: `Complaint_Summary_${id}`,
    onBeforePrint: () => {
      return new Promise<void>((resolve) => {
        setIsEditingNote(null); // Make sure we're not in edit mode when printing

        // Ensure the print ref is valid before proceeding
        if (!printRef.current) {
          showError('Print preparation failed. Please try again.');
          resolve();
          return;
        }

        // Force all content to be visible before printing
        const printContainer = printRef.current;

        // Find all scrollable elements and make them visible
        const scrollableElements = printContainer.querySelectorAll('.overflow-y-auto, .overflow-x-auto, .overflow-auto, .flex-grow, .flex-1');

        scrollableElements.forEach((element) => {
          const htmlElement = element as HTMLElement;
          htmlElement.style.overflow = 'visible';
          htmlElement.style.height = 'auto';
          htmlElement.style.maxHeight = 'none';
          htmlElement.style.flexGrow = '0';
        });

        // Compact the main content for printing
        const mainContent = printContainer.querySelector('.flex-shrink-0') as HTMLElement;
        if (mainContent) {
          // Use breakInside instead of deprecated pageBreakInside
          mainContent.style.breakInside = 'avoid';
          mainContent.style.marginBottom = '0';
          mainContent.style.paddingBottom = '0';
        }

        // Reduce spacing between sections
        const spacingElements = printContainer.querySelectorAll('.gap-2, .gap-3, .gap-4, .mb-2, .mb-3, .mb-4, .p-2, .p-3, .p-4');
        spacingElements.forEach((element) => {
          const htmlElement = element as HTMLElement;
          if (htmlElement.classList.contains('gap-2') || htmlElement.classList.contains('gap-3') || htmlElement.classList.contains('gap-4')) {
            htmlElement.style.gap = '0.25rem';
          }
          if (htmlElement.classList.contains('mb-2') || htmlElement.classList.contains('mb-3') || htmlElement.classList.contains('mb-4')) {
            htmlElement.style.marginBottom = '0.25rem';
          }
          if (htmlElement.classList.contains('p-2') || htmlElement.classList.contains('p-3') || htmlElement.classList.contains('p-4')) {
            htmlElement.style.padding = '0.5rem';
          }
        });

        // Increase timeout to ensure content is fully rendered
        setTimeout(() => {
          resolve();
        }, 1000);
      });
    },
    onAfterPrint: () => {
      // Reset all modified styles after printing
      if (printRef.current) {
        // Reset scrollable elements
        const scrollableElements = printRef.current.querySelectorAll('.overflow-y-auto, .overflow-x-auto, .overflow-auto, .flex-grow, .flex-1');
        scrollableElements.forEach((element) => {
          const htmlElement = element as HTMLElement;
          htmlElement.style.overflow = '';
          htmlElement.style.height = '';
          htmlElement.style.maxHeight = '';
          htmlElement.style.flexGrow = '';
        });

        // Reset main content
        const mainContent = printRef.current.querySelector('.flex-shrink-0') as HTMLElement;
        if (mainContent) {
          mainContent.style.breakInside = '';
          mainContent.style.marginBottom = '';
          mainContent.style.paddingBottom = '';
        }

        // Reset spacing elements
        const spacingElements = printRef.current.querySelectorAll('.gap-2, .gap-3, .gap-4, .mb-2, .mb-3, .mb-4, .p-2, .p-3, .p-4');
        spacingElements.forEach((element) => {
          const htmlElement = element as HTMLElement;
          htmlElement.style.gap = '';
          htmlElement.style.marginBottom = '';
          htmlElement.style.padding = '';
        });
      }

      showSuccess('Summary printed successfully');
    },
    onPrintError: (_errorLocation, _error) => {
      showError('Failed to print. Please try again.');
    }
  });

  // Fetch summary data
  useEffect(() => {
    const fetchSummaryData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await complaintService.getComplaintSummary(id);

        // Also fetch the complaint data to get the correct total withdrawal amount
        const complaintData = await complaintService.getComplaintById(id);

        // Get the total withdrawal amount from the complaint data if available
        let totalWithdrawal = 0;
        if (complaintData && complaintData.csv_data && complaintData.csv_data.length > 0) {
          // Calculate total withdrawal from CSV data
          totalWithdrawal = complaintData.csv_data
            .filter((row: any) =>
              row.txn_type !== 'Transaction Put on Hold' &&
              row.txn_type !== 'Others' &&
              !row.txn_type?.includes('Money Transfer to')
            )
            .reduce((sum: number, row: any) => sum + (parseFloat(row.amount) || 0), 0);
        } else if (complaintData && complaintData.financial_details &&
            complaintData.financial_details.transaction_totals &&
            complaintData.financial_details.transaction_totals.total_withdrawal) {
          totalWithdrawal = complaintData.financial_details.transaction_totals.total_withdrawal;
        }

        // Process and clean the data - now all data comes from the consolidated metadata structure
        // The backend now returns data in the same structure but sourced from consolidated metadata
        // Summary page structure is preserved - no structural changes, just data source consolidation
        const processedData = {
          complaint_details: {
            complaint_number: data.complaint_details?.complaint_number || id,
            date: data.complaint_details?.date || '',
            category: data.complaint_details?.category || 'Unknown',
            subcategory: data.complaint_details?.subcategory || 'Unknown',
            status: data.complaint_details?.status || 'Pending'
          },
          complainant_details: {
            name: data.complainant_details?.name || 'Unknown',
            mobile: data.complainant_details?.mobile || '',
            email: data.complainant_details?.email || '',
            phone: data.complainant_details?.phone || '',
            gender: data.complainant_details?.gender || '',
            relationship: data.complainant_details?.relationship || '',
            address: data.complainant_details?.address || {}
          },
          financial_details: {
            total_fraudulent_amount: data.financial_details?.total_fraudulent_amount || 0,
            total_lien_amount: data.financial_details?.total_lien_amount || 0,
            transaction_totals: {
              atm_withdrawal: data.financial_details?.transaction_totals?.atm_withdrawal || 0,
              cheque_withdrawal: data.financial_details?.transaction_totals?.cheque_withdrawal || 0,
              online_transfer: data.financial_details?.transaction_totals?.online_transfer || 0,
              total_withdrawal: totalWithdrawal || data.financial_details?.transaction_totals?.total_withdrawal || 0
            }
          },
          notes: data.notes || ''
        };

        setSummaryData(processedData);

        // Try to load notes from local storage first
        let notesData: Note[] = loadNotesFromLocalStorage(id);

        // If no notes in local storage, try to convert backend notes to our format
        if (notesData.length === 0 && processedData.notes) {
          try {
            // First check if it's already in our JSON format
            const parsedNotes = JSON.parse(processedData.notes);
            if (Array.isArray(parsedNotes) && parsedNotes.length > 0 && 'id' in parsedNotes[0]) {
              notesData = parsedNotes;
            } else {
              // If it's just a string, convert it to our format
              notesData = [{
                id: generateNoteId(),
                text: processedData.notes,
                timestamp: 'Imported'
              }];
            }
          } catch (e) {
            // If it's not valid JSON, treat it as a plain string
            if (processedData.notes.trim()) {
              notesData = [{
                id: generateNoteId(),
                text: processedData.notes,
                timestamp: 'Imported'
              }];
            }
          }
        }

        setNotes(notesData);

        // Save the converted notes to local storage
        if (notesData.length > 0) {
          saveNotesToLocalStorage(id, notesData);
        }
      } catch (error) {
        showError('Failed to load complaint summary data');
      } finally {
        setLoading(false);
      }
    };

    fetchSummaryData();
  }, [id, showError]);

  // Format currency values
  const formatCurrency = (value: string | number | undefined): string => {
    if (value === undefined || value === null) return '₹0.00';

    // Convert to number if it's a string
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value;

    // Check if it's a valid number
    if (isNaN(numValue)) return '₹0.00';

    // Format with Indian numbering system (commas at thousands, lakhs, crores)
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numValue);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!summaryData) {
    return (
      <div className="p-6 w-full">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4 text-red-800 dark:text-red-300">
          <p>No summary data available for this complaint.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full min-w-0 max-w-100vw overflow-x-hidden p-4 relative">
      {/* Header with navigation */}
      <div className="flex flex-wrap justify-between items-center mb-4 gap-2">
        <Button
          variant="outlined"
          size="sm"
          startIcon={<FiArrowLeft className="bg-transparent" />}
          onClick={() => navigate('/dashboard')}
        >
          Back to Dashboard
        </Button>
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            variant="primary"
            size="sm"
            startIcon={<FiPrinter className="bg-transparent" />}
            onClick={handlePrint}
          >
            Print Summary
          </Button>
          <h1 className="text-xl font-bold truncate">
            Complaint Summary: {summaryData.complaint_details.complaint_number}
          </h1>
        </div>
      </div>

      {/* Printable content */}
      <div ref={printRef} className="print-container">
        <div className="flex flex-col lg:flex-row gap-4 w-full">
          {/* Main content - 70% width */}
          <div style={{width: '70%'}} className="flex-shrink-0">
            <Card>
              <Card.Header>
                <h2 className="text-lg font-semibold dark:cyber-text-gradient">Complaint Summary</h2>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 gap-2">
                  {/* Complaint Details Section */}
                  <div>
                    <h3 className="text-md font-semibold mb-2 border-b pb-1">Complaint Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      <div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Complaint Number</p>
                        <p className="font-medium text-sm">{summaryData.complaint_details.complaint_number}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Date</p>
                        <p className="font-medium text-sm">{summaryData.complaint_details.date}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Category</p>
                        <p className="font-medium text-sm">{summaryData.complaint_details.category}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Subcategory</p>
                        <p className="font-medium text-sm">{summaryData.complaint_details.subcategory}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Status</p>
                        <p className="font-medium text-sm">{summaryData.complaint_details.status}</p>
                      </div>
                    </div>
                  </div>

                  {/* Financial Details Section */}
                  <div>
                    <h3 className="text-md font-semibold mb-2 border-b pb-1">Financial Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                      <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg p-2">
                        <p className="text-xs text-gray-500 dark:text-gray-400">Total Fraudulent Amount</p>
                        <p className="font-bold text-lg text-gray-900 dark:text-amber-400">
                          {formatCurrency(summaryData.financial_details.total_fraudulent_amount)}
                        </p>
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 rounded-lg p-2">
                        <p className="text-xs text-gray-500 dark:text-gray-400">Total Lien/Hold Amount</p>
                        <p className="font-bold text-lg text-gray-900 dark:text-green-400">
                          {formatCurrency(summaryData.financial_details.total_lien_amount)}
                        </p>
                      </div>
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-2">
                        <p className="text-xs text-gray-500 dark:text-gray-400">Total Withdrawal Amount</p>
                        <p className="font-bold text-lg text-gray-900 dark:text-red-400">
                          {formatCurrency(summaryData.financial_details.transaction_totals.total_withdrawal)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Complainant Details Section */}
                  <div>
                    <h3 className="text-md font-semibold mb-2 border-b pb-1">Complainant Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Personal Details Column */}
                      <div className="bg-gray-50/50 dark:bg-gray-800/30 rounded-lg p-3">
                        <h4 className="text-sm font-semibold mb-2 border-b border-gray-200 dark:border-gray-700 pb-1">Personal Information</h4>
                        <div className="grid grid-cols-1 gap-2">
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Name</p>
                            <p className="font-medium text-sm">{summaryData.complainant_details.name}</p>
                          </div>

                          {summaryData.complainant_details.mobile && (
                            <div>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Mobile</p>
                              <p className="font-medium text-sm">{summaryData.complainant_details.mobile}</p>
                            </div>
                          )}

                          {summaryData.complainant_details.email && (
                            <div>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Email</p>
                              <p className="font-medium text-sm">
                                {summaryData.complainant_details.email}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Address Details Column */}
                      {summaryData.complainant_details.address &&
                       Object.keys(summaryData.complainant_details.address).length > 0 && (
                        <div className="bg-gray-50/50 dark:bg-gray-800/30 rounded-lg p-3">
                          <h4 className="text-sm font-semibold mb-2 border-b border-gray-200 dark:border-gray-700 pb-1">Address Information</h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            {summaryData.complainant_details.address.full_address ? (
                              <div className="col-span-1 sm:col-span-2">
                                <p className="text-xs text-gray-500 dark:text-gray-400">Full Address</p>
                                <p className="font-medium text-sm">{summaryData.complainant_details.address.full_address}</p>
                              </div>
                            ) : (
                              <>
                                {summaryData.complainant_details.address.house_no && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">House No</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.house_no}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.street && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Street</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.street}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.colony && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Colony</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.colony}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.village_town_city && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Village/Town/City</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.village_town_city}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.district && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">District</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.district}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.state && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">State</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.state}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.country && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Country</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.country}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.pin_code && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">PIN Code</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.pin_code}</p>
                                  </div>
                                )}

                                {summaryData.complainant_details.address.police_station && (
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">Police Station</p>
                                    <p className="font-medium text-sm">{summaryData.complainant_details.address.police_station}</p>
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Card.Content>
            </Card>
          </div>

          {/* Notes section - 30% width */}
          <div className="w-full lg:w-[30%] lg:min-w-[300px] lg:max-w-[30%]">
            <Card className="mb-4 h-[calc(100vh-11rem)]">
              <Card.Header className="flex justify-between items-center">
                <h3 className="text-md font-semibold">Notes</h3>
              </Card.Header>
              <Card.Content className="flex flex-col h-[calc(100%-56px)]">
                {/* Add new note form */}
                {isEditingNote === null && (
                  <div className="mb-4 flex-shrink-0">
                    <textarea
                      rows={3}
                      value={currentNote}
                      onChange={(e) => setCurrentNote(e.target.value)}
                      placeholder="Add a new note..."
                      className="w-full block text-sm border rounded-lg px-4 py-2.5 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-400 focus:outline-none focus:ring-2 mb-2"
                      style={{
                        backgroundColor: 'var(--theme-bg-card)',
                        borderColor: 'var(--theme-border)',
                        boxShadow: 'none'
                      }}
                    />
                    <div className="flex justify-end">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={addNote}
                        disabled={!currentNote.trim()}
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                )}

                {/* Edit note form */}
                {isEditingNote && (
                  <div className="mb-4 flex-shrink-0">
                    <textarea
                      rows={3}
                      value={currentNote}
                      onChange={(e) => setCurrentNote(e.target.value)}
                      className="w-full block text-sm border rounded-lg px-4 py-2.5 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-400 focus:outline-none focus:ring-2 mb-2"
                      style={{
                        backgroundColor: 'var(--theme-bg-card)',
                        borderColor: 'var(--theme-border)',
                        boxShadow: 'none'
                      }}
                    />
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outlined"
                        size="sm"
                        onClick={() => {
                          setIsEditingNote(null);
                          setCurrentNote('');
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={saveEditedNote}
                        disabled={!currentNote.trim()}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                )}

                {/* Notes list - using flex-grow to fill remaining space */}
                <div className="space-y-4 flex-grow overflow-y-auto px-1 py-2" style={{
                  minHeight: '200px'
                }}>
                  {notes.length === 0 ? (
                    <p className="text-center text-gray-500 dark:text-gray-400 py-4">No notes added yet.</p>
                  ) : (
                    notes.map((note) => (
                      <div
                        key={note.id}
                        className="p-3 rounded-lg border transition-all duration-300 hover:-translate-y-0.5"
                        style={{
                          backgroundColor: 'var(--theme-bg-card)',
                          borderColor: 'var(--theme-border)',
                          borderLeft: '3px solid var(--theme-button)',
                          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
                          cursor: 'default'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1), 0 0 8px var(--theme-glow)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                        }}
                      >
                        <div className="flex justify-between items-start mb-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400">{note.timestamp}</span>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => startEditingNote(note.id)}
                              className="text-primary-500 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 bg-transparent p-1 rounded-full hover:bg-primary-100/30 dark:hover:bg-primary-900/30 transition-colors"
                              aria-label="Edit note"
                              style={{
                                boxShadow: '0 0 0 transparent',
                                transition: 'all 0.2s ease-in-out'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.boxShadow = '0 0 5px var(--theme-glow)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.boxShadow = '0 0 0 transparent';
                              }}
                            >
                              <FiEdit size={14} className="bg-transparent" />
                            </button>
                            <button
                              onClick={() => deleteNote(note.id)}
                              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 bg-transparent p-1 rounded-full hover:bg-red-100/30 dark:hover:bg-red-900/30 transition-colors"
                              aria-label="Delete note"
                              style={{
                                boxShadow: '0 0 0 transparent',
                                transition: 'all 0.2s ease-in-out'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.boxShadow = '0 0 5px rgba(244, 67, 54, 0.5)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.boxShadow = '0 0 0 transparent';
                              }}
                            >
                              <FiTrash2 size={14} className="bg-transparent" />
                            </button>
                          </div>
                        </div>
                        <p className="whitespace-pre-wrap text-sm">{note.text}</p>
                      </div>
                    ))
                  )}
                </div>
              </Card.Content>
            </Card>
          </div>
        </div>
      </div>

      {/* Print styles */}
      <style>{`
        @media print {
          @page {
            size: auto;
            margin: 10mm;
          }

          html, body {
            height: auto !important;
            overflow: visible !important;
          }

          body * {
            visibility: hidden;
          }

          .print-container, .print-container * {
            visibility: visible;
            overflow: visible !important;
          }

          .print-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: auto !important;
            padding: 20px;
            overflow: visible !important;
          }

          button, .no-print {
            display: none !important;
          }

          /* Ensure the layout is optimized for printing */
          .print-container .flex-col,
          .print-container .flex-row,
          .print-container .lg\\:flex-row {
            display: block !important;
          }

          /* Keep main content together on one page */
          .print-container .flex-shrink-0 {
            page-break-inside: avoid !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
          }

          /* Reduce spacing between sections */
          .print-container .gap-2,
          .print-container .gap-3,
          .print-container .gap-4 {
            gap: 0.25rem !important;
          }

          .print-container .mb-2,
          .print-container .mb-3,
          .print-container .mb-4 {
            margin-bottom: 0.25rem !important;
          }

          .print-container .p-2,
          .print-container .p-3,
          .print-container .p-4 {
            padding: 0.5rem !important;
          }

          /* Adjust width for print layout */
          .print-container .lg\\:w-\\[70\\%\\] {
            width: 100% !important;
            max-width: 100% !important;
            margin-bottom: 0 !important;
          }

          /* Force page break before notes section */
          .print-container .lg\\:w-\\[30\\%\\],
          .print-container .w-full.lg\\:w-\\[30\\%\\] {
            width: 100% !important;
            max-width: 100% !important;
            page-break-before: always !important;
            margin-top: 1rem !important;
          }

          /* Ensure all content is visible */
          .print-container .max-h-\\[500px\\],
          .print-container .h-\\[calc\\(100vh-11rem\\)\\],
          .print-container .h-\\[calc\\(100vh-350px\\)\\],
          .print-container .min-h-\\[300px\\],
          .print-container .overflow-y-auto,
          .print-container .overflow-x-hidden,
          .print-container .overflow-hidden,
          .print-container .flex-grow,
          .print-container .flex-1 {
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
            flex-grow: 0 !important;
          }

          /* Ensure proper page breaks */
          .print-container h2,
          .print-container h3,
          .print-container h4 {
            page-break-after: avoid !important;
            margin-bottom: 0.25rem !important;
          }

          /* Ensure notes are properly displayed */
          .print-container .space-y-3 {
            display: block !important;
            height: auto !important;
            margin-top: 0.5rem !important;
          }

          /* Compact the financial details cards */
          .print-container .grid.grid-cols-1.md\\:grid-cols-3 {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 0.5rem !important;
          }

          /* Compact the complainant details section */
          .print-container .grid.grid-cols-1.md\\:grid-cols-2 {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 0.5rem !important;
          }
        }
      `}</style>
    </div>
  );
};

// Apply the layout configuration using the HOC
const ComplaintSummary = withLayout(ComplaintSummaryComponent, {
  customPadding: '0 1rem',
});

export default ComplaintSummary;
