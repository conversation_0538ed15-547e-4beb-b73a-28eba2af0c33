import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import complaintService from '../services/complaintService';

import { useAuth } from '../context/AuthContext';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import FraudTypeSelector, { fraudTypes } from '../components/FraudTypeSelector';
import LoadingDialog from '../components/ui/LoadingDialog';
import { useThemeContext } from '../context/TailwindThemeContext';
import Dialog from '../components/ui/Dialog';
import { useClearComplaintCache } from '../hooks/useComplaintMutations';
import useSmartError from '../hooks/useSmartError';

// Icons
import {
  FiUpload,
  FiFile,
  FiTrash2,
  FiAlertTriangle,
  FiArrowLeft,
} from 'react-icons/fi';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const TailwindSimpleFileUpload: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [error, setError] = useState<string>('');
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [processingStep, setProcessingStep] = useState<string>('');
  const [layerDepth, setLayerDepth] = useState<number>(7); // Default to layer 7
  const [duplicateComplaint, setDuplicateComplaint] = useState<any>(null);
  const [fraudType, setFraudType] = useState<string | null>(null);
  const [showTransactionDialog, setShowTransactionDialog] = useState(false);
  const [transactionWarning, setTransactionWarning] = useState<any>(null);
  const [customThreshold, setCustomThreshold] = useState<number>(1000);
  const [extractionType, setExtractionType] = useState<string | null>(null);
  const [showTemplatePrompt, setShowTemplatePrompt] = useState<boolean>(false);
  const [templateLoaded, setTemplateLoaded] = useState<boolean>(false);
  const { isDark } = useThemeContext();
  // Help dialog removed and moved to profile page

  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  const { showSuccess, showError, showInfo } = useAlert();
  const clearComplaintCache = useClearComplaintCache();
  const { handleError } = useSmartError();

  // Get the fraud type name for display
  const getFraudTypeName = () => {
    if (!fraudType) return null;
    const selectedType = fraudTypes.find(type => type.id === fraudType);
    return selectedType ? selectedType.name : null;
  };

  // Load user template on component mount
  useEffect(() => {
    if (user) {
      loadUserTemplate();
    }
  }, [user]);

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError('');

    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const selectedFile = event.target.files[0];

    // Validate file type - HTML only
    const validTypes = ['text/html'];
    if (!validTypes.includes(selectedFile.type)) {
      setError('Please select an HTML file');
      return;
    }

    // Validate file size
    if (selectedFile.size > MAX_FILE_SIZE) {
      setError('File size exceeds the 10MB limit');
      return;
    }

    setFile(selectedFile);
    showInfo('File selected. Click "Process Complaint" to proceed.');
  };

  // Handle file browse button click
  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file removal
  const handleRemoveFile = () => {
    setFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];

      // Validate file type - HTML only
      const validTypes = ['text/html'];
      if (!validTypes.includes(droppedFile.type)) {
        setError('Please select an HTML file');
        return;
      }

      // Validate file size
      if (droppedFile.size > MAX_FILE_SIZE) {
        setError('File size exceeds the 10MB limit');
        return;
      }

      setFile(droppedFile);
      showInfo('File selected. Click "Process Complaint" to proceed.');
    }
  };

  // Function to handle file upload and extraction
  const handleUpload = async () => {
    if (!file) {
      showError('Please select a file first');
      return;
    }

    if (!fraudType) {
      showError('Please select a fraud type');
      return;
    }

    // Check if user has a template, if not show the prompt
    // We only need to check the templateLoaded state which is set in loadUserTemplate
    if (!templateLoaded && !showTemplatePrompt) {
      setShowTemplatePrompt(true);
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError('');

    try {
      // Create FormData object
      const formData = new FormData();
      formData.append('file', file);

      // Ensure max_layer is always sent as a string and matches the user's selection
      // Validate that layerDepth is a valid integer between 2 and 7
      const maxLayerToSend = Number.isInteger(layerDepth) && layerDepth >= 2 && layerDepth <= 7
        ? layerDepth.toString()
        : '7'; // Default to 7 if invalid
      formData.append('max_layer', maxLayerToSend);

      // Add fraud type if selected
      if (fraudType) {
        formData.append('fraud_type', fraudType);
      }

      // Add extraction type if available
      if (extractionType) {
        formData.append('extraction_type', extractionType);
      }

      // If there's a duplicate complaint, handle replacement
      if (duplicateComplaint) {
        formData.append('replace_existing', 'true');
        formData.append('existing_complaint_id', duplicateComplaint.id);
      }

      // Step 1: Upload file and extract data
      setProcessingStep('Uploading file and extracting data...');
      const extractionResponse = await complaintService.processComplaintFile(
        formData,
        (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(Math.min(progress, 90)); // Stop at 90% until saving
        }
      );

      if (extractionResponse && extractionResponse.data) {
        // Step 2: Process the extracted data
        setProcessingStep('Processing extracted data...');
        setUploadProgress(50);

        // Check for duplicate complaint
        if (extractionResponse.data.duplicate_found && !duplicateComplaint) {
          const duplicateInfo = extractionResponse.data.duplicate_info;
          setDuplicateComplaint(duplicateInfo);
          setIsUploading(false);
          setProcessingStep('');

          // Show a message about the duplicate
          showInfo(`A complaint with number ${duplicateInfo.complaint_number} already exists. Do you want to replace it?`);
          return;
        }

        // Check for high transaction count warning
        if (extractionResponse.data.transaction_count_warning) {
          const warning = extractionResponse.data.transaction_count_warning;
          setIsUploading(false);
          setProcessingStep('');
          setTransactionWarning(warning);
          setCustomThreshold(warning.suggested_threshold);
          setShowTransactionDialog(true);
          return; // Stop processing until user makes a decision
        }

        // Step 3: The graph data is already included in the extractionResponse
        setProcessingStep('Processing graph visualization...');
        setUploadProgress(70);

        // Extract graph data from the extraction response
        const graphData = extractionResponse.data.graph_data;

        if (graphData) {
          // Step 4: Store metadata in MongoDB
          setProcessingStep('Saving complaint data...');
          setUploadProgress(90);

          const metadataResponse = await complaintService.createComplaint({
            complaintData: extractionResponse.data,
            graphData: graphData
          });

          if (metadataResponse) {
            // Now complete the progress to 100%
            setUploadProgress(100);
            const complaintId = metadataResponse.complaint_id || metadataResponse.id || metadataResponse._id;

            // Make sure we have a valid complaint ID
            if (!complaintId) {
              showError('Failed to get complaint ID from server');
              setProcessingStep('');
              setIsUploading(false);
              return;
            }

            showSuccess('Complaint processed successfully!');

            // Clear all complaint cache to ensure fresh data when navigating
            clearComplaintCache();

            // Redirect to the complaint detail page
            navigate(`/complaint/${complaintId}`);
          } else {
            throw new Error('Failed to save complaint metadata');
          }
        } else {
          throw new Error('Data extraction failed');
        }
      }
    } catch (error: any) {
      // Use smart error handling for user-friendly messages
      await handleError(error, 'complaint upload');

      // Set a generic error state for the component
      setError('Failed to process complaint. Please try again.');
    } finally {
      setIsUploading(false);
      setProcessingStep('');
    }
  };

  // Handle transaction filtering decision
  const handleTransactionFiltering = async (useFiltering: boolean) => {
    setShowTransactionDialog(false);

    if (!transactionWarning || !file) return;

    try {
      setIsUploading(true);
      setProcessingStep(useFiltering ? 'Re-processing with transaction filtering...' : 'Processing all transactions...');
      setUploadProgress(10);

      const newFormData = new FormData();
      newFormData.append('file', file);
      newFormData.append('max_layer', layerDepth.toString());
      if (fraudType) newFormData.append('fraud_type', fraudType);
      if (extractionType) newFormData.append('extraction_type', extractionType);

      if (useFiltering) {
        newFormData.append('skip_low_value_threshold', customThreshold.toString());
      }

      // If there's a duplicate complaint, handle replacement
      if (duplicateComplaint) {
        newFormData.append('replace_existing', 'true');
        newFormData.append('existing_complaint_id', duplicateComplaint.id);
      }

      const filteredResponse = await complaintService.processComplaintFile(
        newFormData,
        (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(Math.min(progress, 90)); // Stop at 90% until saving
        }
      );

      // Continue processing with the response
      if (filteredResponse && filteredResponse.data) {
        // Continue with the normal flow
        setProcessingStep('Processing graph visualization...');
        setUploadProgress(70);

        const graphData = filteredResponse.data.graph_data;
        if (graphData) {
          setProcessingStep('Saving complaint data...');
          setUploadProgress(90);

          const metadataResponse = await complaintService.createComplaint({
            complaintData: filteredResponse.data,
            graphData: graphData
          });

          if (metadataResponse) {
            setUploadProgress(100);
            const complaintId = metadataResponse.complaint_id || metadataResponse.id || metadataResponse._id;

            if (!complaintId) {
              showError('Failed to get complaint ID from server');
              setProcessingStep('');
              setIsUploading(false);
              return;
            }

            showSuccess('Complaint processed successfully!');
            clearComplaintCache();
            navigate(`/complaint/${complaintId}`);
          } else {
            throw new Error('Failed to save complaint metadata');
          }
        } else {
          throw new Error('Data extraction failed');
        }
      } else {
        throw new Error('Failed to process complaint');
      }
    } catch (error: any) {
      await handleError(error, 'complaint processing');
      setError('Failed to process complaint. Please try again.');
    } finally {
      setIsUploading(false);
      setProcessingStep('');
      setTransactionWarning(null);
    }
  };

  // Function to load user template from backend
  const loadUserTemplate = async () => {
    try {
      // First check if we already know the user has a template from their profile
      // The has_template boolean in the user object is the source of truth
      if (user && user.has_template === true) {
        setTemplateLoaded(true);
        // Don't show the prompt if user has a template
        setShowTemplatePrompt(false);
        return;
      }

      // If user.has_template is explicitly false, show the prompt
      if (user && user.has_template === false) {
        setTemplateLoaded(false);
        setShowTemplatePrompt(true);
        return;
      }

      // If we're not sure from the user object, check the cache
      const cachedTemplate = sessionStorage.getItem('user_template');
      if (cachedTemplate) {
        setTemplateLoaded(true);
        setShowTemplatePrompt(false);
        return;
      }

      // If we still don't know, don't show the prompt by default
      // This prevents showing it unnecessarily
      setShowTemplatePrompt(false);
    } catch (error) {
      // Use smart error handling but don't show critical errors for template loading
      await handleError(error, 'template loading');
      // Don't show the prompt by default on errors
      setShowTemplatePrompt(false);
    }
  };

  // Paid access check removed - now handled globally by ProtectedRoute

  // Render the upload form
  return (
    <div className="max-w-7xl mx-auto px-4 pt-6 pb-8 overflow-auto h-full">
      {/* Loading Dialog */}
      <LoadingDialog
        open={isUploading}
        message={processingStep || 'Processing your complaint...'}
        progress={uploadProgress}
      />

      {/* Template Prompt Dialog - Vertical layout */}
      <Dialog
        open={showTemplatePrompt}
        onClose={() => setShowTemplatePrompt(false)}
        maxWidth="sm"
      >
        <Dialog.Title onClose={() => setShowTemplatePrompt(false)}>
          Template Required
        </Dialog.Title>
        <Dialog.Content>
          <div className="text-center space-y-4">
            <div className="mx-auto w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
              <FiAlertTriangle className="w-6 h-6 text-amber-600 dark:text-amber-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Notice Template Missing
              </h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  You don't have a notice template uploaded yet.
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  You can still process complaints, but you'll need a template for notices.
                </p>
              </div>
            </div>
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <div className="w-full space-y-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() => navigate('/profile')}
              className="w-full"
            >
              Upload Template
            </Button>
            <Button
              variant="outlined"
              size="sm"
              onClick={() => setShowTemplatePrompt(false)}
              className="w-full"
            >
              Continue Anyway
            </Button>
          </div>
        </Dialog.Actions>
      </Dialog>

      {/* Transaction Count Dialog - Vertical layout with better input */}
      <Dialog
        open={showTransactionDialog}
        onClose={() => setShowTransactionDialog(false)}
        maxWidth="sm"
      >
        <Dialog.Title onClose={() => setShowTransactionDialog(false)}>
          High Transaction Count
        </Dialog.Title>
        <Dialog.Content>
          <div className="space-y-4">
            {/* Icon and main message */}
            <div className="text-center space-y-3">
              <div className="mx-auto w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                <FiAlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {transactionWarning?.count} Transactions Found
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  This complaint contains a large number of transactions. You can filter out low-value transactions to improve processing speed.
                </p>
              </div>
            </div>

            {/* Threshold input section */}
            <div className="space-y-3">
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Filter transactions below amount:
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">₹</span>
                  <input
                    type="number"
                    value={customThreshold}
                    onChange={(e) => {
                      const value = e.target.value;
                      setCustomThreshold(value === '' ? 0 : Number(value));
                    }}
                    className="w-full pl-8 pr-3 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter amount (e.g., 1000)"
                    min="0"
                    step="100"
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Transactions below ₹{customThreshold} will be excluded from processing
                </p>
              </div>

              {/* Quick amount buttons */}
              <div className="grid grid-cols-4 gap-2">
                {[500, 1000, 2000, 5000].map((amount) => (
                  <button
                    key={amount}
                    onClick={() => setCustomThreshold(amount)}
                    className={`px-3 py-2 text-xs rounded-md border transition-colors ${
                      customThreshold === amount
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    ₹{amount}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <div className="w-full space-y-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() => handleTransactionFiltering(true)}
              className="w-full"
            >
              Filter & Continue (₹{customThreshold}+)
            </Button>
            <Button
              variant="outlined"
              size="sm"
              onClick={() => handleTransactionFiltering(false)}
              className="w-full"
            >
              Process All {transactionWarning?.count} Transactions
            </Button>
          </div>
        </Dialog.Actions>
      </Dialog>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
          {isDark ? (
            <span className="cyber-text-gradient">Upload Complaint</span>
          ) : (
            'Upload Complaint'
          )}
        </h1>
        <Button
          variant="outlined"
          size="sm"
          onClick={() => navigate('/dashboard')}
          startIcon={<FiArrowLeft />}
        >
          Back to Dashboard
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-12">
          <Card className="h-full flex flex-col dark:shadow-glow-blue-sm">
            <Card.Header className="py-3 border-b border-gray-200/50 dark:border-[rgba(0,170,255,0.2)]">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100/80 dark:bg-[rgba(0,170,255,0.15)] rounded-full text-primary-600 dark:text-primary-400 mr-3 dark:shadow-glow-blue-sm">
                  <FiUpload className="w-4 h-4" />
                </div>
                <h2 className="text-lg font-bold dark:cyber-text-gradient">Complaint Details</h2>
              </div>
            </Card.Header>

            <Card.Content className="flex-1 p-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                {/* Fraud Type Selection */}
                <div className="col-span-2">
                  <FraudTypeSelector
                    selectedType={fraudType}
                    onTypeSelect={(type, extractionType) => {
                      setFraudType(type);
                      setExtractionType(extractionType);
                    }}
                  />
                </div>

                {/* Layer Depth Selection */}
                <div className="col-span-1">
                  <div className="p-4 rounded-lg border h-full" style={{
                    backgroundColor: 'var(--theme-bg-primary)',
                    borderColor: 'var(--theme-border)',
                    color: 'var(--theme-text)'
                  }}>
                    <h3 className="text-sm font-medium mb-3" style={{ color: 'var(--theme-text)' }}>
                      Money Flow Depth
                    </h3>

                    <div className="flex items-center mb-3">
                      <input
                        type="range"
                        min={2}
                        max={7}
                        step={1}
                        value={layerDepth}
                        onChange={(e) => setLayerDepth(parseInt(e.target.value))}
                        className="w-full h-2 rounded-lg appearance-none cursor-pointer"
                        style={{
                          backgroundColor: 'var(--theme-glow)',
                          accentColor: 'var(--theme-accent)'
                        }}
                      />
                    </div>

                    <div className="flex justify-between mb-3">
                      {[2, 3, 4, 5, 6, 7].map((value) => (
                        <button
                          key={value}
                          onClick={() => setLayerDepth(value)}
                          className={`flex flex-col items-center justify-center w-8 h-8 rounded-full text-xs`}
                          style={{
                            backgroundColor: layerDepth === value ? 'var(--theme-accent)' : 'var(--theme-bg-card)',
                            color: layerDepth === value ? 'white' : 'var(--theme-text)',
                            borderColor: 'var(--theme-border)',
                            border: '1px solid var(--theme-border)',
                            boxShadow: layerDepth === value ? '0 0 5px var(--theme-glow)' : 'none'
                          }}
                        >
                          {value}
                        </button>
                      ))}
                    </div>

                    <div className="flex justify-between items-center p-2 rounded-lg text-xs" style={{
                      backgroundColor: 'var(--theme-bg-card)',
                      borderColor: 'var(--theme-border)',
                      border: '1px solid var(--theme-border)'
                    }}>
                      <div className="flex items-center">
                        <div className="w-5 h-5 rounded-full flex items-center justify-center mr-2 text-xs" style={{
                          backgroundColor: 'var(--theme-glow)',
                          color: 'var(--theme-text)'
                        }}>
                          {layerDepth}
                        </div>
                        <p className="text-xs" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                          {layerDepth <= 3 ? 'Faster processing' : layerDepth >= 6 ? 'Deeper tracing' : 'Balanced depth'}
                        </p>
                      </div>
                      <div className="px-2 py-0.5 rounded-md" style={{
                        backgroundColor: 'var(--theme-glow)',
                        borderColor: 'var(--theme-border)',
                        border: '1px solid var(--theme-border)'
                      }}>
                        <p className="text-xs font-medium" style={{ color: 'var(--theme-text)' }}>
                          {layerDepth <= 3 ? 'Fast ⚡' :
                           layerDepth <= 5 ? 'Medium' :
                           'Deep'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* File Upload - Larger and more prominent */}
              <div className="mb-6 p-4 rounded-lg border" style={{
                backgroundColor: 'var(--theme-bg-primary)',
                borderColor: 'var(--theme-border)',
                color: 'var(--theme-text)'
              }}>
                <h3 className="text-base font-medium mb-3" style={{ color: 'var(--theme-text)' }}>
                  Upload Complaint File
                </h3>

                <div
                  className={`border-2 border-dashed rounded-lg p-6 transition-all duration-300 ${
                    isDragging
                      ? 'border-primary-500 dark:border-primary-400 bg-primary-50/50 dark:bg-[rgba(0,170,255,0.15)]'
                      : file
                        ? 'border-success-500 dark:border-success-400 bg-success-50/50 dark:bg-[rgba(0,255,149,0.08)]'
                        : 'border-gray-300/70 dark:border-[rgba(0,170,255,0.3)] hover:border-primary-400 dark:hover:border-primary-500 hover:bg-gray-50/50 dark:hover:bg-[rgba(0,170,255,0.1)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)]'
                  } hover:-translate-y-1 transform`}
                  onDragOver={handleDragOver}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    accept=".html,.htm"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  {!file ? (
                    <div className="flex flex-col md:flex-row items-center justify-between p-4">
                      <div className="flex items-center mb-4 md:mb-0">
                        <div className="p-4 bg-primary-100/80 dark:bg-[rgba(0,170,255,0.15)] rounded-full inline-flex items-center justify-center text-primary-600 dark:text-primary-400 mr-4 dark:shadow-glow-blue-sm">
                          <FiUpload className="w-6 h-6" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium" style={{ color: 'var(--theme-text)' }}>
                            Upload HTML file
                          </h3>
                          <p className="text-sm mt-1" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                            Drag and drop or click to browse
                          </p>
                        </div>
                      </div>

                      <Button
                        variant="primary"
                        size="md"
                        onClick={handleBrowseClick}
                        className="ml-4 px-6"
                        style={{ color: 'white' }}
                      >
                        Browse Files
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col md:flex-row items-center justify-between p-4">
                      <div className="flex items-center min-w-0 mb-4 md:mb-0">
                        <div className="p-4 bg-success-100/80 dark:bg-[rgba(0,255,149,0.15)] rounded-full inline-flex items-center justify-center text-success-600 dark:text-success-400 mr-4 flex-shrink-0 dark:shadow-glow-green-sm">
                          <FiFile className="w-6 h-6" />
                        </div>
                        <div className="min-w-0">
                          <h3 className="text-lg font-medium truncate" style={{ color: 'var(--theme-text)' }}>
                            {file.name}
                          </h3>
                          <p className="text-sm mt-1" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                            {(file.size / 1024).toFixed(1)} KB
                          </p>
                        </div>
                      </div>

                      <Button
                        variant="danger"
                        size="md"
                        onClick={handleRemoveFile}
                        className="ml-4"
                        startIcon={<FiTrash2 />}
                      >
                        Remove
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Process Button - More prominent */}
              <div className="mb-4 p-4 rounded-lg border" style={{
                backgroundColor: 'var(--theme-bg-primary)',
                borderColor: 'var(--theme-border)',
                color: 'var(--theme-text)'
              }}>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>
                    Process Complaint
                  </h3>
                  <div className="px-3 py-1 rounded-md" style={{
                    backgroundColor: 'var(--theme-bg-card)',
                    borderColor: 'var(--theme-border)',
                    border: '1px solid var(--theme-border)'
                  }}>
                    <p className="text-sm font-medium" style={{ color: 'var(--theme-accent)' }}>
                      {!fraudType ? 'Step 1: Select type' : !file ? 'Step 2: Upload file' : 'Ready'}
                      {fraudType && ` - ${getFraudTypeName()}`}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Button
                    variant="primary"
                    size="md"
                    startIcon={isUploading ? undefined : <FiUpload />}
                    loading={isUploading}
                    onClick={handleUpload}
                    disabled={!file || isUploading || !fraudType}
                    className="flex-1 py-3"
                    style={{ color: 'white' }}
                  >
                    {isUploading ? 'Processing...' : 'Process Complaint'}
                  </Button>

                  <Button
                    variant="outlined"
                    size="md"
                    onClick={() => navigate('/dashboard')}
                  >
                    Cancel
                  </Button>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="mb-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg text-red-800 dark:text-red-300 flex items-start">
                  <FiAlertTriangle className="w-4 h-4 mr-2 flex-shrink-0 mt-0.5" />
                  <div className="flex flex-col">
                    {typeof error === 'string' && error.includes('\n') ?
                      error.split('\n').map((line, i) => <p key={i}>{line}</p>) :
                      <span>{typeof error === 'object' ? JSON.stringify(error) : error}</span>
                    }
                  </div>
                </div>
              )}

              {/* Upload Progress - Removed and replaced with LoadingDialog */}

              {/* Duplicate Warning */}
              {duplicateComplaint && (
                <div className="mb-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg">
                  <h3 className="text-sm font-medium text-amber-800 dark:text-amber-300 mb-2 flex items-center">
                    <FiAlertTriangle className="w-4 h-4 mr-2" />
                    Duplicate Complaint Detected
                  </h3>

                  <p className="text-xs text-amber-700 dark:text-amber-400 mb-2">
                    A complaint with the same number ({duplicateComplaint.complaint_number}) already exists.
                    Do you want to replace it with this new file?
                  </p>

                  <div className="flex space-x-2">
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleUpload}
                      style={{ color: 'white' }}
                    >
                      Replace
                    </Button>

                    <Button
                      variant="outlined"
                      size="sm"
                      onClick={() => {
                        setDuplicateComplaint(null);
                        setFile(null);
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </Card.Content>
          </Card>
        </div>


      </div>


    </div>
  );
};

export default TailwindSimpleFileUpload;
