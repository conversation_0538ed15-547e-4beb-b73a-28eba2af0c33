.complaint-details-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 0;
  max-width: 100%;
  overflow-x: hidden;
}

.complaint-details-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-width: 0;
  max-width: 100%;
}

.complaint-details-table {
  flex-grow: 1;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* Ensure the sidebar toggle button stays in the correct position */
.complaint-detail-layout :global(.sidebar-toggle-btn) {
  position: fixed !important;
  z-index: 30;
  transition: all 0.3s ease-in-out;
}

/* Fix spacing between dashboard and branding */
.complaint-detail-layout :global(.flex.items-center) {
  gap: 0.5rem;
  transition: all 0.3s ease-in-out;
}

/* Ensure proper spacing in the header */
.complaint-detail-layout :global(.flex.items-center.justify-start) {
  padding: 0.75rem;
  gap: 0.5rem;
  transition: all 0.3s ease-in-out;
}

/* Ensure the layout persists during navigation */
.complaint-detail-layout {
  transition: all 0.3s ease-in-out;
}

/* Fix the main content area */
:global(.flex.flex-col.flex-1) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Ensure the sidebar stays in place */
:global(aside) {
  z-index: 40;
}

/* Fix the header spacing */
:global(.flex.flex-col.md\\:flex-row) {
  margin: 0;
  padding: 1rem;
} 
