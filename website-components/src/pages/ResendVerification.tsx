import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import authService from '../services/authService';
import OTPVerification from '../components/OTPVerification';
import { FiMail } from 'react-icons/fi';

const ResendVerification: React.FC = () => {
  const [email, setEmail] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [error, setError] = useState('');
  const { showSuccess, showError } = useAlert();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSubmitting(true);

    try {
      await authService.api.post('/auth/resend-otp', null, {
        params: { email },
        timeout: 30000 // 30 seconds timeout for email sending
      });
      setOtpSent(true);
      showSuccess('Verification code sent! Please check your inbox.');
    } catch (error: any) {
      if (error.response) {
        setError(error.response.data.detail || 'Failed to send verification code');
      } else if (error.request) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError('An unexpected error occurred');
      }
      showError('Failed to send verification code');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 px-4 py-8 sm:py-12 animate-gradient-x overflow-hidden">
      <div className="w-full max-w-md mx-auto">
        {otpSent ? (
          <OTPVerification
            email={email}
            onVerified={async (otp: string) => {
              try {
                const response = await authService.verifyOTP(email, otp);
                if (response.success) {
                  showSuccess('Email verified successfully! Please login with your credentials.');
                  window.location.href = '/login';
                }
              } catch (error: any) {
                showError('Invalid verification code. Please try again.');
              }
            }}
            onCancel={() => setOtpSent(false)}
          />
        ) : (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6 text-center text-gray-800 dark:text-white">
              Email Verification
            </h2>

            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                {error}
              </div>
            )}

            <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
              Enter your email address to receive a verification code
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                    <span className="flex items-center justify-center w-5 h-5">
                      <FiMail className="h-4 w-4 text-gray-400" />
                    </span>
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="block w-full pl-8 pr-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-2"
                    style={{ textIndent: '12px' }}
                    required
                  />
                </div>
              </div>

              <div className="flex flex-col space-y-2">
                <button
                  type="submit"
                  disabled={submitting}
                  className="w-full flex justify-center items-center py-2.5 px-4 rounded-md shadow-md text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-500/20 disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {submitting ? (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : 'Send Verification Code'}
                </button>

                <div className="text-center mt-4">
                  <Link to="/login" className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                    Back to Login
                  </Link>
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResendVerification;
