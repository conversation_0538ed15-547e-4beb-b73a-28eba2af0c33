import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import { useComplaints } from '../hooks/useComplaintData';
import complaintService from '../services/complaintService';

// Tailwind UI Components
import DashboardTable from '../components/DashboardTable';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import TextField from '../components/ui/TextField';
import { ConfirmDialog } from '../components/ui/Dialog';
import Modal from '../components/ui/Modal';


// Icons
import {
  FiSearch,
  FiFileText,
  FiCheckCircle,
  FiClock,
} from 'react-icons/fi';

// Types
interface Complaint {
  _id?: string;
  complaint_number: string;
  complainant_name: string;
  category: string;
  amount: string | number;
  status: string;
  metadata?: {
    total_amount?: string | number;
    [key: string]: any;
  };
}

const TailwindDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useAlert();

  // Search state
  const [searchTerm, setSearchTerm] = useState('');

  // Delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [complaintToDelete, setComplaintToDelete] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [complaintToEdit, setComplaintToEdit] = useState<Complaint | null>(null);
  const [editFormData, setEditFormData] = useState({
    complainant_name: '',
    category: '',
    amount: '',
    status: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination state
  const [page, setPage] = useState(1);
  const limit = 10; // Fixed limit size

  // Use our custom hook to fetch complaints with pagination
  const {
    data: complaints,
    loading,
    pagination,
    changePage,
    fetchComplaints
  } = useComplaints(page, limit);

  // Filtered complaints based on search
  const [filteredComplaints, setFilteredComplaints] = useState<Complaint[]>([]);

  // Stats
  const [stats, setStats] = useState({
    totalComplaints: 0,
    pendingComplaints: 0,
    resolvedComplaints: 0,
    totalAmount: '0.00'
  });

  // Update filtered complaints when search term or complaints change
  useEffect(() => {
    if (!complaints) return;

    // Check if complaints is an array or an object with data property
    const complaintsArray = Array.isArray(complaints)
      ? complaints
      : (complaints.data && Array.isArray(complaints.data) ? complaints.data : []);

    // Use a reference to avoid infinite loops
    const currentComplaints = [...complaintsArray];

    if (searchTerm.trim() === '') {
      setFilteredComplaints(currentComplaints);
    } else {
      const lowercasedSearch = searchTerm.toLowerCase();
      const filtered = currentComplaints.filter(
        (complaint: Complaint) =>
          (complaint.complainant_name?.toLowerCase() || '').includes(lowercasedSearch) ||
          (complaint.category?.toLowerCase() || '').includes(lowercasedSearch) ||
          (complaint.complaint_number?.toLowerCase() || '').includes(lowercasedSearch)
      );
      setFilteredComplaints(filtered);
    }
  }, [searchTerm, complaints]);

  // Update stats when complaints change
  useEffect(() => {
    if (!complaints) return;

    // Check if complaints is an array or an object with data property
    const complaintsArray = Array.isArray(complaints)
      ? complaints
      : (complaints.data && Array.isArray(complaints.data) ? complaints.data : []);

    // Use a reference to avoid infinite loops
    const currentComplaints = [...complaintsArray];

    const total = currentComplaints.length;
    const pending = currentComplaints.filter((c: Complaint) =>
      c.status?.toLowerCase() === 'pending').length;
    const resolved = currentComplaints.filter((c: Complaint) =>
      c.status?.toLowerCase() === 'resolved').length;

    // Calculate total amount from metadata.total_amount if available, otherwise use amount
    const amount = currentComplaints.reduce((sum: number, c: any) => {
      let amountValue = 0;

      // First try to get total_amount from metadata
      if (c.metadata && c.metadata.total_amount !== undefined && c.metadata.total_amount !== null) {
        if (typeof c.metadata.total_amount === 'number') {
          amountValue = c.metadata.total_amount;
        } else if (typeof c.metadata.total_amount === 'string') {
          // Remove any non-numeric characters except decimal point
          const amountStr = c.metadata.total_amount.replace(/[^0-9.]/g, '');
          amountValue = parseFloat(amountStr) || 0;
        }
      }
      // Fall back to amount field if metadata.total_amount is not available or is zero
      else if (c.amount !== undefined && c.amount !== null && (!amountValue || amountValue === 0)) {
        if (typeof c.amount === 'number') {
          amountValue = c.amount;
        } else if (typeof c.amount === 'string') {
          // Remove any non-numeric characters except decimal point
          const amountStr = c.amount.replace(/[^0-9.]/g, '');
          amountValue = parseFloat(amountStr) || 0;
        }
      }

      return sum + amountValue;
    }, 0);

    // Only update stats if they've actually changed
    setStats(prevStats => {
      // Check if the stats have actually changed
      if (
        prevStats.totalComplaints !== total ||
        prevStats.pendingComplaints !== pending ||
        prevStats.resolvedComplaints !== resolved ||
        prevStats.totalAmount !== amount.toFixed(2)
      ) {
        return {
          totalComplaints: total,
          pendingComplaints: pending,
          resolvedComplaints: resolved,
          totalAmount: amount.toFixed(2)
        };
      }

      // If stats haven't changed, return the previous state to avoid re-renders
      return prevStats;
    });
  }, [complaints]);

  // Handle view complaint
  const handleViewComplaint = (complaintNumber: string) => {
    navigate(`/complaint/${complaintNumber}`);
  };

  // Handle view complaint summary
  const handleViewSummary = (complaintNumber: string) => {
    navigate(`/complaint-summary/${complaintNumber}`);
  };

  // Handle edit complaint
  const handleEditComplaint = (complaint: Complaint) => {
    setComplaintToEdit(complaint);
    setEditFormData({
      complainant_name: complaint.complainant_name || '',
      category: complaint.category || '',
      amount: complaint.metadata?.total_amount?.toString() || complaint.amount?.toString() || '',
      status: complaint.status || ''
    });
    setEditDialogOpen(true);
  };

  // Handle edit dialog close
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setComplaintToEdit(null);
  };

  // Handle edit form change
  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle edit form submit
  const handleEditFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!complaintToEdit) return;

    try {
      setIsSubmitting(true);

      // Format the amount properly
      let formattedAmount = editFormData.amount;
      if (formattedAmount) {
        // Remove any non-numeric characters except decimal point
        formattedAmount = formattedAmount.replace(/[^0-9.]/g, '');
      }

      const updateData = {
        complainant_name: editFormData.complainant_name,
        category: editFormData.category,
        amount: formattedAmount ? parseFloat(formattedAmount) : 0,
        status: editFormData.status
      };

      // Create an updated complaint object for optimistic UI update
      const updatedComplaint = {
        ...complaintToEdit,
        complainant_name: editFormData.complainant_name,
        category: editFormData.category,
        amount: formattedAmount ? parseFloat(formattedAmount) : 0,
        status: editFormData.status
      };

      // If the complaint has metadata, update the total_amount there too
      if (updatedComplaint.metadata) {
        updatedComplaint.metadata.total_amount = formattedAmount ? parseFloat(formattedAmount) : 0;
      }

      // Optimistically update the UI before the API call completes
      // This makes the UI feel more responsive
      setFilteredComplaints(prevComplaints => {
        return prevComplaints.map(complaint =>
          complaint.complaint_number === complaintToEdit.complaint_number
            ? updatedComplaint
            : complaint
        );
      });

      // Close the dialog immediately for better UX
      handleCloseEditDialog();

      // Show a temporary success message
      showSuccess('Updating complaint...');

      // Make the actual API call
      await complaintService.updateComplaint(complaintToEdit.complaint_number, updateData);

      // Show final success message
      showSuccess('Complaint updated successfully');

      // Force a refresh of the data to ensure consistency
      // This is necessary to get the latest data from the server
      await fetchComplaints();

      // We don't need to force a re-render anymore
      // The fetchComplaints call will update the complaints state
      // which will trigger the useEffect hooks to update filteredComplaints and stats
    } catch (error) {
      showError('Failed to update complaint');

      // If there was an error, refresh to get the correct data
      fetchComplaints();
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (complaintNumber: string) => {
    setComplaintToDelete(complaintNumber);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setComplaintToDelete(null);
  };

  // Handle delete complaint with optimized performance
  const handleDeleteConfirm = async () => {
    if (!complaintToDelete) return;

    try {
      // Store the complaint number for reference
      const complaintNumberToDelete = complaintToDelete;

      // Close the dialog immediately for better UX
      setDeleteDialogOpen(false);
      setComplaintToDelete(null);

      // Show a temporary success message
      showSuccess('Deleting complaint...');

      // Optimistically update the UI before the API call completes
      // This makes the UI feel more responsive
      setFilteredComplaints(prevComplaints =>
        prevComplaints.filter(complaint => complaint.complaint_number !== complaintNumberToDelete)
      );

      // Make the actual API call in the background
      const deletePromise = complaintService.deleteComplaint(complaintNumberToDelete);

      // Show success message immediately for better UX
      showSuccess('Complaint deleted successfully');

      // Wait for the API call to complete
      await deletePromise;

      // Refresh data in the background without blocking the UI
      // This ensures the data is consistent with the server
      setTimeout(async () => {
        try {
          await fetchComplaints();
        } catch (refreshError) {
          // Only show error if it's a critical failure
          if (refreshError instanceof Error && refreshError.message.includes('critical')) {
            showError('There was an error refreshing the data. Please refresh the page.');
          }
        }
      }, 100);
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to delete complaint';
      showError(errorMessage);

      // If there was an error, refresh to get the correct data
      await fetchComplaints();

      // Close the dialog if it's still open
      setDeleteDialogOpen(false);
      setComplaintToDelete(null);
    }
  };

  // Format currency
  const formatCurrency = (amount: number | string | undefined | null) => {
    // Handle undefined or null
    if (amount === undefined || amount === null) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(0);
    }

    // Convert to number if it's a string
    let numAmount = 0;

    if (typeof amount === 'number') {
      numAmount = amount;
    } else if (typeof amount === 'string') {
      // Remove any non-numeric characters except decimal point
      const amountStr = amount.replace(/[^0-9.]/g, '');
      numAmount = parseFloat(amountStr) || 0;
    }

    // Format with Indian Rupee symbol
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numAmount);
  };





  return (
    <div className="p-4 sm:p-6 max-w-7xl mx-auto">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
        {/* Total Complaints */}
        <Card className="text-white transform transition-all duration-300 hover:-translate-y-1" style={{
          backgroundColor: 'var(--theme-storm-bg-primary)',
          boxShadow: '0 0 10px var(--theme-storm-glow)'
        }}>
          <Card.Content className="flex items-center justify-between p-5">
            <div>
              <p className="text-sm font-medium opacity-90">Total Complaints</p>
              <h3 className="text-3xl font-bold mt-1">{stats.totalComplaints}</h3>
            </div>
            <div className="p-3 rounded-full" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              boxShadow: '0 0 5px var(--theme-storm-glow)'
            }}>
              <FiFileText className="w-5 h-5" />
            </div>
          </Card.Content>
        </Card>

        {/* Pending Complaints */}
        <Card className="text-white transform transition-all duration-300 hover:-translate-y-1" style={{
          backgroundColor: 'var(--theme-clay-bg-primary)',
          boxShadow: '0 0 10px var(--theme-clay-glow)'
        }}>
          <Card.Content className="flex items-center justify-between p-5">
            <div>
              <p className="text-sm font-medium opacity-90">Pending</p>
              <h3 className="text-3xl font-bold mt-1">{stats.pendingComplaints}</h3>
            </div>
            <div className="p-3 rounded-full" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              boxShadow: '0 0 5px var(--theme-clay-glow)'
            }}>
              <FiClock className="w-5 h-5" />
            </div>
          </Card.Content>
        </Card>

        {/* Resolved Complaints */}
        <Card className="text-white transform transition-all duration-300 hover:-translate-y-1" style={{
          backgroundColor: 'var(--theme-forest-bg-primary)',
          boxShadow: '0 0 10px var(--theme-forest-glow)'
        }}>
          <Card.Content className="flex items-center justify-between p-5">
            <div>
              <p className="text-sm font-medium opacity-90">Resolved</p>
              <h3 className="text-3xl font-bold mt-1">{stats.resolvedComplaints}</h3>
            </div>
            <div className="p-3 rounded-full" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              boxShadow: '0 0 5px var(--theme-forest-glow)'
            }}>
              <FiCheckCircle className="w-5 h-5" />
            </div>
          </Card.Content>
        </Card>

        {/* Total Amount */}
        <Card className="text-white transform transition-all duration-300 hover:-translate-y-1" style={{
          backgroundColor: 'var(--theme-fog-bg-primary)',
          boxShadow: '0 0 10px var(--theme-fog-glow)'
        }}>
          <Card.Content className="flex items-center justify-between p-5">
            <div>
              <p className="text-sm font-medium opacity-90">Total Amount</p>
              <h3 className="text-2xl font-bold mt-1">{formatCurrency(parseFloat(stats.totalAmount))}</h3>
            </div>
            <div className="p-3 rounded-full" style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              boxShadow: '0 0 5px var(--theme-fog-glow)'
            }}>
              <span className="font-bold text-lg">₹</span>
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Complaints Table */}
      <Card
        className="overflow-hidden"
        style={{
          boxShadow: '0 0 10px var(--theme-glow)'
        }}
      >
        <Card.Header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-xl font-bold" style={{ color: 'var(--theme-text)' }}>Complaints</h2>

          {/* Search Field */}
          <div className="w-full sm:w-auto">
            <TextField
              placeholder="Search complaints..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startAdornment={<FiSearch className="w-3.5 h-3.5" style={{ color: 'var(--theme-accent)' }} />}
              className="w-full sm:w-56"
              style={{ boxShadow: '0 0 5px var(--theme-glow)' }}
            />
          </div>
        </Card.Header>

        <div className="overflow-x-auto" style={{ maxHeight: "550px", overflowY: "auto" }}>
          <DashboardTable
            complaints={filteredComplaints}
            loading={loading}
            onView={handleViewComplaint}
            onSummary={handleViewSummary}
            onEdit={handleEditComplaint}
            onDelete={handleDeleteClick}
            pagination={{
              page: pagination.page,
              pages: pagination.totalPages,
              total: pagination.total,
              limit: pagination.limit
            }}
            onPageChange={(newPage) => {
              setPage(newPage);
              changePage(newPage);
            }}
          />
        </div>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleDeleteConfirm}
        title="Delete Complaint"
        content="Are you sure you want to delete this complaint? This action cannot be undone."
        confirmText="Delete"
        confirmVariant="danger"
        maxWidth="sm"
      />

      {/* Edit Complaint Dialog */}
      <Modal
        isOpen={editDialogOpen}
        onClose={handleCloseEditDialog}
        title="Edit Complaint"
      >
        <form onSubmit={handleEditFormSubmit} className="space-y-4">
          <div>
            <label htmlFor="complainant_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Complainant Name
            </label>
            <TextField
              id="complainant_name"
              name="complainant_name"
              value={editFormData.complainant_name}
              onChange={handleEditFormChange}
              fullWidth
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Category
            </label>
            <TextField
              id="category"
              name="category"
              value={editFormData.category}
              onChange={handleEditFormChange}
              fullWidth
            />
          </div>

          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Amount
            </label>
            <TextField
              id="amount"
              name="amount"
              value={editFormData.amount}
              onChange={handleEditFormChange}
              fullWidth
            />
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              id="status"
              name="status"
              value={editFormData.status}
              onChange={handleEditFormChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
            >
              <option value="Pending">Pending</option>
              <option value="In Progress">In Progress</option>
              <option value="Resolved">Resolved</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button
              variant="outlined"
              onClick={handleCloseEditDialog}
              type="button"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default TailwindDashboard;
