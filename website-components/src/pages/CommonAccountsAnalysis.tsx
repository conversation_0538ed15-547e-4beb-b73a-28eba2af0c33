import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import complaintService from '../services/complaintService';
import { withLayout } from '../context/LayoutContext';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import LoadingDialog from '../components/ui/LoadingDialog';
import { FiArrowLeft, FiExternalLink, FiFileText } from 'react-icons/fi';

interface CommonAccount {
  account: string;
  account_type: string;
  bank: string;
  total_occurrences: number;
  occurrences: Array<{
    complaint_id: string;
    complaint_number: string;
    transaction_ids: string[];
    amount: number;
  }>;
}

interface ComplaintDetail {
  complaint_number: string;
  complainant_name: string;
  date: string;
  amount: number;
}

interface AnalysisResult {
  common_accounts: CommonAccount[];
  complaint_details: Record<string, ComplaintDetail>;
  total_complaints_analyzed: number;
  total_common_accounts: number;
}

const CommonAccountsAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showError } = useAlert();

  // Get complaint IDs from location state
  const complaintIds = location.state?.complaintIds || [];

  const [loading, setLoading] = useState<boolean>(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  useEffect(() => {
    // Redirect if no complaint IDs are provided
    if (!complaintIds.length) {
      showError('No complaints selected for analysis');
      navigate('/dashboard');
      return;
    }

    // Analyze complaints
    const analyzeComplaints = async () => {
      try {
        setLoading(true);
        const result = await complaintService.analyzeCommonAccounts(complaintIds);
        setAnalysisResult(result);
      } catch (error: any) {
        showError(error.message || 'Failed to analyze complaints');
      } finally {
        setLoading(false);
      }
    };

    analyzeComplaints();
  }, [complaintIds, navigate, showError]);

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  const handleViewComplaint = (complaintId: string) => {
    navigate(`/complaint/${complaintId}`);
  };

  const handleViewSummary = (complaintId: string) => {
    navigate(`/complaint-summary/${complaintId}`);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="text"
          onClick={handleBackToDashboard}
          className="mr-4"
          startIcon={<FiArrowLeft />}
        >
          Back to Dashboard
        </Button>
        <h1 className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
          Common Accounts Analysis
        </h1>
      </div>

      <LoadingDialog open={loading} message="Analyzing complaints..." />

      {!loading && analysisResult && (
        <>
          {/* Analysis Summary */}
          <Card className="mb-6">
            <Card.Header>
              <h2 className="text-xl font-bold" style={{ color: 'var(--theme-text)' }}>Analysis Summary</h2>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Complaints Analyzed</p>
                  <p className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
                    {analysisResult.total_complaints_analyzed}
                  </p>
                </div>
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Common Accounts Found</p>
                  <p className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
                    {analysisResult.total_common_accounts}
                  </p>
                </div>
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Total Connections</p>
                  <p className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
                    {analysisResult.common_accounts.reduce((sum, account) => sum + account.occurrences.length, 0)}
                  </p>
                </div>
              </div>
            </Card.Content>
          </Card>

          {/* Complaints List */}
          <Card className="mb-6">
            <Card.Header>
              <h2 className="text-xl font-bold" style={{ color: 'var(--theme-text)' }}>Analyzed Complaints</h2>
            </Card.Header>
            <Card.Content>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead>
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Complaint Number
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Complainant
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {Object.entries(analysisResult.complaint_details).map(([complaintId, details]) => (
                      <tr key={complaintId} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-3 text-sm" style={{ color: 'var(--theme-text)' }}>
                          {details.complaint_number}
                        </td>
                        <td className="px-4 py-3 text-sm" style={{ color: 'var(--theme-text)' }}>
                          {details.complainant_name}
                        </td>
                        <td className="px-4 py-3 text-sm" style={{ color: 'var(--theme-text)' }}>
                          {details.date}
                        </td>
                        <td className="px-4 py-3 text-sm" style={{ color: 'var(--theme-text)' }}>
                          ₹{typeof details.amount === 'number' ? details.amount.toLocaleString('en-IN') : details.amount}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <div className="flex space-x-2">
                            <Button
                              variant="text"
                              size="sm"
                              onClick={() => handleViewComplaint(complaintId)}
                              className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
                              aria-label="View complaint"
                            >
                              <FiExternalLink className="w-3.5 h-3.5" />
                            </Button>
                            <Button
                              variant="text"
                              size="sm"
                              onClick={() => handleViewSummary(complaintId)}
                              className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
                              aria-label="View complaint summary"
                            >
                              <FiFileText className="w-3.5 h-3.5" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card.Content>
          </Card>

          {/* Common Accounts */}
          <Card>
            <Card.Header>
              <h2 className="text-xl font-bold" style={{ color: 'var(--theme-text)' }}>Common Accounts</h2>
            </Card.Header>
            <Card.Content>
              {analysisResult.common_accounts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">No common accounts found across the selected complaints.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {analysisResult.common_accounts.map((account, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                      <div className="bg-gray-50 dark:bg-gray-800 px-4 py-3 flex flex-wrap justify-between items-center">
                        <div>
                          <h3 className="text-lg font-semibold" style={{ color: 'var(--theme-text)' }}>
                            {account.account} ({account.account_type === 'sender' ? 'Sender' : 'Receiver'})
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {account.bank || 'Unknown Bank'} • Found in {account.total_occurrences} complaints
                          </p>
                        </div>
                        <div className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">
                          {account.account_type === 'sender' ? 'Sender Account' : 'Receiver Account'}
                        </div>
                      </div>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                          <thead>
                            <tr className="bg-gray-50 dark:bg-gray-800/50">
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Complaint
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Transaction IDs
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Amount
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {account.occurrences.map((occurrence, occIndex) => (
                              <tr key={occIndex} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="px-4 py-2 text-sm" style={{ color: 'var(--theme-text)' }}>
                                  {occurrence.complaint_number}
                                </td>
                                <td className="px-4 py-2 text-sm" style={{ color: 'var(--theme-text)' }}>
                                  {occurrence.transaction_ids.length > 0
                                    ? occurrence.transaction_ids.join(', ')
                                    : 'No transaction IDs'}
                                </td>
                                <td className="px-4 py-2 text-sm" style={{ color: 'var(--theme-text)' }}>
                                  ₹{typeof occurrence.amount === 'number' ? occurrence.amount.toLocaleString('en-IN') : occurrence.amount}
                                </td>
                                <td className="px-4 py-2 text-sm">
                                  <div className="flex space-x-2">
                                    <Button
                                      variant="text"
                                      size="sm"
                                      onClick={() => handleViewComplaint(occurrence.complaint_id)}
                                      className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
                                      aria-label="View complaint"
                                    >
                                      <FiExternalLink className="w-3.5 h-3.5" />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card.Content>
          </Card>
        </>
      )}
    </div>
  );
};

export default withLayout(CommonAccountsAnalysis, {
  showSidebar: true,
  showHeader: true,
  showFooter: false,
  containerClass: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'
});
