import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import authService from '../services/authService';
import Button from '../components/ui/Button';
import { FiLock, FiMail, FiAlertTriangle, FiCheckCircle, FiArrowLeft } from 'react-icons/fi';

const TailwindPasswordReset: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const isResetMode = !!token;

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [validToken, setValidToken] = useState(true);
  const [tokenChecking, setTokenChecking] = useState(isResetMode);
  const [submitted, setSubmitted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const navigate = useNavigate();
  const { showSuccess, showError } = useAlert();

  // Verify token validity when in reset mode
  useEffect(() => {
    if (isResetMode) {
      const verifyToken = async () => {
        try {
          await authService.api.get(`/auth/verify-reset-token/${token}`);
          setValidToken(true);
        } catch (error) {
          setValidToken(false);
        } finally {
          setTokenChecking(false);
        }
      };

      verifyToken();
    }
  }, [token, isResetMode]);

  const handleForgotPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      showError('Please enter your email address');
      return;
    }

    setLoading(true);
    try {
      await authService.api.post('/auth/forgot-password', { email }, {
        timeout: 30000 // 30 seconds timeout for email sending
      });
      setSubmitted(true);
      showSuccess('Password reset instructions sent to your email');
    } catch (error) {
      showError('Failed to process your request. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords
    if (password.length < 8) {
      showError('Password must be at least 8 characters long');
      return;
    }

    if (password !== confirmPassword) {
      showError('Passwords do not match');
      return;
    }

    setLoading(true);
    try {
      await authService.api.post('/auth/reset-password', {
        token,
        password
      });

      setSubmitted(true);
      showSuccess('Your password has been reset successfully');
    } catch (error) {
      showError('Failed to reset password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  if (tokenChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 animate-fade-in">
        {isResetMode ? (
          // Reset Password Mode
          !validToken ? (
            <div className="text-center space-y-6">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full inline-flex items-center justify-center mx-auto mb-4">
                <FiAlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>

              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Invalid or Expired Link
              </h2>

              <p className="text-gray-600 dark:text-gray-400 text-sm">
                The password reset link is invalid or has expired. Please request a new password reset link.
              </p>

              <div className="pt-4">
                <Button
                  variant="primary"
                  onClick={() => navigate('/forgot-password')}
                  fullWidth
                >
                  Request New Link
                </Button>
              </div>
            </div>
          ) : submitted ? (
            <div className="text-center space-y-6">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full inline-flex items-center justify-center mx-auto mb-4">
                <FiCheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>

              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Password Reset Complete
              </h2>

              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Your password has been reset successfully. You can now log in with your new password.
              </p>

              <div className="pt-4">
                <Button
                  variant="primary"
                  onClick={() => navigate('/login')}
                  fullWidth
                >
                  Go to Login
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
                  Reset Password
                </h1>
              </div>

              <form onSubmit={handleResetPasswordSubmit} className="space-y-6">
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Please enter your new password below.
                </p>

                {/* New Password */}
                <div className="space-y-1">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    New Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                      <span className="flex items-center justify-center w-5 h-5">
                        <FiLock className="h-4 w-4 text-gray-400" />
                      </span>
                    </div>
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter new password"
                      required
                      className="block w-full pl-8 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      style={{ textIndent: '12px' }}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="text-gray-400 hover:text-gray-500 focus:outline-none bg-transparent p-1 rounded-full hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors duration-200"
                      >
                        {showPassword ? (
                          <svg className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                          </svg>
                        ) : (
                          <svg className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Password must be at least 8 characters long
                  </p>
                </div>

                {/* Confirm Password */}
                <div className="space-y-1">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                      <span className="flex items-center justify-center w-5 h-5">
                        <FiLock className="h-4 w-4 text-gray-400" />
                      </span>
                    </div>
                    <input
                      id="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Confirm new password"
                      required
                      className="block w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      style={{ textIndent: '12px' }}
                    />
                  </div>
                </div>

                <div>
                  <Button
                    type="submit"
                    variant="primary"
                    fullWidth
                    loading={loading}
                  >
                    Reset Password
                  </Button>
                </div>

                <div className="text-center mt-4">
                  <Link
                    to="/login"
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <FiArrowLeft className="mr-1 h-4 w-4" />
                    Back to Login
                  </Link>
                </div>
              </form>
            </>
          )
        ) : (
          // Forgot Password Mode
          !submitted ? (
            <>
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
                  Forgot Password
                </h1>
              </div>

              <form onSubmit={handleForgotPasswordSubmit} className="space-y-6">
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Enter your email address and we'll send you instructions to reset your password.
                </p>

                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                    <span className="flex items-center justify-center w-5 h-5">
                      <FiMail className="h-4 w-4 text-gray-400" />
                    </span>
                  </div>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Email address"
                    required
                    className="block w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    style={{ textIndent: '12px' }}
                  />
                </div>

                <div>
                  <Button
                    type="submit"
                    variant="primary"
                    fullWidth
                    loading={loading}
                  >
                    Send Reset Instructions
                  </Button>
                </div>

                <div className="text-center mt-4">
                  <Link
                    to="/login"
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <FiArrowLeft className="mr-1 h-4 w-4" />
                    Back to Login
                  </Link>
                </div>
              </form>
            </>
          ) : (
            <div className="text-center space-y-6">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full inline-flex items-center justify-center mx-auto mb-4">
                <FiMail className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>

              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Check Your Email
              </h2>

              <p className="text-gray-600 dark:text-gray-400 text-sm">
                We've sent password reset instructions to <span className="font-medium">{email}</span>.
                Please check your email inbox and spam folder.
              </p>

              <div className="pt-4">
                <Button
                  variant="outlined"
                  onClick={() => navigate('/login')}
                  fullWidth
                >
                  Return to Login
                </Button>
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default TailwindPasswordReset;
