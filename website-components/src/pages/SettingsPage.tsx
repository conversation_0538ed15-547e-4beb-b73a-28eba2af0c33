import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../context/UserContext';
import { useAlert } from '../context/TailwindAlertContext';
import { useThemeContext } from '../context/TailwindThemeContext';

import authService from '../services/authService';
import { useLayout } from '../context/LayoutContext';

// UI Components
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { ConfirmDialog } from '../components/ui/Dialog';
import ThemeSettings from '../components/ThemeSettings';
import { Link } from 'react-router-dom';

// Icons
import {
  FiBell,
  FiShield,
  FiToggleLeft,
  FiToggleRight,
  FiTrash2,
  FiUser
} from 'react-icons/fi';

const SettingsPage: React.FC = () => {
  const { userInfo, fetchUserInfo } = useUser();
  const { showSuccess, showError, showInfo } = useAlert();
  const { isDark } = useThemeContext();
  const navigate = useNavigate();
  const { setLayoutConfig } = useLayout();

  // Settings state
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);

  // Dialog states
  const [deleteAccountDialogOpen, setDeleteAccountDialogOpen] = useState(false);

  // Set layout configuration
  useEffect(() => {
    setLayoutConfig({
      hideSidebar: false,
      fullWidth: false,
      customPadding: '1rem'
    });
  }, [setLayoutConfig]);

  // Fetch user data on mount and set initial two-factor auth state
  useEffect(() => {
    if (!userInfo) {
      fetchUserInfo();
    } else {
      // Set the two-factor auth state from user info
      setTwoFactorAuth(userInfo.two_step_enabled || false);
    }
  }, [userInfo, fetchUserInfo]);

  // Settings handlers
  const handleToggleEmailNotifications = () => {
    setEmailNotifications(!emailNotifications);
    showInfo(`Email notifications ${!emailNotifications ? 'enabled' : 'disabled'}`);
  };

  const handleToggleTwoFactorAuth = async () => {
    try {
      const newState = !twoFactorAuth;

      // Call the backend API to update two-step verification setting
      const response = await authService.api.post('/auth/two-step/settings', {
        enabled: newState
      });

      if (response.data) {
        setTwoFactorAuth(newState);
        showSuccess(`Two-factor authentication ${newState ? 'enabled' : 'disabled'}`);

        // Refresh user data to get updated settings
        await fetchUserInfo();
      }
    } catch (error: any) {
      showError('Failed to update two-factor authentication settings. Please try again.');
      // Two-factor auth toggle error handled
    }
  };

  // Delete account handler
  const handleDeleteAccount = async () => {
    try {
      // Use the correct endpoint for deleting the account
      await authService.api.delete('/auth/account');

      // Show success message and redirect to login
      showSuccess('Your account has been deleted successfully');

      // Logout the user
      await authService.logout();

      // Redirect to login page
      navigate('/login');
    } catch (error) {
      showError('Failed to delete your account. Please try again later.');
    } finally {
      setDeleteAccountDialogOpen(false);
    }
  };

  if (!userInfo) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`max-w-7xl mx-auto p-6 h-[calc(100vh-64px)] ${isDark ? 'cyber-grid-bg' : ''}`}>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
          <span className={isDark ? 'cyber-text-gradient' : ''}>Settings</span>
        </h1>
        <Link to="/profile">
          <Button
            variant="outlined"
            size="sm"
            startIcon={<FiUser />}
          >
            My Profile
          </Button>
        </Link>
      </div>

      {/* Settings Card */}
      <Card className="dark:shadow-glow-blue-sm">
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Application Settings */}
            <div className="border rounded-lg p-4 bg-white/90 dark:bg-[rgba(16,16,30,0.7)] shadow-sm dark:shadow-glow-blue-sm backdrop-blur-md">
              <h3 className="text-lg font-bold mb-4" style={{ color: 'var(--theme-text)' }}>
                <span className={isDark ? 'cyber-text-gradient' : ''}>Application Settings</span>
              </h3>

              <div className="space-y-4 divide-y divide-gray-200/50 dark:divide-[rgba(0,170,255,0.2)]">
                {/* Theme Settings */}
                <ThemeSettings className="pb-4" />

                {/* Email Notifications */}
                <div className="pt-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 rounded-full bg-blue-100/80 dark:bg-[rgba(0,170,255,0.15)] text-primary-600 dark:text-primary-400 mr-3 dark:shadow-glow-blue-sm">
                      <FiBell className="w-5 h-5" />
                    </div>
                    <div>
                      <h3 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>Email Notifications</h3>
                      <p className="text-sm" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                        Receive email notifications for important updates
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={handleToggleEmailNotifications}
                    className="text-gray-500 hover:text-gray-700 dark:text-primary-400 dark:hover:text-primary-300 bg-transparent"
                  >
                    {emailNotifications ? (
                      <FiToggleRight className="w-8 h-8 text-primary-500 dark:shadow-glow-blue-sm" />
                    ) : (
                      <FiToggleLeft className="w-8 h-8" />
                    )}
                  </button>
                </div>

                {/* Two-Factor Authentication */}
                <div className="pt-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 rounded-full bg-green-100/80 dark:bg-[rgba(0,255,136,0.15)] text-success-600 dark:text-success-400 mr-3 dark:shadow-glow-green-sm">
                      <FiShield className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>Two-Factor Authentication</h3>
                      <p className="text-sm" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                        Add an extra layer of security to your account
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={handleToggleTwoFactorAuth}
                    className="text-gray-500 hover:text-gray-700 dark:text-success-400 dark:hover:text-success-300 bg-transparent"
                  >
                    {twoFactorAuth ? (
                      <FiToggleRight className="w-8 h-8 text-success-500 dark:shadow-glow-green-sm" />
                    ) : (
                      <FiToggleLeft className="w-8 h-8" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Account Actions Section */}
            <div className="border rounded-lg p-4 bg-white/90 dark:bg-[rgba(16,16,30,0.7)] shadow-sm dark:shadow-glow-blue-sm backdrop-blur-md">
              <h3 className="text-lg font-bold mb-4" style={{ color: 'var(--theme-text)' }}>
                <span className={isDark ? 'cyber-text-gradient' : ''}>Account Actions</span>
              </h3>

              <div className="space-y-4">
                {/* Delete Account */}
                <div className="flex items-center justify-between p-3 bg-red-50/80 dark:bg-[rgba(255,0,102,0.08)] rounded-lg border border-red-200/50 dark:border-[rgba(255,0,102,0.3)] transition-all duration-200 hover:-translate-y-0.5 transform dark:shadow-glow-red-sm">
                  <div className="flex items-center">
                    <div className="p-2 rounded-full bg-red-100/80 dark:bg-[rgba(255,0,102,0.15)] text-error-600 dark:text-error-400 mr-3 dark:shadow-glow-red-sm">
                      <FiTrash2 className="w-4 h-4" />
                    </div>
                    <div>
                      <h3 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>Delete Account</h3>
                      <p className="text-sm" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                        Permanently delete your account and all data
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="danger"
                    onClick={() => setDeleteAccountDialogOpen(true)}
                    size="sm"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Delete Account Confirmation Dialog */}
      <ConfirmDialog
        open={deleteAccountDialogOpen}
        onClose={() => setDeleteAccountDialogOpen(false)}
        onConfirm={handleDeleteAccount}
        title="Delete Account"
        content={
          <div className="text-center py-3">
            <div className="mx-auto flex items-center justify-center h-14 w-14 rounded-full bg-red-100/80 dark:bg-[rgba(255,0,102,0.15)] mb-4 dark:shadow-glow-red-sm">
              <FiTrash2 className="h-7 w-7 text-error-600 dark:text-error-400" />
            </div>
            <p style={{ color: 'var(--theme-text)' }} className="mb-2">Are you sure you want to delete your account?</p>
            <p className="text-sm" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>This action cannot be undone and all your data will be permanently deleted.</p>
          </div>
        }
        confirmText="Delete Account"
        confirmVariant="danger"
      />
    </div>
  );
};

export default SettingsPage;
