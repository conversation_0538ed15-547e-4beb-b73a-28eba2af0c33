import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { TailwindThemeProvider } from './context/TailwindThemeContext';
import { TailwindAlertProvider } from './context/TailwindAlertContext';
import { AuthProvider } from './context/AuthContext';
import { LayoutProvider } from './context/LayoutContext';
import ProtectedRoute from './components/ProtectedRoute';
import { useAuth } from './context/AuthContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import ErrorFallback from './components/common/ErrorFallback';


// Component to handle default route redirection based on authentication status
const DefaultRedirect: React.FC = () => {
  const { loading, isAuthenticated } = useAuth();

  // Don't make additional auth calls here - AuthContext handles initialization
  // This prevents race conditions and duplicate requests during page refresh

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check authentication status - no additional API calls needed
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  } else {
    return <Navigate to="/login" replace />;
  }
};

// Layouts
import TailwindLayout from './components/layout/TailwindLayout';

// Loading component for Suspense fallback
const LoadingComponent = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Lazy load pages for better performance
// Auth related pages (load immediately as they're critical)
import TailwindAuthForm from './components/TailwindAuthForm';
import TailwindPasswordReset from './pages/TailwindPasswordReset';
import VerifyEmail from './pages/VerifyEmail';
import ResendVerification from './pages/ResendVerification';
import TailwindNotFound from './pages/TailwindNotFound';

// Lazy loaded pages (load on demand)
const TailwindDashboard = lazy(() => import('./pages/TailwindDashboard'));
const TailwindSimpleFileUpload = lazy(() => import('./pages/TailwindSimpleFileUpload'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const GraphVisualization = lazy(() => import('./pages/GraphVisualization'));
const NoticeGenerationPage = lazy(() => import('./pages/NoticeGenerationPage'));
const ComplaintDetail = lazy(() => import('./pages/ComplaintDetail'));
const ComplaintSummary = lazy(() => import('./pages/ComplaintSummary'));
const InformationSearchPage = lazy(() => import('./pages/InformationSearchPage'));

const ComplaintAnalysisPage = lazy(() => import('./pages/ComplaintAnalysisPage'));

// Custom error handler for the global error boundary
const handleError = (_error: Error, _errorInfo: React.ErrorInfo) => {
  // You could send this to a monitoring service like Sentry here
};

const App: React.FC = () => {
  return (
    <ErrorBoundary
      fallback={<ErrorFallback showDetails={import.meta.env.MODE !== 'production'} />}
      onError={handleError}
    >
      <TailwindThemeProvider>
        <TailwindAlertProvider>
          <AuthProvider>
              <Router>
                <LayoutProvider>
                  <Routes>
                  {/* Public routes */}
                  <Route path="/login" element={<TailwindAuthForm />} />
                  <Route path="/register" element={<TailwindAuthForm isRegister={true} />} />

                  {/* Public routes - Auth related */}
                  <Route path="/forgot-password" element={<TailwindPasswordReset />} />
                  <Route path="/reset-password/:token" element={<TailwindPasswordReset />} />
                  <Route path="/verify-email/:token" element={<VerifyEmail />} />
                  <Route path="/resend-verification" element={<ResendVerification />} />

                  {/* Protected routes - Each wrapped in its own ErrorBoundary */}
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <TailwindDashboard />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/complaint-upload" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <TailwindSimpleFileUpload />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/profile" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <ProfilePage />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <SettingsPage />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  {/* Support both singular and plural routes for backward compatibility */}
                  <Route path="/complaint/:id" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <ComplaintDetail />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />
                  <Route path="/complaints/:id" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <ComplaintDetail />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/graph/:id" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <GraphVisualization />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/complaint-summary/:id" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <ComplaintSummary />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/notices/generate/:id" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <NoticeGenerationPage />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/information-search" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <InformationSearchPage />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />



                  <Route path="/complaint-analysis" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <ComplaintAnalysisPage />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  {/* Default route - redirect based on authentication status */}
                  <Route path="/" element={<DefaultRedirect />} />

                  {/* Catch-all route */}
                  <Route path="*" element={<TailwindNotFound />} />
                  </Routes>
                  </LayoutProvider>
                </Router>
            </AuthProvider>
          </TailwindAlertProvider>
      </TailwindThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
