import authService from './authService';

/**
 * Service for handling user-related API calls
 */
const userService = {
  /**
   * Fetch user profile information
   * @returns Promise with the user profile data
   */
  async getUserProfile() {
    try {
      // For GET requests, we don't need to send a CSRF token
      // But we'll store it if it comes back in the response
      const response = await authService.api.get('/users/profile', {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      // Check for CSRF token in the response header
      const csrfToken = response.headers['x-csrf-token'];
      if (csrfToken) {

        // Store the token in memory via authService
        authService.storeCSRFToken(csrfToken);
      }

      return response.data;
    } catch (error) {

      throw error;
    }
  },

  /**
   * Update user profile information
   * @param profileData User profile data to update
   * @returns Promise with the updated user profile data
   */
  async updateUserProfile(profileData: any) {
    try {
      // First fetch a CSRF token to ensure we have a valid one
      await authService.fetchCsrfToken();

      // Get the CSRF token
      const csrfToken = authService.getCsrfToken();

      // Make the request with the CSRF token
      const response = await authService.api.put('/users/profile', profileData, {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      return response.data;
    } catch (error) {

      throw error;
    }
  },

  /**
   * Upload a notice template for the user
   * @param formData Form data with the template file
   * @returns Promise with the upload result
   */
  async uploadTemplate(formData: FormData) {
    try {
      // Ensure we have a fresh CSRF token before proceeding
      await authService.fetchCsrfToken();

      // Get the CSRF token from the cookie - this is the most reliable approach
      // for the double-submit pattern
      const cookies = document.cookie.split(';');
      const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('csrf_token='));

      let csrfToken: string | null = null;

      if (csrfCookie) {
        csrfToken = csrfCookie.trim().substring('csrf_token='.length);
      } else {
        throw new Error('Security token not available. Please refresh the page and try again.');
      }

      // Create headers with the CSRF token
      // We'll include it in multiple headers for better compatibility
      const headers: Record<string, string> = {
        'X-CSRF-Token': csrfToken,
        'X-Requested-With': 'XMLHttpRequest',
        'csrf-token': csrfToken,
        'x-xsrf-token': csrfToken
      };



      // Use axios for consistency with other API calls
      const response = await authService.api.post('/users/template', formData, {
        headers: headers,
        withCredentials: true
      });

      return response.data;
    } catch (error: any) {
      // Enhanced error handling
      // Check if this is an axios error with response data
      if (error.response) {

        // If it's a CSRF error, provide a more helpful message
        if (error.response.status === 403) {
          throw new Error('Security validation failed. Please refresh the page and try again.');
        }
      }

      throw error;
    }
  },

  /**
   * Get the user's notice template
   * @returns Promise with the template data
   */
  async getTemplate() {
    try {
      // For GET requests, we don't need to send a CSRF token
      // But we'll store it if it comes back in the response
      const response = await authService.api.get('/users/template', {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      // Check for CSRF token in the response header
      const csrfToken = response.headers['x-csrf-token'];
      if (csrfToken) {

        // Store the token in memory via authService
        authService.storeCSRFToken(csrfToken);
      }

      return response.data;
    } catch (error) {

      throw error;
    }
  },

  /**
   * Delete the user's notice template
   * @returns Promise with the deletion result
   */
  async deleteTemplate() {
    try {
      // First fetch a CSRF token to ensure we have a valid one
      await authService.fetchCsrfToken();

      // Get the CSRF token
      const csrfToken = authService.getCsrfToken();

      // Make the request with the CSRF token
      const response = await authService.api.delete('/users/template', {
        headers: {
          'X-CSRF-Token': csrfToken || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      return response.data;
    } catch (error) {

      throw error;
    }
  },

  /**
   * Get the user's subscription details
   * @returns Promise with the subscription details
   */
  async getSubscriptionDetails() {
    try {
      // First fetch a CSRF token to ensure we have a valid one
      await authService.fetchCsrfToken();

      // Get the CSRF token
      const csrfToken = authService.getCsrfToken();

      // Make the request with the CSRF token
      const response = await authService.api.get('/users/subscription', {
        headers: {
          'X-CSRF-Token': csrfToken || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      // Check for CSRF token in the response header
      const newCsrfToken = response.headers['x-csrf-token'];
      if (newCsrfToken) {

        // Store the token in memory via authService
        authService.storeCSRFToken(newCsrfToken);
      }

      return response.data;
    } catch (error) {

      throw error;
    }
  }
};

export default userService;
