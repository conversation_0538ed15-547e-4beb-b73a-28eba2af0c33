import axios, { InternalAxiosRequestConfig, AxiosError, AxiosRequestConfig } from 'axios';
import { User } from '../types/auth';
import { API_URL } from '../config';


// Extend InternalAxiosRequestConfig to include _retry property
interface ExtendedInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  withCredentials: true
});

// Add request interceptor for CSRF token
api.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    try {
      const csrfToken = getCsrfToken();
      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken;
      }
      return config;
    } catch (error) {
      return Promise.reject(error);
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for token refresh and session conflicts
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedInternalAxiosRequestConfig;

    // Handle network errors
    if (!error.response) {
      // Network error (no response from server)
      if (error.code === 'ECONNABORTED') {
        return Promise.reject(new Error('Request timed out. Please check your internet connection.'));
      }
      if (error.message === 'Network Error') {
        return Promise.reject(new Error('Network error. Please check your internet connection.'));
      }
      return Promise.reject(new Error('Unable to connect to the server. Please try again later.'));
    }

    // If error is 401 and we haven't tried to refresh token yet
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        await authService.refreshToken();
        // Retry the original request
        return api(originalRequest as AxiosRequestConfig);
      } catch (refreshError: any) {
        // Check if this is a session conflict error
        if (refreshError.response?.status === 409 &&
            refreshError.response?.data &&
            (refreshError.response.data as any).code === 'SESSION_CONFLICT') {
          // Let the error propagate to be handled by the UI components
          return Promise.reject(refreshError);
        }

        // If refresh fails, propagate the error to AuthContext for handling
        return Promise.reject(new Error('Session expired. Please login again.'));
      }
    }

    // If we've already tried to refresh and still got 401, propagate the error
    if (error.response?.status === 401 && originalRequest?._retry) {
      return Promise.reject(new Error('Session expired. Please login again.'));
    }

    // Handle specific HTTP status codes
    switch (error.response.status) {
      case 400:
        return Promise.reject(new Error('Invalid request. Please check your input.'));
      case 403:
        return Promise.reject(new Error('Access denied. You do not have permission to perform this action.'));
      case 404:
        return Promise.reject(new Error('Resource not found.'));
      case 408:
        return Promise.reject(new Error('Request timeout. Please try again.'));
      case 500:
        return Promise.reject(new Error('Server error. Please try again later.'));
      case 502:
        return Promise.reject(new Error('Bad gateway. Please try again later.'));
      case 503:
        return Promise.reject(new Error('Service unavailable. Please try again later.'));
      case 504:
        return Promise.reject(new Error('Gateway timeout. Please try again later.'));
    }

    // Handle session conflicts at the API level
    if (error.response?.status === 409 &&
        error.response?.data &&
        (error.response.data as any).code === 'SESSION_CONFLICT') {
      // Let the error propagate to be handled by the UI components
      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

// Get CSRF token from cookie (CSRF tokens are not HttpOnly for double-submit pattern)
const getCsrfToken = (): string => {
  try {
    if (!document.cookie) {
      return '';
    }

    const cookies = document.cookie.split('; ').filter(cookie => cookie.trim() !== '');
    const csrfCookie = cookies.find(row => row.startsWith('csrf_token='));

    if (!csrfCookie) {
      return '';
    }

    return csrfCookie.split('=')[1] || '';
  } catch (error) {
    console.error('Error getting CSRF token:', error);
    return '';
  }
};

// Simple auth service with enhanced token management
const authService = {
  api,

  // Get CSRF token
  getCsrfToken,

  // Store CSRF token
  storeCSRFToken: (_token: string) => {
    // Don't store CSRF token in localStorage for security reasons
    // The token should only be in the HttpOnly cookie set by the backend
    // Just log it for debugging
  },

  // Fetch CSRF token with retry
  fetchCsrfToken: async (retryCount = 0): Promise<string> => {
    try {
      await api.get('/auth/verify-token');
      return getCsrfToken();
    } catch (error: any) {
      // Only retry on network errors, not on 401/403
      if (!error.response && retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.fetchCsrfToken(retryCount + 1);
      }
      // If backend says not authenticated, propagate
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        throw new Error('Not authenticated');
      }
      // For other errors (network, aborted), throw a special error
      throw new Error('CSRF token fetch failed: ' + (error.message || 'Unknown error'));
    }
  },

  // Register new user with validation
  register: async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    designation?: string;
  }) => {
    try {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        throw new Error('Invalid email format');
      }

      // Validate password strength
      if (userData.password.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Use longer timeout for registration due to email sending
      const response = await api.post('/auth/register', userData, {
        timeout: 60000 // 60 seconds timeout for email sending
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Login user with enhanced error handling and two-step verification support
  login: async (email: string, password: string) => {
    try {
      const formData = new URLSearchParams();
      formData.append('username', email);
      formData.append('password', password);

      // Explicitly fetch CSRF token before login request
      const csrfToken = await authService.fetchCsrfToken();

      // Use the regular login endpoint which now handles two-step verification
      const response = await api.post('/auth/login', formData.toString(), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-CSRF-Token': csrfToken, // Explicitly add CSRF token
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Verify OTP with retry mechanism
  verifyOTP: async (email: string, otp: string, retryCount = 0): Promise<any> => {
    try {
      const response = await api.post('/auth/verify-otp', { email, otp }, {
        timeout: 20000 // 20 seconds timeout for OTP verification
      });
      return response.data;
    } catch (error) {

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.verifyOTP(email, otp, retryCount + 1);
      }

      throw error;
    }
  },

  // Two-step verification with enhanced security
  verifyTwoStep: async (email: string, otp: string) => {
    try {
      const response = await api.post('/auth/two-step/verify', { email, otp });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Resend OTP with rate limiting
  resendOTP: async (email: string) => {
    try {
      const response = await api.post('/auth/resend-otp', { email }, {
        timeout: 30000 // 30 seconds timeout for email sending
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Check if user is authenticated with retry and abort signal support
  isAuthenticated: async (retryCount = 0, signal?: AbortSignal): Promise<boolean | 'network-error'> => {
    try {
      // Create a timeout for the request to prevent hanging
      const timeoutController = new AbortController();
      const timeoutId = setTimeout(() => timeoutController.abort(), 5000);

      // Combine signals if provided (with fallback for older browsers)
      let combinedSignal = timeoutController.signal;
      if (signal) {
        // Use AbortSignal.any if available, otherwise use the provided signal
        if (typeof AbortSignal.any === 'function') {
          combinedSignal = AbortSignal.any([signal, timeoutController.signal]);
        } else {
          // Fallback: use the provided signal and handle timeout separately
          combinedSignal = signal;
        }
      }

      // Create a direct axios instance to bypass interceptors for auth check
      const directApi = axios.create({
        baseURL: api.defaults.baseURL,
        withCredentials: true,
        timeout: 5000
      });

      const response = await directApi.get('/auth/verify-token', {
        signal: combinedSignal,
        headers: {
          'X-CSRF-Token': getCsrfToken()
        }
      });

      clearTimeout(timeoutId);
      return response.data.valid;
    } catch (error: any) {
      // Handle aborted requests gracefully
      if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
        return 'network-error';
      }

      // Only retry on network errors, not on 401/403
      if (!error.response && retryCount < 2) {
        const delay = Math.pow(2, retryCount) * 500;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.isAuthenticated(retryCount + 1, signal);
      }

      // If backend says not authenticated, return false
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        return false;
      }

      // For other errors (network, aborted), return 'network-error'
      return 'network-error';
    }
  },

  // Logout by invalidating the session on the backend
  logout: async () => {
    try {
      await api.post('/auth/logout', {}, {
        headers: {
          'X-CSRF-Token': getCsrfToken()
        }
      });
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  },

  // Get user data with enhanced error handling
  getUserData: async (): Promise<User | null> => {
    try {
      const response = await api.get('/users/profile');
      const userData = response.data;

      // Ensure all fields are properly mapped
      const mappedUser = {
        id: userData.id,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        designation: userData.designation,
        organization: userData.organization,
        paid: Boolean(userData.paid),
        email_verified: Boolean(userData.emailVerified),
        has_template: Boolean(userData.hasTemplate),
        subscription_expires: userData.subscription_expires,
        complaint_count: userData.complaint_count || 0,
        two_step_enabled: Boolean(userData.twoStepEnabled)
      };

      return mappedUser;
    } catch (error: any) {
      return null;
    }
  },

  // Enhanced token refresh with retry and better error handling
  refreshToken: async (retryCount = 0): Promise<any> => {
    try {
      // Make sure we're using the correct endpoint that matches the backend
      const response = await api.post('/auth/refresh', {}, {
        withCredentials: true, // Ensure cookies are sent
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000 // 5 second timeout for refresh token
      });
      return response.data;
    } catch (error: any) {
      // Handle specific error typesd
      if (error.code === 'ECONNABORTED') {
        throw new Error('Token refresh timed out. Please try again.');
      }
      if (!error.response) {
        throw new Error('Network error during token refresh. Please check your connection.');
      }

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.refreshToken(retryCount + 1);
      }

      // If all retries failed, throw a specific error
      throw new Error('Failed to refresh token. Please login again.');
    }
  }
};

export default authService;
