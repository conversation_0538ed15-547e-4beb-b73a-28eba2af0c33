/**
 * NCRP Matrix Theme System
 *
 * This file defines the color palette and theme variables for both light and dark modes.
 * It ensures consistency across the application UI with improved cyberpunk aesthetics.
 */

export const colors = {
  // Primary colors
  primary: {
    50: '#e6f7ff',
    100: '#b3e6ff',
    200: '#80d4ff',
    300: '#4dc2ff',
    400: '#1aafff',
    500: '#00aaff', // Main primary color
    600: '#0088cc',
    700: '#006699',
    800: '#004466',
    900: '#002233',
  },
  
  // Secondary colors (orange)
  secondary: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316', // Main secondary color
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
  },
  
  // Success colors (green)
  success: {
    50: '#e6fff2',
    100: '#b3ffe0',
    200: '#80ffcd',
    300: '#4dffbb',
    400: '#1affa8',
    500: '#00ff95', // Main success color
    600: '#00cc78',
    700: '#00995a',
    800: '#00663c',
    900: '#00331e',
  },
  
  // Error colors (red)
  error: {
    50: '#ffe6ed',
    100: '#ffb3d1',
    200: '#ff80b5',
    300: '#ff4d99',
    400: '#ff1a7d',
    500: '#ff0066', // Main error color
    600: '#cc0052',
    700: '#99003d',
    800: '#660029',
    900: '#330014',
  },
  
  // Warning colors (amber)
  warning: {
    50: '#fff8e6',
    100: '#ffeab3',
    200: '#ffdc80',
    300: '#ffce4d',
    400: '#ffc01a',
    500: '#ffb200', // Main warning color
    600: '#cc8f00',
    700: '#996b00',
    800: '#664800',
    900: '#332400',
  },
  
  // Neutral colors for both light and dark modes
  gray: {
    50: '#f8faff', // Light mode background
    100: '#eef2fa',
    200: '#e4e9f2',
    300: '#d1d8e6',
    400: '#a3aec2',
    500: '#7a8599',
    600: '#606a7c',
    700: '#4a5568',
    800: '#323a4a',
    900: '#1a202c', // Dark text in light mode
  },
  
  // Improved dark cyberpunk colors
  dark: {
    50: '#e8f4ff',
    100: '#c2d9f0',
    200: '#8fa8c4',
    300: '#6b8aa8',
    400: '#4a6b8a',
    500: '#2d4a6b',
    600: '#1a1a25',
    700: '#12121a',
    800: '#0a0a0f', // Improved dark mode background
    900: '#050508',
  },
};

// Theme configuration for light and dark modes
export const theme = {
  light: {
    background: {
      primary: colors.gray[50],
      secondary: colors.gray[100],
      tertiary: colors.gray[200],
      card: 'white',
      input: 'white',
    },
    text: {
      primary: colors.gray[900],
      secondary: colors.gray[700],
      tertiary: colors.gray[500],
      inverse: colors.gray[50],
    },
    border: {
      light: colors.gray[200],
      medium: colors.gray[300],
      strong: colors.gray[400],
    },
    shadow: {
      sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05)',
    },
    glass: {
      background: 'rgba(255, 255, 255, 0.8)',
      border: 'rgba(255, 255, 255, 0.5)',
      shadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
    },
  },
  dark: {
    background: {
      primary: colors.dark[800],
      secondary: colors.dark[700],
      tertiary: colors.dark[600],
      card: 'rgba(10, 10, 15, 0.85)',
      input: 'rgba(18, 18, 26, 0.9)',
    },
    text: {
      primary: colors.dark[50],
      secondary: colors.dark[100],
      tertiary: colors.dark[200],
      inverse: colors.dark[900],
    },
    border: {
      light: 'rgba(0, 170, 255, 0.25)',
      medium: 'rgba(0, 170, 255, 0.4)',
      strong: 'rgba(0, 170, 255, 0.6)',
    },
    shadow: {
      sm: '0 2px 8px rgba(0, 0, 0, 0.4)',
      md: '0 6px 16px rgba(0, 0, 0, 0.5)',
      lg: '0 12px 32px rgba(0, 0, 0, 0.6)',
    },
    glass: {
      background: 'rgba(10, 10, 15, 0.85)',
      border: 'rgba(0, 170, 255, 0.3)',
      shadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
    },
    glow: {
      blue: {
        sm: '0 0 8px rgba(0, 170, 255, 0.6)',
        md: '0 0 16px rgba(0, 170, 255, 0.8)',
        lg: '0 0 24px rgba(0, 170, 255, 1), 0 0 48px rgba(0, 170, 255, 0.6)',
      },
      purple: {
        sm: '0 0 8px rgba(138, 43, 226, 0.6)',
        md: '0 0 16px rgba(138, 43, 226, 0.8)',
        lg: '0 0 24px rgba(138, 43, 226, 1), 0 0 48px rgba(138, 43, 226, 0.6)',
      },
      orange: {
        sm: '0 0 8px rgba(251, 146, 60, 0.6)',
        md: '0 0 16px rgba(251, 146, 60, 0.8)',
        lg: '0 0 24px rgba(251, 146, 60, 1), 0 0 48px rgba(251, 146, 60, 0.6)',
      },
      green: {
        sm: '0 0 8px rgba(0, 255, 149, 0.6)',
        md: '0 0 16px rgba(0, 255, 149, 0.8)',
        lg: '0 0 24px rgba(0, 255, 149, 1), 0 0 48px rgba(0, 255, 149, 0.6)',
      },
      red: {
        sm: '0 0 8px rgba(255, 0, 102, 0.6)',
        md: '0 0 16px rgba(255, 0, 102, 0.8)',
        lg: '0 0 24px rgba(255, 0, 102, 1), 0 0 48px rgba(255, 0, 102, 0.6)',
      },
    },
  },
};

export default theme;
