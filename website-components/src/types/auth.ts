export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name?: string;
  designation?: string;
  paid: boolean;
  email_verified: boolean;
  has_template?: boolean;
  subscription_start_date?: string | null;
  subscription_expires?: string | null;
  complaint_count?: number;
  two_step_enabled?: boolean;
  organization?: string;
  session_id?: string;  // Added for session management
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name?: string;
  designation?: string;
}

export interface RegisterResponse {
  id: string;
  email: string;
  first_name: string;
  last_name?: string;
  message?: string;
}
