/**
 * Console Blocker Utility
 *
 * Blocks all console logging in production environments
 */

/**
 * Block all console methods in production
 */
function blockConsoleInProduction(): void {
  const environment = import.meta.env.VITE_ENV || import.meta.env.MODE || 'development';

  if (environment === 'production') {
    const noop = () => {};

    // Block all standard console methods (browser environments)
    console.log = noop;
    console.error = noop;
    console.warn = noop;
    console.info = noop;
    console.debug = noop;
    console.trace = noop;
    console.table = noop;
    console.group = noop;
    console.groupCollapsed = noop;
    console.groupEnd = noop;
    console.time = noop;
    console.timeEnd = noop;
    console.timeLog = noop;
    console.count = noop;
    console.countReset = noop;
    console.assert = noop;
    console.clear = noop;
    console.dir = noop;
    console.dirxml = noop;
    // @ts-ignore - profile methods may not exist in all environments
    console.profile = noop;
    // @ts-ignore - profile methods may not exist in all environments
    console.profileEnd = noop;

    // Block window.console access to prevent bypassing (browser environments)
    if (typeof window !== 'undefined') {
      try {
        Object.defineProperty(window, 'console', {
          value: new Proxy(console, {
            get: () => noop,
            set: () => false
          }),
          writable: false,
          configurable: false
        });
      } catch (e) {
        // Fallback if defineProperty fails
        (window as any).console = new Proxy(console, {
          get: () => noop,
          set: () => false
        });
      }
    }

    // Block global console access (Node.js environments)
    // @ts-ignore - global may not exist in browser environments
    if (typeof global !== 'undefined') {
      try {
        // @ts-ignore - global may not exist in browser environments
        Object.defineProperty(global, 'console', {
          value: new Proxy(console, {
            get: () => noop,
            set: () => false
          }),
          writable: false,
          configurable: false
        });
      } catch (e) {
        // Fallback if defineProperty fails
        // @ts-ignore - global may not exist in browser environments
        (global as any).console = new Proxy(console, {
          get: () => noop,
          set: () => false
        });
      }
    }
  }
}

// Auto-execute on import
blockConsoleInProduction();

export default blockConsoleInProduction;
