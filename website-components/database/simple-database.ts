import Database from 'better-sqlite3';
import { app } from 'electron';
import { join } from 'path';
import { randomBytes, createCipheriv, createDecipheriv } from 'crypto';
import * as keytar from 'keytar';

const SERVICE_NAME = 'NCRP-Matrix-Desktop';
const ACCOUNT_NAME = 'database-encryption-key';

let db: Database.Database | null = null;

async function getOrCreateEncryptionKey(userEmail: string): Promise<string> {
  const keyAccount = `${ACCOUNT_NAME}-${userEmail}`;
  let key = await keytar.getPassword(SERVICE_NAME, keyAccount);
  if (!key) {
    const keyBuffer = randomBytes(32);
    key = keyBuffer.toString('hex');
    await keytar.setPassword(SERVICE_NAME, keyAccount, key);
  }
  return key;
}

class EncryptionService {
  private encryptionKey: string | null = null;
  async initialize(userEmail: string) {
    this.encryptionKey = await getOrCreateEncryptionKey(userEmail);
  }
  encrypt(data: string): string {
    if (!this.encryptionKey || !data) return data;
    const iv = randomBytes(16);
    const cipher = createCipheriv('aes-256-cbc', Buffer.from(this.encryptionKey, 'hex'), iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }
  decrypt(encryptedData: string): string {
    if (!this.encryptionKey || !encryptedData) return encryptedData;
    const parts = encryptedData.split(':');
    if (parts.length !== 2) return encryptedData;
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    const decipher = createDecipheriv('aes-256-cbc', Buffer.from(this.encryptionKey, 'hex'), iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
export const encryptionService = new EncryptionService();

export function setupDatabase(userEmail: string) {
  const dbPath = join(app.getPath('userData'), 'ncrp-matrix.db');
  db = new Database(dbPath);
  db.pragma('journal_mode = WAL');
  db.pragma('synchronous = NORMAL');
  db.pragma('foreign_keys = ON');
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT NOT NULL,
      name TEXT NOT NULL,
      role TEXT NOT NULL,
      last_sync INTEGER NOT NULL
    );
    CREATE TABLE IF NOT EXISTS templates (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      type TEXT NOT NULL,
      content TEXT NOT NULL,
      file_name TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      is_default INTEGER DEFAULT 0,
      description TEXT,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );
    CREATE TABLE IF NOT EXISTS complaints (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      title TEXT NOT NULL,
      complaint_number TEXT NOT NULL,
      complainant_name TEXT NOT NULL,
      amount REAL NOT NULL,
      category TEXT NOT NULL,
      subcategory TEXT NOT NULL,
      date TEXT NOT NULL,
      fraud_type TEXT NOT NULL,
      status TEXT NOT NULL,
      html_content TEXT,
      extracted_data TEXT,
      graph_data TEXT,
      bank_notice_data TEXT,
      csv_data_base64 TEXT,
      notes TEXT,
      metadata TEXT,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    );
    CREATE TABLE IF NOT EXISTS transactions (
      id TEXT PRIMARY KEY,
      complaint_id TEXT NOT NULL,
      date TEXT NOT NULL,
      amount REAL NOT NULL,
      layer TEXT NOT NULL,
      fraud_type TEXT NOT NULL,
      sender_account TEXT NOT NULL,
      sender_transaction_id TEXT NOT NULL,
      sender_bank TEXT NOT NULL,
      receiver_account TEXT NOT NULL,
      receiver_transaction_id TEXT NOT NULL,
      receiver_bank TEXT NOT NULL,
      receiver_ifsc TEXT NOT NULL,
      receiver_info TEXT NOT NULL,
      txn_type TEXT NOT NULL,
      reference TEXT NOT NULL,
      description TEXT NOT NULL,
      account_number TEXT NOT NULL,
      bank_name TEXT NOT NULL,
      transaction_type TEXT NOT NULL,
      sr_no INTEGER NOT NULL,
      extracted_at INTEGER NOT NULL,
      source TEXT NOT NULL,
      is_valid INTEGER NOT NULL,
      validation_errors TEXT,
      created_at INTEGER NOT NULL
    );
    CREATE TABLE IF NOT EXISTS graph_data (
      id TEXT PRIMARY KEY,
      complaint_id TEXT NOT NULL,
      nodes TEXT NOT NULL,
      edges TEXT NOT NULL,
      layout TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    );
  `);
  encryptionService.initialize(userEmail);
}

export function saveTemplate(template: {
  id?: string; // Make ID optional for new templates
  userId: string;
  name: string;
  type: string;
  content: string;
  fileName: string;
  fileSize?: number;
  mimeType?: string;
  description?: string;
  isDefault?: boolean;
}) {
  if (!db) throw new Error('DB not initialized');
  const now = Date.now();
  let templateId = template.id || `template_${now}_${Math.random().toString(36).substr(2, 9)}`;

  // If a new template is being uploaded and marked as default,
  // set any existing default templates of the same type for this user to non-default.
  if (template.isDefault) {
    db.prepare('UPDATE templates SET is_default = 0, updated_at = ? WHERE user_id = ? AND type = ? AND is_default = 1')
      .run(now, template.userId, template.type);
  }

  // Check if a template with this ID already exists (for updates)
  const existingTemplate = db.prepare('SELECT id FROM templates WHERE id = ?').get(templateId);

  if (existingTemplate) {
    // Update existing template
    const stmt = db.prepare(`
      UPDATE templates SET
        name = ?, type = ?, content = ?, file_name = ?, file_size = ?,
        mime_type = ?, is_default = ?, description = ?, updated_at = ?
      WHERE id = ? AND user_id = ?
    `);
    stmt.run(
      template.name,
      template.type,
      encryptionService.encrypt(template.content),
      template.fileName,
      template.fileSize || 0,
      template.mimeType || 'application/octet-stream',
      template.isDefault ? 1 : 0,
      template.description || '',
      now,
      templateId,
      template.userId
    );
  } else {
    // Insert new template
    const stmt = db.prepare(`
      INSERT INTO templates (id, user_id, name, type, content, file_name, file_size, mime_type, is_default, description, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    stmt.run(
      templateId,
      template.userId,
      template.name,
      template.type,
      encryptionService.encrypt(template.content),
      template.fileName,
      template.fileSize || 0,
      template.mimeType || 'application/octet-stream',
      template.isDefault ? 1 : 0,
      template.description || '',
      now,
      now
    );
  }
  return templateId;
}

export function getTemplates(userId: string, type?: string) {
  if (!db) throw new Error('DB not initialized');
  let query = 'SELECT * FROM templates WHERE user_id = ?';
  const params: any[] = [userId];
  if (type) {
    query += ' AND type = ?';
    params.push(type);
  }
  query += ' ORDER BY created_at DESC';
  const rows = db.prepare(query).all(...params);
  return rows.map((row: any) => ({
    ...row,
    content: encryptionService.decrypt((row as any).content)
  }));
}

export function deleteTemplate(templateId: string, userId: string) {
  if (!db) throw new Error('DB not initialized');
  db.prepare('DELETE FROM templates WHERE id = ? AND user_id = ?').run(templateId, userId);
}

export function closeDatabase() {
  if (db) db.close();
  db = null;
}

// Complaint CRUD
export function saveComplaint(complaint: any) {
  if (!db) throw new Error('DB not initialized');
  const now = Date.now();
  const stmt = db.prepare(`
    INSERT INTO complaints (
      id, user_id, title, complaint_number, complainant_name, amount, category, subcategory, date, fraud_type, status, html_content, extracted_data, graph_data, bank_notice_data, csv_data_base64, notes, metadata, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  stmt.run(
    complaint.id,
    complaint.userId,
    complaint.title,
    complaint.complaintNumber,
    complaint.complainantName,
    complaint.amount,
    complaint.category,
    complaint.subcategory,
    complaint.date,
    complaint.fraudType,
    complaint.status,
    complaint.htmlContent,
    complaint.extractedData,
    complaint.graphData,
    complaint.bankNoticeData,
    complaint.csvDataBase64,
    complaint.notes,
    complaint.metadata,
    now,
    now
  );
  return complaint.id;
}

export function getComplaints(userId?: string, filters?: any) {
  if (!db) throw new Error('DB not initialized');
  let query = 'SELECT * FROM complaints';
  const params: any[] = [];
  if (userId) {
    query += ' WHERE user_id = ?';
    params.push(userId);
  }
  query += ' ORDER BY created_at DESC';
  return db.prepare(query).all(...params);
}

export function deleteComplaint(id: string) {
  if (!db) throw new Error('DB not initialized');
  db.prepare('DELETE FROM complaints WHERE id = ?').run(id);
  db.prepare('DELETE FROM transactions WHERE complaint_id = ?').run(id);
  db.prepare('DELETE FROM graph_data WHERE complaint_id = ?').run(id);
}

export function saveTransactions(transactions: any[]) {
  if (!db) throw new Error('DB not initialized');
  const stmt = db.prepare(`
    INSERT INTO transactions (
      id, complaint_id, date, amount, layer, fraud_type, sender_account, sender_transaction_id, sender_bank, receiver_account, receiver_transaction_id, receiver_bank, receiver_ifsc, receiver_info, txn_type, reference, description, account_number, bank_name, transaction_type, sr_no, extracted_at, source, is_valid, validation_errors, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  for (const txn of transactions) {
    stmt.run(
      txn.id,
      txn.complaintId,
      txn.date,
      txn.amount,
      txn.layer,
      txn.fraudType,
      txn.senderAccount,
      txn.senderTransactionId,
      txn.senderBank,
      txn.receiverAccount,
      txn.receiverTransactionId,
      txn.receiverBank,
      txn.receiverIfsc,
      txn.receiverInfo,
      txn.txnType,
      txn.reference,
      txn.description,
      txn.accountNumber,
      txn.bankName,
      txn.transactionType,
      txn.srNo,
      txn.extractedAt,
      txn.source,
      txn.isValid,
      txn.validationErrors,
      Date.now()
    );
  }
}

export function saveTransaction(transaction: any) {
  if (!db) throw new Error('DB not initialized');
  const stmt = db.prepare(`
    INSERT INTO transactions (
      id, complaint_id, date, amount, layer, fraud_type, sender_account, sender_transaction_id, sender_bank, receiver_account, receiver_transaction_id, receiver_bank, receiver_ifsc, receiver_info, txn_type, reference, description, account_number, bank_name, transaction_type, sr_no, extracted_at, source, is_valid, validation_errors, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  stmt.run(
    transaction.id,
    transaction.complaintId,
    transaction.date,
    transaction.amount,
    transaction.layer,
    transaction.fraudType,
    transaction.senderAccount,
    transaction.senderTransactionId,
    transaction.senderBank,
    transaction.receiverAccount,
    transaction.receiverTransactionId,
    transaction.receiverBank,
    transaction.receiverIfsc,
    transaction.receiverInfo,
    transaction.txnType,
    transaction.reference,
    transaction.description,
    transaction.accountNumber,
    transaction.bankName,
    transaction.transactionType,
    transaction.srNo,
    transaction.extractedAt,
    transaction.source,
    transaction.isValid,
    transaction.validationErrors,
    Date.now()
  );
}

export function updateTransaction(id: string, updates: any) {
  if (!db) throw new Error('DB not initialized');
  const fields = Object.keys(updates).map(k => `${k} = ?`).join(', ');
  const values = Object.values(updates);
  values.push(id);
  db.prepare(`UPDATE transactions SET ${fields} WHERE id = ?`).run(...values);
}

export function deleteTransaction(id: string) {
  if (!db) throw new Error('DB not initialized');
  db.prepare('DELETE FROM transactions WHERE id = ?').run(id);
}

export function getComplaintById(id: string) {
  if (!db) throw new Error('DB not initialized');
  return db.prepare('SELECT * FROM complaints WHERE id = ?').get(id);
}

export function getTransactionsByComplaint(complaintId: string) {
  if (!db) throw new Error('DB not initialized');
  return db.prepare('SELECT * FROM transactions WHERE complaint_id = ?').all(complaintId);
}

export function getGraphData(complaintId: string) {
  if (!db) throw new Error('DB not initialized');
  return db.prepare('SELECT * FROM graph_data WHERE complaint_id = ?').get(complaintId);
}

export function updateComplaint(id: string, updates: any) {
  if (!db) throw new Error('DB not initialized');
  const fields = Object.keys(updates).map(k => `${k} = ?`).join(', ');
  const values = Object.values(updates);
  values.push(id);
  db.prepare(`UPDATE complaints SET ${fields} WHERE id = ?`).run(...values);
}

// Implement saveGraphData in simpleDB if missing
export function saveGraphData(graphData: any) {
  if (!db) throw new Error('DB not initialized');
  const now = Date.now();
  const stmt = db.prepare(`
    INSERT OR REPLACE INTO graph_data (
      id, complaint_id, nodes, edges, layout, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  stmt.run(
    graphData.id,
    graphData.complaintId,
    graphData.nodes,
    graphData.edges,
    graphData.layout,
    now,
    now
  );
  return graphData.id;
}

// User CRUD for local DB
export function getUserByEmail(email: string) {
  if (!db) throw new Error('Database not initialized');
  const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
  return stmt.get(email);
}

export function createUser(user: { id: string; email: string; name: string; role: string; lastSync: number }) {
  if (!db) throw new Error('Database not initialized');

  // First, check if a user with this email already exists
  const existingUserByEmail = getUserByEmail(user.email);

  if (existingUserByEmail) {
    // If user with this email exists, update their record using their existing ID
    const stmt = db.prepare(`
      UPDATE users SET
        id = ?, name = ?, role = ?, last_sync = ?
      WHERE email = ?
    `);
    return stmt.run(user.id, user.name, user.role, user.lastSync, user.email);
  } else {
    // If no user with this email, check by ID (for cases where ID might change or be new)
    const existingUserById = db.prepare('SELECT id FROM users WHERE id = ?').get(user.id);

    if (existingUserById) {
      // User exists by ID, update
      const stmt = db.prepare(`
        UPDATE users SET
          email = ?, name = ?, role = ?, last_sync = ?
        WHERE id = ?
      `);
      return stmt.run(user.email, user.name, user.role, user.lastSync, user.id);
    } else {
      // User does not exist by email or ID, insert new user
      const stmt = db.prepare(`
        INSERT INTO users (id, email, name, role, last_sync)
        VALUES (?, ?, ?, ?, ?)
      `);
      return stmt.run(user.id, user.email, user.name, user.role, user.lastSync);
    }
  }
}

export function getUserById(userId: string) {
  if (!db) throw new Error('Database not initialized');
  const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
  return stmt.get(userId);
}

export function updateTemplate(templateId: string, userId: string, updates: any) {
  if (!db) throw new Error('Database not initialized');
  // Only allow updating certain fields for security
  const allowedFields = ['name', 'type', 'content', 'file_name', 'file_size', 'mime_type', 'is_default', 'description', 'updated_at'];
  const setClauses: string[] = [];
  const values: any[] = [];
  for (const key of allowedFields) {
    if (updates[key] !== undefined) {
      setClauses.push(`${key} = ?`);
      values.push(updates[key]);
    }
  }
  if (setClauses.length === 0) throw new Error('No valid fields to update');
  values.push(templateId, userId);
  const sql = `UPDATE templates SET ${setClauses.join(', ')} WHERE id = ? AND user_id = ?`;
  return db.prepare(sql).run(...values);
}
