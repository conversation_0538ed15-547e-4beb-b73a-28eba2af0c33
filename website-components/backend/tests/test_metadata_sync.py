"""
Test metadata synchronization between graph_data.metadata and complaint model's metadata field.
This ensures dashboard and summary pages have access to the metadata.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from bson import ObjectId
import copy

# Import the functions we want to test
from services.graph_service import process_csv_data, sync_metadata_to_complaint
from services.complaint_service import get_complaint_graph_data


class TestMetadataSync:
    """Test class for metadata synchronization functionality"""

    def test_process_csv_data_includes_metadata(self):
        """Test that process_csv_data includes metadata in graph_data"""
        # Sample CSV data
        csv_data = """Layer,Fraud Type,Txn Type,Txn Date,Amount,Sender Account,Sender Bank,Sender Txn ID,Receiver Account,Receiver Bank,Txn ID
1,banking_upi,Money transfer,2024-01-01,1000,*********,SBI,TXN001,*********,HDFC,TXN002"""

        # Sample metadata
        metadata = {
            "complaint_number": "TEST-001",
            "complainant_name": "Test User",
            "date": "2024-01-01",
            "total_amount": "1000",
            "complainant_mobile": "**********",
            "complainant_email": "<EMAIL>"
        }

        # Process the CSV data
        transactions, graph_data = process_csv_data(csv_data, metadata)

        # Verify that graph_data contains metadata
        assert isinstance(graph_data, dict), "graph_data should be a dictionary"
        assert "metadata" in graph_data, "graph_data should contain metadata"

        # Verify that metadata is properly preserved
        graph_metadata = graph_data["metadata"]
        assert graph_metadata["complaint_number"] == "TEST-001"
        assert graph_metadata["complainant_name"] == "Test User"
        assert graph_metadata["complainant_mobile"] == "**********"
        assert graph_metadata["complainant_email"] == "<EMAIL>"

        print("✅ process_csv_data correctly includes metadata in graph_data")

    def test_sync_metadata_to_complaint(self):
        """Test the sync_metadata_to_complaint helper function"""
        # Mock database - using regular Mock since the function is synchronous
        mock_db = MagicMock()
        mock_result = MagicMock()
        mock_result.modified_count = 1
        mock_db.complaints.update_one.return_value = mock_result

        # Sample metadata
        metadata = {
            "complaint_number": "TEST-001",
            "complainant_name": "Test User",
            "total_amount": "1000"
        }

        # Test with ObjectId
        complaint_id = str(ObjectId())
        result = sync_metadata_to_complaint(complaint_id, metadata, mock_db)

        # Verify the function returns True for successful sync
        assert result == True, "sync_metadata_to_complaint should return True on success"

        # Verify that update_one was called with correct parameters
        mock_db.complaints.update_one.assert_called_once()
        call_args = mock_db.complaints.update_one.call_args

        # Check the query (first argument)
        query = call_args[0][0]
        assert "_id" in query, "Query should contain _id field"

        # Check the update data (second argument)
        update_data = call_args[0][1]
        assert "$set" in update_data, "Update should use $set operator"
        assert "metadata" in update_data["$set"], "Update should set metadata field"

        updated_metadata = update_data["$set"]["metadata"]
        assert updated_metadata["complaint_number"] == "TEST-001"
        assert updated_metadata["complainant_name"] == "Test User"

        print("✅ sync_metadata_to_complaint works correctly")

    def test_metadata_deep_copy(self):
        """Test that metadata is properly deep copied to avoid reference issues"""
        original_metadata = {
            "complaint_number": "TEST-001",
            "complainant_name": "Test User",
            "address": {
                "house_no": "123",
                "street": "Main St",
                "city": "Test City"
            }
        }

        # Process CSV data with metadata
        csv_data = """Layer,Fraud Type,Txn Type,Txn Date,Amount,Sender Account,Sender Bank,Sender Txn ID,Receiver Account,Receiver Bank,Txn ID
1,banking_upi,Money transfer,2024-01-01,1000,*********,SBI,TXN001,*********,HDFC,TXN002"""

        transactions, graph_data = process_csv_data(csv_data, original_metadata)

        # Modify the original metadata
        original_metadata["complainant_name"] = "Modified Name"
        original_metadata["address"]["city"] = "Modified City"

        # Verify that graph_data metadata is not affected
        graph_metadata = graph_data["metadata"]
        assert graph_metadata["complainant_name"] == "Test User", "Metadata should be deep copied"
        assert graph_metadata["address"]["city"] == "Test City", "Nested objects should be deep copied"

        print("✅ Metadata is properly deep copied")

    def test_metadata_with_empty_fields(self):
        """Test metadata handling with empty or missing fields"""
        # Test with minimal metadata
        minimal_metadata = {
            "complaint_number": "TEST-002"
        }

        csv_data = """Layer,Fraud Type,Txn Type,Txn Date,Amount,Sender Account,Sender Bank,Sender Txn ID,Receiver Account,Receiver Bank,Txn ID
1,banking_upi,Money transfer,2024-01-01,1000,*********,SBI,TXN001,*********,HDFC,TXN002"""

        transactions, graph_data = process_csv_data(csv_data, minimal_metadata)

        # Verify that graph_data contains metadata even with minimal input
        assert "metadata" in graph_data
        graph_metadata = graph_data["metadata"]
        assert graph_metadata["complaint_number"] == "TEST-002"

        # Check what fields are actually present
        print(f"Graph metadata keys: {list(graph_metadata.keys())}")

        # The address field might not be created for minimal metadata, which is fine
        # Just verify that the provided metadata is preserved
        assert graph_metadata["complaint_number"] == "TEST-002"

        print("✅ Metadata handling works with minimal input")

    def test_default_metadata_creation(self):
        """Test that default metadata is created when none is provided"""
        csv_data = """Layer,Fraud Type,Txn Type,Txn Date,Amount,Sender Account,Sender Bank,Sender Txn ID,Receiver Account,Receiver Bank,Txn ID
1,banking_upi,Money transfer,2024-01-01,1000,*********,SBI,TXN001,*********,HDFC,TXN002"""

        # Process CSV data without metadata
        transactions, graph_data = process_csv_data(csv_data, None)

        # Verify that default metadata is created
        assert "metadata" in graph_data
        graph_metadata = graph_data["metadata"]

        # Check default values
        assert graph_metadata["complainant_name"] == "CSV Import"
        assert graph_metadata["complaint_number"] == "CSV-IMPORT"
        assert "address" in graph_metadata
        assert isinstance(graph_metadata["address"], dict)

        print("✅ Default metadata is created when none provided")


if __name__ == "__main__":
    # Run the tests
    test_instance = TestMetadataSync()

    print("Running metadata synchronization tests...\n")

    # Run synchronous tests
    test_instance.test_process_csv_data_includes_metadata()
    test_instance.test_sync_metadata_to_complaint()
    test_instance.test_metadata_deep_copy()
    test_instance.test_metadata_with_empty_fields()
    test_instance.test_default_metadata_creation()

    print("\n🎉 All metadata synchronization tests passed!")
    print("\nMetadata sync functionality is working correctly:")
    print("✅ Metadata is stored in both graph_data.metadata and complaint.metadata")
    print("✅ Deep copying prevents reference issues")
    print("✅ Default metadata is created when needed")
    print("✅ Sync function properly updates complaint metadata field")
