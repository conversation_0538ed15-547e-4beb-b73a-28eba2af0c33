"""
CORS utility functions for adding CORS headers to responses.
"""
import os
import logging
from starlette.responses import Response
from starlette.requests import Request

# Setup logging
logger = logging.getLogger(__name__)

# Get allowed origins from environment
FRONTEND_ORIGIN = os.getenv("FRONTEND_ORIGIN", "http://localhost:3001")

# In development mode, allow more origins for easier debugging
if os.getenv("ENVIRONMENT", "development") == "development":
    # Allow multiple origins in development mode
    ALLOWED_ORIGINS = [
        FRONTEND_ORIGIN,
        "http://127.0.0.1:3001",
        "http://localhost:3001",
        "http://localhost:8080",  # Common webpack port
        "http://127.0.0.1:8080"
    ]
else:
    ALLOWED_ORIGINS = [FRONTEND_ORIGIN]

def add_cors_headers(response: Response, request: Request) -> Response:
    """
    Add CORS headers to a response.

    Args:
        response: The response to add headers to
        request: The request that triggered the response

    Returns:
        The response with CORS headers added
    """
    origin = request.headers.get("Origin")
    if origin:
        # Check if the origin is in the allowed origins list
        if origin in ALLOWED_ORIGINS or "*" in ALLOWED_ORIGINS:
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Expose-Headers"] = "Content-Type, X-CSRF-Token, Authorization, X-Requested-With"
            
            # Add preflight headers if this is an OPTIONS request
            if request.method == "OPTIONS":
                response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
                response.headers["Access-Control-Allow-Headers"] = "*"
                response.headers["Access-Control-Max-Age"] = "600"  # 10 minutes
    
    return response
