"""
Password validation utilities for enforcing password complexity requirements.
"""
import re
import logging
from typing import List, Tuple

# Setup logging
logger = logging.getLogger(__name__)

# Password complexity requirements
MIN_PASSWORD_LENGTH = 8
REQUIRE_UPPERCASE = True
REQUIRE_LOWERCASE = True
REQUIRE_DIGITS = True
REQUIRE_SPECIAL_CHARS = True
SPECIAL_CHARS = r"[!@#$%^&*(),.?\":{}|<>]"

# Common password list (abbreviated)
COMMON_PASSWORDS = {
    "password", "123456", "12345678", "qwerty", "admin", "welcome",
    "1234", "12345", "password123", "abc123", "letmein", "monkey",
    "1234567", "123456789", "password1", "sunshine", "iloveyou"
}

def validate_password(password: str) -> Tuple[bool, List[str]]:
    """
    Validate a password against complexity requirements.
    
    Args:
        password: The password to validate
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Check length
    if len(password) < MIN_PASSWORD_LENGTH:
        errors.append(f"Password must be at least {MIN_PASSWORD_LENGTH} characters long")
    
    # Check for uppercase letters
    if REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")
    
    # Check for lowercase letters
    if REQUIRE_LOWERCASE and not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")
    
    # Check for digits
    if REQUIRE_DIGITS and not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one digit")
    
    # Check for special characters
    if REQUIRE_SPECIAL_CHARS and not re.search(SPECIAL_CHARS, password):
        errors.append("Password must contain at least one special character")
    
    # Check for common passwords
    if password.lower() in COMMON_PASSWORDS:
        errors.append("Password is too common and easily guessable")
    
    # Check for sequential characters
    if has_sequential_chars(password, 3):
        errors.append("Password contains sequential characters")
    
    # Check for repeated characters
    if has_repeated_chars(password, 3):
        errors.append("Password contains too many repeated characters")
    
    return len(errors) == 0, errors

def has_sequential_chars(password: str, length: int) -> bool:
    """
    Check if password has sequential characters.
    
    Args:
        password: The password to check
        length: The length of the sequence to check
        
    Returns:
        bool: True if sequential characters found
    """
    # Check for sequential letters
    for i in range(len(password) - length + 1):
        sequence = password[i:i+length].lower()
        # Check if alphabetically sequential
        if all(ord(sequence[j+1]) - ord(sequence[j]) == 1 for j in range(length-1)):
            return True
    
    # Check for sequential digits
    for i in range(len(password) - length + 1):
        sequence = password[i:i+length]
        if sequence.isdigit():
            if all(int(sequence[j+1]) - int(sequence[j]) == 1 for j in range(length-1)):
                return True
    
    return False

def has_repeated_chars(password: str, length: int) -> bool:
    """
    Check if password has repeated characters.
    
    Args:
        password: The password to check
        length: The number of repetitions to check
        
    Returns:
        bool: True if repeated characters found
    """
    for i in range(len(password) - length + 1):
        if all(password[i] == password[i+j] for j in range(1, length)):
            return True
    return False

def get_password_strength(password: str) -> int:
    """
    Calculate password strength score (0-100).
    
    Args:
        password: The password to evaluate
        
    Returns:
        int: Strength score from 0 (weakest) to 100 (strongest)
    """
    score = 0
    
    # Length contribution (up to 25 points)
    length_score = min(25, len(password) * 2)
    score += length_score
    
    # Character variety (up to 35 points)
    if any(c.isupper() for c in password):
        score += 10
    if any(c.islower() for c in password):
        score += 10
    if any(c.isdigit() for c in password):
        score += 5
    if re.search(SPECIAL_CHARS, password):
        score += 10
    
    # Deductions for patterns
    if has_sequential_chars(password, 3):
        score -= 10
    if has_repeated_chars(password, 3):
        score -= 10
    if password.lower() in COMMON_PASSWORDS:
        score -= 20
    
    # Ensure score is between 0 and 100
    return max(0, min(100, score))
