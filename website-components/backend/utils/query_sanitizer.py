import re
import logging
from typing import Any, Dict, List, Union, Optional

# Setup logging
logger = logging.getLogger(__name__)

# List of allowed MongoDB operators
ALLOWED_OPERATORS = {
    # Query operators
    '$eq', '$gt', '$gte', '$in', '$lt', '$lte', '$ne', '$nin',
    '$and', '$not', '$nor', '$or',
    '$exists', '$type',
    '$regex', '$options',
    '$all', '$elemMatch', '$size',

    # Update operators
    '$set', '$unset', '$inc', '$push', '$pull', '$addToSet',

    # Aggregation operators
    '$match', '$project', '$group', '$sort', '$limit', '$skip',
    '$lookup', '$unwind', '$count', '$sum', '$avg', '$min', '$max',

    # Other common operators
    '$text', '$search', '$meta'
}

def sanitize_query(query: Union[Dict[str, Any], List[Any], None]) -> Union[Dict[str, Any], List[Any], None]:
    """
    Sanitize MongoDB query to prevent NoSQL injection attacks.

    This function recursively processes query dictionaries and lists to ensure they don't contain
    potentially dangerous operators or patterns.

    Args:
        query: The MongoDB query to sanitize

    Returns:
        The sanitized query
    """
    if query is None:
        return None

    # Handle list of queries (e.g., for aggregate pipeline)
    if isinstance(query, list):
        return [sanitize_query(item) for item in query]

    # Handle dictionary queries
    if not isinstance(query, dict):
        return query

    sanitized_query = {}

    for key, value in query.items():
        # Check for MongoDB operator keys that could be used for injection
        if key.startswith('$') and key not in ALLOWED_OPERATORS:
            logger.warning(f"Potentially dangerous MongoDB operator removed: {key}")
            continue

        # Recursively sanitize nested dictionaries and lists
        if isinstance(value, dict):
            sanitized_query[key] = sanitize_query(value)
        elif isinstance(value, list):
            sanitized_query[key] = [
                sanitize_query(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            # Check for JavaScript code in string values
            if isinstance(value, str) and contains_js_code(value):
                logger.warning(f"Potentially dangerous JavaScript code removed from query value")
                sanitized_query[key] = sanitize_string(value)
            else:
                sanitized_query[key] = value

    return sanitized_query

def contains_js_code(value: str) -> bool:
    """
    Check if a string contains potential JavaScript code.

    Args:
        value: The string to check

    Returns:
        bool: True if the string contains potential JavaScript code
    """
    # Patterns that might indicate JavaScript code
    js_patterns = [
        r'function\s*\(',  # function declarations
        r'=>',             # arrow functions
        r'eval\s*\(',      # eval calls
        r'new\s+Function', # Function constructor
        r'\$where',        # $where operator
        r'javascript:',    # javascript: protocol
        r'setTimeout',     # setTimeout function
        r'setInterval',    # setInterval function
        r'__proto__',      # prototype pollution
        r'constructor\s*\(' # constructor calls
    ]

    for pattern in js_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            return True

    return False

def sanitize_string(value: str) -> str:
    """
    Sanitize a string by removing potential JavaScript code.

    Args:
        value: The string to sanitize

    Returns:
        The sanitized string
    """
    # Replace JavaScript patterns with empty strings
    js_patterns = [
        r'function\s*\([^)]*\)\s*{[^}]*}',
        r'=>',
        r'eval\s*\([^)]*\)',
        r'new\s+Function\s*\([^)]*\)',
        r'\$where\s*:',
        r'javascript:',
        r'setTimeout\s*\([^)]*\)',
        r'setInterval\s*\([^)]*\)',
        r'__proto__',
        r'constructor\s*\([^)]*\)'
    ]

    result = value
    for pattern in js_patterns:
        result = re.sub(pattern, '', result, flags=re.IGNORECASE)

    return result



def sanitize_mongodb_id(id_value: Any) -> Optional[str]:
    """
    Sanitize a MongoDB ID value to ensure it's a valid string format.

    Args:
        id_value: The ID value to sanitize

    Returns:
        The sanitized ID as a string, or None if invalid
    """
    if id_value is None:
        return None

    # Convert to string if it's not already
    id_str = str(id_value)

    # Check if it's a valid MongoDB ObjectId format (24 hex characters)
    if re.match(r'^[0-9a-fA-F]{24}$', id_str):
        return id_str

    # Log warning for invalid ID format
    logger.warning(f"Invalid MongoDB ID format: {id_str}")
    return None
