"""
Custom JSON encoders for MongoDB ObjectId and other types.
"""
from bson import ObjectId
from fastapi.encoders import jsonable_encoder

# Original jsonable_encoder function from FastAPI
original_jsonable_encoder = jsonable_encoder

def custom_jsonable_encoder(*args, **kwargs):
    """
    Custom JSON encoder that handles MongoDB ObjectId.
    This wraps FastAPI's jsonable_encoder to add support for ObjectId.
    """
    # Add custom encoder for ObjectId
    custom_encoder = kwargs.get("custom_encoder", {})
    custom_encoder[ObjectId] = str
    kwargs["custom_encoder"] = custom_encoder
    
    # Call the original encoder
    return original_jsonable_encoder(*args, **kwargs)
