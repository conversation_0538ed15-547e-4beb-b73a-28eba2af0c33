import os
import logging
import logging.handlers
import time
from datetime import datetime
from pathlib import Path

def setup_log_rotation(log_dir: str, max_bytes: int = 10485760, backup_count: int = 10):
    """
    Set up log rotation to prevent logs from consuming too much disk space.

    Args:
        log_dir: Directory where log files are stored
        max_bytes: Maximum size of each log file in bytes (default: 10MB)
        backup_count: Number of backup files to keep (default: 10)
    """
    # Create log directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)

    # Get the root logger
    root_logger = logging.getLogger()

    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create rotating file handlers
    main_log_path = os.path.join(log_dir, 'app.log')
    error_log_path = os.path.join(log_dir, 'error.log')

    # Main log with rotation
    main_handler = logging.handlers.RotatingFileHandler(
        main_log_path,
        maxBytes=max_bytes,
        backupCount=backup_count
    )

    # Error log with rotation
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_path,
        maxBytes=max_bytes,
        backupCount=backup_count
    )
    error_handler.setLevel(logging.ERROR)

    # Add formatters
    if os.getenv("ENVIRONMENT", "development") == "production":
        # JSON format for production
        formatter = logging.Formatter('%(message)s')
    else:
        # Readable format for development
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    main_handler.setFormatter(formatter)
    error_handler.setFormatter(formatter)

    # Add handlers to root logger
    root_logger.addHandler(main_handler)
    root_logger.addHandler(error_handler)

    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # Log rotation setup
    logging.info(f"Log rotation configured: max_bytes={max_bytes}, backup_count={backup_count}")

def cleanup_old_logs(log_dir: str, max_age_days: int = 30):
    """
    Clean up old log files that exceed the maximum age.

    Args:
        log_dir: Directory where log files are stored
        max_age_days: Maximum age of log files in days (default: 30)
    """
    try:
        # Get current time
        now = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60

        # Get all log files
        log_dir_path = Path(log_dir)
        log_files = list(log_dir_path.glob('*.log.*'))

        # Check each file
        for log_file in log_files:
            # Get file modification time
            file_mtime = log_file.stat().st_mtime

            # Check if file is older than max age
            if now - file_mtime > max_age_seconds:
                # Remove file
                log_file.unlink()
                logging.info(f"Removed old log file: {log_file}")
    except Exception as e:
        logging.error(f"Error cleaning up old logs: {str(e)}")

def get_log_stats(log_dir: str):
    """
    Get statistics about log files.

    Args:
        log_dir: Directory where log files are stored

    Returns:
        dict: Statistics about log files
    """
    try:
        log_dir_path = Path(log_dir)
        log_files = list(log_dir_path.glob('*.log*'))

        total_size = sum(f.stat().st_size for f in log_files)
        oldest_file = min(log_files, key=lambda f: f.stat().st_mtime, default=None)
        newest_file = max(log_files, key=lambda f: f.stat().st_mtime, default=None)

        oldest_time = datetime.fromtimestamp(oldest_file.stat().st_mtime).isoformat() if oldest_file else None
        newest_time = datetime.fromtimestamp(newest_file.stat().st_mtime).isoformat() if newest_file else None

        return {
            "total_files": len(log_files),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "oldest_file": str(oldest_file) if oldest_file else None,
            "oldest_time": oldest_time,
            "newest_file": str(newest_file) if newest_file else None,
            "newest_time": newest_time
        }
    except Exception as e:
        logging.error(f"Error getting log stats: {str(e)}")
        return {
            "error": str(e)
        }
