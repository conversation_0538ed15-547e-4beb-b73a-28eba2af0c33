from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import os
import logging
import secrets
import time
from typing import Dict

# Setup logging
logger = logging.getLogger(__name__)

# Cache for nonce values with TTL
NONCE_CACHE: Dict[str, float] = {}

def generate_nonce() -> str:
    """Generate a secure nonce value for CSP"""
    return secrets.token_hex(16)

def get_request_nonce(request_id: str) -> str:
    """
    Get or create a nonce for a specific request

    Args:
        request_id: Unique identifier for the request

    Returns:
        str: The nonce value
    """
    # Clean up expired nonces (older than 5 minutes)
    current_time = time.time()
    expired_keys = [k for k, v in NONCE_CACHE.items() if current_time - v > 300]
    for key in expired_keys:
        NONCE_CACHE.pop(key, None)

    # Generate new nonce if needed
    if request_id not in NONCE_CACHE:
        NONCE_CACHE[request_id] = current_time
        request_id = generate_nonce()

    return request_id

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Generate a unique request ID and nonce for this request
        request_id = request.headers.get("X-Request-ID", generate_nonce())
        nonce = get_request_nonce(request_id)

        # Store nonce in request state for use in templates
        request.state.csp_nonce = nonce

        # Process the request
        response = await call_next(request)

        # Add enhanced security headers for production with 500 users
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=63072000; includeSubDomains; preload"  # 2 years
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["X-Permitted-Cross-Domain-Policies"] = "none"
        response.headers["X-DNS-Prefetch-Control"] = "off"

        # Add security headers for preventing clickjacking and MIME sniffing
        response.headers["X-Download-Options"] = "noopen"  # Prevents IE from executing downloads

        # Content Security Policy with nonce support for production with 500 users
        # This is critical for preventing XSS attacks at scale
        # Updated to work with React and Vite development/production builds
        is_production = os.getenv("ENVIRONMENT") == "production"

        csp_policy = (
            f"default-src 'self'; "
            f"script-src 'self' 'nonce-{nonce}' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com; "
            f"style-src 'self' 'nonce-{nonce}' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com; "
            f"font-src 'self' https://fonts.googleapis.com https://fonts.gstatic.com data:; "
            f"img-src 'self' data: blob: https:; "
            f"connect-src 'self' https://api.ncrpmatrix.in https://app.ncrpmatrix.in wss: ws:; "
            f"media-src 'self' data: blob:; "
            f"object-src 'none'; "
            f"base-uri 'self'; "
            f"form-action 'self'; "
            f"frame-ancestors 'none'; "
            f"worker-src 'self' blob:; "
            f"child-src 'self' blob:; "
            f"manifest-src 'self'"
        )

        # Only add upgrade-insecure-requests in production
        if is_production:
            csp_policy += "; upgrade-insecure-requests"

        response.headers["Content-Security-Policy"] = csp_policy

        # Add Feature-Policy header (deprecated but still useful for older browsers)
        # Removed features duplicated in Permissions-Policy
        response.headers["Feature-Policy"] = (
            "accelerometer 'none'; "
            "autoplay 'none'; "
            "clipboard-read 'none'; "
            "clipboard-write 'none'; "
            "display-capture 'none'; "
            "encrypted-media 'none'; "
            "fullscreen 'none'; "
            "gamepad 'none'; "
            "gyroscope 'none'; "
            "hid 'none'; "
            "idle-detection 'none'; "
            "magnetometer 'none'; "
            "midi 'none'; "
            "picture-in-picture 'none'; "
            "publickey-credentials-get 'none'; "
            "screen-wake-lock 'none'; "
            "serial 'none'; "
            "sync-xhr 'none'; "
            "usb 'none'; "
            "xr-spatial-tracking 'none'"
        )

        # Add Cache-Control for API responses
        if request.url.path.startswith("/api") or request.url.path.startswith("/auth"):
            response.headers["Cache-Control"] = "no-store, max-age=0"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"

        # Add comprehensive Permissions-Policy header to limit browser features
        # This is critical for security with 500 users
        # Removed unrecognized features
        response.headers["Permissions-Policy"] = (
            "accelerometer=(), "
            "autoplay=(), "
            "camera=(), "
            "clipboard-read=(), "
            "clipboard-write=(), "
            "display-capture=(), "
            "encrypted-media=(), "
            "fullscreen=(), "
            "gamepad=(), "
            "geolocation=(), "
            "gyroscope=(), "
            "hid=(), "
            "idle-detection=(), "
            "magnetometer=(), "
            "microphone=(), "
            "midi=(), "
            "payment=(), "
            "picture-in-picture=(), "
            "publickey-credentials-get=(), "
            "screen-wake-lock=(), "
            "serial=(), "
            "sync-xhr=(), "
            "usb=(), "
            "xr-spatial-tracking=()"
        )

        # Add Cross-Origin headers for API responses
        if request.url.path.startswith("/api"):
            response.headers["Cross-Origin-Resource-Policy"] = "same-origin"
            response.headers["Cross-Origin-Opener-Policy"] = "same-origin"
            response.headers["Cross-Origin-Embedder-Policy"] = "require-corp"

        return response
