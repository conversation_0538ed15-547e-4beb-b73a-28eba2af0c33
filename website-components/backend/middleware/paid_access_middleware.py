from fastapi import Request, HTTPException
from services.auth_service import get_current_user
from datetime import datetime, timezone
from database import get_db
import logging

logger = logging.getLogger(__name__)

async def check_subscription_status(request: Request):
    """
    Comprehensive subscription status checker.
    Checks if user has active subscription and updates expired subscriptions.
    """
    try:
        # Get current user
        user = await get_current_user(request)

        # Check subscription expiration
        now = datetime.now(timezone.utc)
        subscription_expires = user.get("subscription_expires")
        is_paid = user.get("paid", False)

        # Convert subscription_expires to timezone-aware datetime if it's naive
        if subscription_expires and isinstance(subscription_expires, datetime) and subscription_expires.tzinfo is None:
            subscription_expires = subscription_expires.replace(tzinfo=timezone.utc)

        # If user has paid status but subscription has expired, update it
        if is_paid and subscription_expires and subscription_expires < now:
            logger.info(f"User {user.get('email', 'unknown')} subscription has expired. Updating status.")

            # Get database connection
            db = get_db()
            if db is not None:
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": {"paid": False}}
                )

            # Update user object to reflect the change
            user["paid"] = False
            is_paid = False

        # Check if subscription is expired (with timezone-aware comparison)
        is_expired = False
        if is_paid and subscription_expires:
            # Ensure subscription_expires is timezone-aware for comparison
            if isinstance(subscription_expires, datetime) and subscription_expires.tzinfo is None:
                subscription_expires = subscription_expires.replace(tzinfo=timezone.utc)
            is_expired = subscription_expires < now

        return {
            "user": user,
            "is_paid": is_paid,
            "subscription_expires": subscription_expires,
            "is_expired": is_expired
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error checking subscription status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during subscription check"
        )

async def check_paid_access(request: Request):
    """
    Dependency to check if a user has paid access.
    Use this as a dependency in routes that should be restricted to paid users.
    Now includes comprehensive subscription expiration handling.
    """
    try:
        # Check subscription status
        subscription_info = await check_subscription_status(request)
        user = subscription_info["user"]
        is_paid = subscription_info["is_paid"]

        # Check if user has paid access
        if not is_paid:
            logger.warning(f"Unpaid user {user.get('email', 'unknown')} attempted to access paid feature")

            # Provide structured error response for better frontend handling
            error_detail = {
                "message": "Your subscription has expired. Please renew to continue using this feature.",
                "error_type": "subscription_expired",
                "user_friendly": True,
                "retryable": False,
                "subscription_status": {
                    "is_paid": is_paid,
                    "expires": subscription_info.get("subscription_expires"),
                    "is_expired": True
                }
            }

            raise HTTPException(
                status_code=403,
                detail=error_detail
            )

        return user
    except HTTPException as e:
        # Re-raise authentication errors with proper status code
        if e.status_code == 401:
            logger.warning(f"Authentication failed for paid access check: {e.detail}")
            raise HTTPException(
                status_code=401,
                detail="Authentication required for this feature",
                headers={"WWW-Authenticate": "Bearer"}
            )
        # Re-raise other HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in paid access check: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during access check"
        )
