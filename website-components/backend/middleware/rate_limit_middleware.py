from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import time
import logging
import os
import re
from collections import defaultdict
from typing import Tuple

# Setup logging
logger = logging.getLogger(__name__)

# Define endpoint categories for more granular rate limiting
# Optimized for production with 500 users
ENDPOINT_CATEGORIES = {
    # Authentication endpoints with adjusted limits for 500 users
    'auth_login': {
        'pattern': r'^/auth/login$',
        'limit': int(os.getenv("AUTH_LOGIN_RATE_LIMIT", "10")),    # 10 requests per minute (increased for 500 users)
        'window': int(os.getenv("AUTH_LOGIN_WINDOW", "60")),       # 1 minute window
        'per_user': True,                                          # Apply limit per user (email) when possible
    },
    'auth_register': {
        'pattern': r'^/auth/register$',
        'limit': int(os.getenv("AUTH_REGISTER_RATE_LIMIT", "5")),  # 5 requests per minute (increased for 500 users)
        'window': int(os.getenv("AUTH_REGISTER_WINDOW", "300")),   # 5 minute window
        'per_user': False,                                         # Apply limit per IP (not per user)
    },
    'auth_password_reset': {
        'pattern': r'^/auth/forgot-password$|^/auth/reset-password$',
        'limit': int(os.getenv("AUTH_RESET_RATE_LIMIT", "5")),     # 5 requests per 5 minutes (increased for 500 users)
        'window': int(os.getenv("AUTH_RESET_WINDOW", "300")),      # 5 minute window
        'per_user': True,                                          # Apply limit per user (email) when possible
    },
    'auth_verify': {
        'pattern': r'^/auth/verify-otp$|^/auth/resend-otp$',
        'limit': int(os.getenv("AUTH_VERIFY_RATE_LIMIT", "10")),   # 10 requests per minute (increased for 500 users)
        'window': int(os.getenv("AUTH_VERIFY_WINDOW", "60")),      # 1 minute window
        'per_user': True,                                          # Apply limit per user (email) when possible
    },
    'auth_other': {
        'pattern': r'^/auth/',
        'limit': int(os.getenv("AUTH_OTHER_RATE_LIMIT", "30")),    # 30 requests per minute (increased for 500 users)
        'window': int(os.getenv("AUTH_OTHER_WINDOW", "60")),       # 1 minute window
        'per_user': False,                                         # Apply limit per IP
    },
    # API endpoints with optimized limits for 500 users
    'api_read': {
        'pattern': r'^/complaints$|^/complaints/[^/]+$|^/templates$',
        'limit': int(os.getenv("API_READ_RATE_LIMIT", "180")),     # 180 requests per minute (increased for 500 users)
        'window': int(os.getenv("API_READ_WINDOW", "60")),         # 1 minute window
        'per_user': True,                                          # Apply limit per user when authenticated
    },
    'api_write': {
        'pattern': r'^/complaints/create$|^/extraction/parse-csv$',
        'limit': int(os.getenv("API_WRITE_RATE_LIMIT", "60")),     # 60 requests per minute (increased for 500 users)
        'window': int(os.getenv("API_WRITE_WINDOW", "60")),        # 1 minute window
        'per_user': True,                                          # Apply limit per user when authenticated
    },
    # Add specific endpoint for CSV processing which is resource-intensive
    'csv_processing': {
        'pattern': r'^/extraction/parse-csv$',
        'limit': int(os.getenv("CSV_PROCESSING_RATE_LIMIT", "15")), # 15 requests per minute (increased but still limited due to resource intensity)
        'window': int(os.getenv("CSV_PROCESSING_WINDOW", "60")),    # 1 minute window
        'per_user': True,                                           # Apply limit per user when authenticated
    },
    # Notice generation endpoints with optimized limits for batch processing
    'notice_single': {
        'pattern': r'^/notices/generate/[^/]+$|^/notices/download/[^/]+$',
        'limit': int(os.getenv("NOTICE_SINGLE_RATE_LIMIT", "30")),  # 30 requests per minute (optimized for 500 users)
        'window': int(os.getenv("NOTICE_SINGLE_WINDOW", "60")),     # 1 minute window
        'per_user': True,                                           # Apply limit per user when authenticated
    },
    'notice_batch': {
        'pattern': r'^/notices/generate-batch/[^/]+$',
        'limit': int(os.getenv("NOTICE_BATCH_RATE_LIMIT", "10")),   # 10 requests per minute (optimized for batch processing)
        'window': int(os.getenv("NOTICE_BATCH_WINDOW", "60")),      # 1 minute window
        'per_user': True,                                           # Apply limit per user when authenticated
    },
    # Data upload endpoints with optimized limits
    'data_upload': {
        'pattern': r'^/upload/nodal-data$|^/upload/atm-data$',
        'limit': int(os.getenv("DATA_UPLOAD_RATE_LIMIT", "5")),     # 5 requests per minute (limited due to resource intensity)
        'window': int(os.getenv("DATA_UPLOAD_WINDOW", "300")),      # 5 minute window
        'per_user': True,                                           # Apply limit per user when authenticated
    },
}

class RateLimitMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app,
        general_rate_limit=100,  # Default: 100 requests per minute
        auth_rate_limit=20,      # Default: 20 requests per minute for auth endpoints
        window_seconds=60,       # Default: 1 minute window
        enable_ip_blocking=True, # Default: Enable IP blocking for severe violations
        block_duration=1800      # Default: 30 minutes block duration
    ):
        super().__init__(app)
        # Use environment variables if available, otherwise use defaults
        self.general_rate_limit = int(os.getenv("GENERAL_RATE_LIMIT", general_rate_limit))
        self.auth_rate_limit = int(os.getenv("AUTH_RATE_LIMIT", auth_rate_limit))
        self.window_seconds = int(os.getenv("RATE_LIMIT_WINDOW", window_seconds))
        self.enable_ip_blocking = os.getenv("ENABLE_IP_BLOCKING", str(enable_ip_blocking)).lower() == "true"
        self.block_duration = int(os.getenv("BLOCK_DURATION", block_duration))

        # Compile regex patterns for endpoint categories
        self.endpoint_categories = {}
        for category, config in ENDPOINT_CATEGORIES.items():
            self.endpoint_categories[category] = {
                'pattern': re.compile(config['pattern']),
                'limit': config['limit'],
                'window': config['window']
            }

        # Store request timestamps by IP and endpoint category
        self.requests = defaultdict(list)

        # Track IP addresses with suspicious activity
        self.suspicious_ips = {}

        # Last cleanup time
        self.last_cleanup = time.time()

        logger.info(f"Enhanced rate limit middleware initialized with {len(self.endpoint_categories)} endpoint categories")
        logger.info(f"Default limits: {self.general_rate_limit} general requests and {self.auth_rate_limit} auth requests per {self.window_seconds} seconds")

    async def dispatch(self, request: Request, call_next):
        # Skip rate limiting in development mode if configured
        if os.getenv("DISABLE_RATE_LIMIT", "false").lower() == "true" and \
           os.getenv("ENVIRONMENT", "development") == "development":
            return await call_next(request)

        # Get client IP with fallbacks for proxies
        client_ip = self._get_client_ip(request)

        # Get current time
        now = time.time()

        # Periodically clean up old requests (every 5 minutes)
        if now - self.last_cleanup > 300:  # 5 minutes
            self._cleanup_old_requests(now)
            self.last_cleanup = now

        # Check if IP is in suspicious list with active ban
        if client_ip in self.suspicious_ips:
            ban_until, reason = self.suspicious_ips[client_ip]
            if now < ban_until:
                # IP is currently banned
                ban_remaining = int(ban_until - now)
                logger.warning(f"Blocked request from banned IP {client_ip}: {reason}. Ban remaining: {ban_remaining}s")
                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": "Too many requests. Please try again later.",
                        "success": False,
                        "retry_after": ban_remaining
                    },
                    headers={"Retry-After": str(ban_remaining)}
                )
            else:
                # Ban expired, remove from suspicious list
                del self.suspicious_ips[client_ip]

        # Determine endpoint category and get rate limit
        path = request.url.path
        category, rate_limit, window, per_user = self._get_endpoint_category(path)

        # Try to get user identifier for per-user rate limiting
        user_id = None
        if per_user:
            # For auth endpoints with email in body
            if category.startswith('auth_') and request.method == "POST":
                try:
                    # Try to get request body without consuming it
                    body = await request.body()
                    request._body = body  # Put the body back for later consumption

                    # Try to parse JSON body
                    try:
                        json_body = await request.json()
                        user_id = json_body.get('email') or json_body.get('username')
                    except:
                        # If not JSON, try form data
                        form_data = await request.form()
                        user_id = form_data.get('email') or form_data.get('username')
                except:
                    # If we can't parse the body, fall back to IP-based limiting
                    pass

            # For authenticated endpoints, try to get user from token
            if not user_id:
                # Try to get token from cookies first
                token = request.cookies.get("access_token")

                # If not in cookies, try Authorization header (for backward compatibility)
                if not token:
                    auth_header = request.headers.get("Authorization")
                    if auth_header and auth_header.startswith("Bearer "):
                        token = auth_header.replace("Bearer ", "")

                if token:
                    try:
                        # Try to decode JWT to get user email
                        from services.auth_service import SECRET_KEY, ALGORITHM
                        import jwt
                        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                        user_id = payload.get("sub")  # sub contains user email
                    except:
                        # If token is invalid, fall back to IP-based limiting
                        pass

        # Create a unique key for rate limiting
        if user_id and per_user:
            # Use user identifier for per-user rate limiting
            key = f"user:{user_id}:{category}"
            logger.debug(f"Using per-user rate limiting for {user_id} on {category}")
        else:
            # Fall back to IP-based rate limiting
            key = f"ip:{client_ip}:{category}"
            logger.debug(f"Using IP-based rate limiting for {client_ip} on {category}")

        # Get request times for this key and endpoint category
        requests_list = self.requests.get(key, [])

        # Remove requests outside the window
        requests_list = [t for t in requests_list if now - t < window]

        # Check if rate limit exceeded
        if len(requests_list) >= rate_limit:
            # Calculate ban duration based on violation severity
            ban_duration = self._calculate_ban_duration(len(requests_list), rate_limit)

            if ban_duration > 0:
                # Add to suspicious IPs with ban
                self.suspicious_ips[client_ip] = (now + ban_duration, f"Rate limit exceeded on {category}")

                if user_id and per_user:
                    logger.warning(
                        f"Rate limit severely exceeded for user {user_id} on {category} endpoint: "
                        f"{len(requests_list)} requests in {window} seconds. IP {client_ip} banned for {ban_duration} seconds."
                    )
                else:
                    logger.warning(
                        f"Rate limit severely exceeded for IP {client_ip} on {category} endpoint: "
                        f"{len(requests_list)} requests in {window} seconds. Banned for {ban_duration} seconds."
                    )
            else:
                if user_id and per_user:
                    logger.warning(
                        f"Rate limit exceeded for user {user_id} on {category} endpoint: "
                        f"{len(requests_list)} requests in {window} seconds"
                    )
                else:
                    logger.warning(
                        f"Rate limit exceeded for IP {client_ip} on {category} endpoint: "
                        f"{len(requests_list)} requests in {window} seconds"
                    )

            # Return 429 Too Many Requests
            retry_after = ban_duration if ban_duration > 0 else window
            return JSONResponse(
                status_code=429,
                content={
                    "detail": "Too many requests. Please try again later.",
                    "success": False,
                    "retry_after": retry_after
                },
                headers={"Retry-After": str(retry_after)}
            )

        # Add current request time
        requests_list.append(now)
        self.requests[key] = requests_list

        # Process request
        return await call_next(request)

    def _get_endpoint_category(self, path: str) -> Tuple[str, int, int, bool]:
        """Determine the endpoint category and its rate limit

        Returns:
            Tuple containing:
            - category name (str)
            - rate limit (int)
            - window in seconds (int)
            - whether to apply per-user limiting (bool)
        """
        # Check each category pattern
        for category, config in self.endpoint_categories.items():
            if config['pattern'].search(path):
                return category, config['limit'], config['window'], config.get('per_user', False)

        # Default to general category
        is_auth_endpoint = path.startswith("/auth")
        if is_auth_endpoint:
            return "auth_other", self.auth_rate_limit, self.window_seconds, False
        else:
            return "general", self.general_rate_limit, self.window_seconds, False

    def _calculate_ban_duration(self, requests: int, limit: int) -> int:
        """
        Calculate ban duration based on violation severity

        Enhanced for production with 500 users:
        - Uses configurable block duration
        - Respects enable_ip_blocking setting
        - More progressive scaling based on violation severity
        """
        # If IP blocking is disabled, return 0 (no ban)
        if not self.enable_ip_blocking:
            return 0

        # No ban for minor violations
        if requests < limit * 1.5:
            return 0

        # Progressive ban duration based on severity
        base_duration = self.block_duration  # Default 30 minutes (1800 seconds)

        if requests < limit * 2:
            return int(base_duration * 0.1)  # 10% of base duration for moderate violations
        elif requests < limit * 3:
            return int(base_duration * 0.5)  # 50% of base duration for serious violations
        elif requests < limit * 5:
            return base_duration  # Full base duration for severe violations
        else:
            return base_duration * 2  # Double base duration for extreme violations

    def _get_client_ip(self, request: Request) -> str:
        """Get the client IP address with proxy support"""
        # Check for X-Forwarded-For header (common with proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Get the first IP in the chain (client IP)
            return forwarded_for.split(",")[0].strip()

        # Check for X-Real-IP header (used by some proxies)
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"

    def _cleanup_old_requests(self, now: float) -> None:
        """Clean up old request records to prevent memory leaks"""
        # Clean up old requests
        for key in list(self.requests.keys()):
            # Get category from key
            _, category = key.split(":", 1)

            # Get window for this category
            window = self.window_seconds
            for cat_name, config in self.endpoint_categories.items():
                if cat_name == category:
                    window = config['window']
                    break

            # Calculate cutoff time
            cutoff = now - window

            # Filter out old requests
            self.requests[key] = [t for t in self.requests[key] if t >= cutoff]

            # Remove empty lists
            if not self.requests[key]:
                del self.requests[key]

        # Clean up expired bans
        for ip in list(self.suspicious_ips.keys()):
            ban_until, _ = self.suspicious_ips[ip]
            if now > ban_until:
                del self.suspicious_ips[ip]

        logger.debug(f"Cleaned up rate limit tracking. Current tracked IPs: {len(self.requests)}, Banned IPs: {len(self.suspicious_ips)}")
