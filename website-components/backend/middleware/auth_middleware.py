from fastapi import Request, HTTPException
import logging
from functools import wraps
from services.auth_service import get_current_user

# Setup logging
logger = logging.getLogger(__name__)

# Public paths that don't require authentication
PUBLIC_PATHS = [
    "/health",
    "/auth/register",
    "/auth/login",
    "/docs",
    "/openapi.json",
    "/redoc"
]

async def is_authenticated(request: Request) -> bool:
    """
    Check if a request is authenticated

    Args:
        request: The request to check

    Returns:
        bool: True if authenticated, False otherwise
    """
    # Check if path is public
    if request.url.path in PUBLIC_PATHS:
        return True

    # Check for token in cookies
    has_cookie_token = "access_token" in request.cookies

    # Check for Authorization header (for backward compatibility)
    auth_header = request.headers.get("Authorization")
    has_auth_header = auth_header and auth_header.startswith("Bearer ")

    # If neither token source is available, not authenticated
    if not has_cookie_token and not has_auth_header:
        return False

    # Try to get current user
    try:
        await get_current_user(request)
        return True
    except HTTPException:
        return False

def requires_auth(func):
    """
    Decorator to require authentication for a route

    Args:
        func: The route function to protect

    Returns:
        Callable: The protected route function
    """
    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
        # Check if authenticated
        if not await is_authenticated(request):
            logger.warning(f"Unauthenticated access attempt to {request.url.path}")
            raise HTTPException(
                status_code=401,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"}
            )

        # Get current user
        user = await get_current_user(request)

        # Add user to request state
        request.state.user = user

        # Call original function
        return await func(request, *args, **kwargs)

    return wrapper
