#!/usr/bin/env python3
"""
Migration script to add subscription fields to existing users.
This script will:
1. Add subscription_start_date to users who don't have it
2. Ensure all users have proper subscription_expires field
3. Handle existing paid users appropriately
"""

import asyncio
import sys
import os
from datetime import datetime, timezone, timedelta

# Add the current directory to the path so we can import from backend
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database import get_db
    import logging
    print("✅ Successfully imported database module")
except ImportError as e:
    print(f"❌ Failed to import database module: {e}")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def migrate_subscription_fields():
    """Migrate existing users to include subscription fields"""
    try:
        # Get database connection
        db = get_db()
        if db is None:
            logger.error("Failed to connect to database")
            return False

        logger.info("Starting subscription fields migration...")

        # Find all users
        users_cursor = db.users.find({})
        users = await users_cursor.to_list(length=None)

        logger.info(f"Found {len(users)} users to process")

        updated_count = 0
        now = datetime.now(timezone.utc)

        for user in users:
            user_id = user["_id"]
            email = user.get("email", "unknown")

            # Prepare update fields
            update_fields = {}

            # Add subscription_start_date if missing
            if "subscription_start_date" not in user:
                # Use created_at if available, otherwise use current time
                start_date = user.get("created_at", now)
                update_fields["subscription_start_date"] = start_date
                logger.info(f"Adding subscription_start_date for user {email}")

            # Handle subscription_expires based on current paid status
            if user.get("paid", False):
                # User is currently paid
                if "subscription_expires" not in user or user.get("subscription_expires") is None:
                    # Paid user without expiration date - give them 1 year from now
                    expiry_date = now + timedelta(days=365)
                    update_fields["subscription_expires"] = expiry_date
                    logger.info(f"Adding 1-year subscription for paid user {email}")
                else:
                    # Check if subscription has expired
                    subscription_expires = user.get("subscription_expires")
                    if subscription_expires < now:
                        # Subscription has expired, set paid to false
                        update_fields["paid"] = False
                        logger.info(f"Marking expired subscription as unpaid for user {email}")
            else:
                # User is not paid
                if "subscription_expires" not in user:
                    # Add null subscription_expires for consistency
                    update_fields["subscription_expires"] = None
                    logger.info(f"Adding null subscription_expires for unpaid user {email}")

            # Apply updates if any
            if update_fields:
                await db.users.update_one(
                    {"_id": user_id},
                    {"$set": update_fields}
                )
                updated_count += 1
                logger.info(f"Updated user {email} with fields: {list(update_fields.keys())}")

        logger.info(f"Migration completed. Updated {updated_count} users.")
        return True

    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        return False

async def verify_migration():
    """Verify that the migration was successful"""
    try:
        db = get_db()
        if db is None:
            logger.error("Failed to connect to database")
            return False

        logger.info("Verifying migration...")

        # Count users with missing subscription_start_date
        missing_start_date = await db.users.count_documents({"subscription_start_date": {"$exists": False}})

        # Count paid users without subscription_expires
        paid_without_expires = await db.users.count_documents({
            "paid": True,
            "subscription_expires": {"$exists": False}
        })

        logger.info(f"Users missing subscription_start_date: {missing_start_date}")
        logger.info(f"Paid users missing subscription_expires: {paid_without_expires}")

        if missing_start_date == 0 and paid_without_expires == 0:
            logger.info("✅ Migration verification successful!")
            return True
        else:
            logger.warning("⚠️ Migration verification found issues")
            return False

    except Exception as e:
        logger.error(f"Error during verification: {str(e)}")
        return False

async def main():
    """Main migration function"""
    logger.info("🚀 Starting subscription fields migration...")

    # Run migration
    migration_success = await migrate_subscription_fields()

    if migration_success:
        # Verify migration
        verification_success = await verify_migration()

        if verification_success:
            logger.info("🎉 Migration completed successfully!")
            return 0
        else:
            logger.error("❌ Migration verification failed")
            return 1
    else:
        logger.error("❌ Migration failed")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
