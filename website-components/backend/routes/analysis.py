from fastapi import APIRouter, HTTPException, Depends, Body
import logging
from routes.auth import get_current_user
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from services.analysis_service import find_common_accounts
from utils.input_validator import validate_list
from utils.auth_utils import verify_resource_ownership
from middleware.paid_access_middleware import check_paid_access

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/common-accounts", dependencies=[Depends(check_paid_access)])
async def analyze_common_accounts(
    request: dict = Body(...),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Analyze multiple complaints to find common accounts.

    Args:
        request: Request body containing complaint_ids
        user: Current authenticated user
        db: Database connection

    Returns:
        Dictionary containing common accounts and their occurrences
    """
    # Extract complaint_ids from request body
    complaint_ids = request.get("complaint_ids", [])
    try:
        # Validate input
        is_valid, _, error_message = validate_list(complaint_ids)
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message or "Invalid complaint IDs format")

        if len(complaint_ids) < 2:
            raise HTTPException(status_code=400, detail="At least two complaints must be selected for analysis")

        if len(complaint_ids) > 10:
            raise HTTPException(status_code=400, detail="Maximum 10 complaints can be analyzed at once")

        user_id = str(user.get("_id", ""))
        logger.info(f"Analyzing {len(complaint_ids)} complaints for user {user['email']}")

        # Verify that all complaints belong to the user
        for complaint_id in complaint_ids:
            await verify_resource_ownership(
                resource_id=complaint_id,
                user_id=user_id,
                collection_name="complaints",
                db=db
            )

        # Find common accounts
        result = await find_common_accounts(complaint_ids, user_id, db)

        return {
            "success": True,
            "data": result
        }
    except HTTPException as e:
        # Re-raise HTTP exceptions with their status code
        raise
    except Exception as e:
        logger.error(f"Error analyzing complaints: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to analyze complaints: {str(e)}")
