from fastapi import APIRouter, HTTPException, Depends
import logging
from datetime import datetime, timezone
from routes.auth import get_current_user
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
from utils.input_validator import validate_string, validate_dict
from utils.db_sanitizer import sanitize_find_query, sanitize_update_query
from utils.error_utils import validation_error, not_found_error, server_error

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/users/subscription")
async def get_subscription_details(user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    """Get the user's subscription details"""
    try:
        # Validate user object
        if not isinstance(user, dict):
            server_error("Invalid user object format")

        # Get user ID - handle both formats (_id or id)
        user_id = None
        if "_id" in user:
            user_id = str(user["_id"])
        elif "id" in user:
            user_id = user["id"]
        else:
            # Log the user object for debugging
            logger.error(f"User object missing ID field: {user}")
            server_error("User ID not found in user object")

        # Validate user_id
        is_valid_id, sanitized_id, id_error = validate_string(
            user_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            server_error(f"Invalid user ID format: {id_error}")

        logger.info(f"Getting subscription details for user ID: {sanitized_id}")

        # For user's own profile, we don't need ownership verification
        # Just fetch the user document directly with sanitized query
        query = sanitize_find_query({"_id": ObjectId(sanitized_id)})
        user_doc = await db.users.find_one(query)

        if not user_doc:
            logger.error(f"User document not found for ID: {sanitized_id}")
            not_found_error("User not found", "user", sanitized_id)

        # Check if subscription has expired
        now = datetime.now(timezone.utc)
        subscription_expires = user_doc.get("subscription_expires")
        is_paid = user_doc.get("paid", False)

        # Ensure subscription_expires is timezone-aware for comparison
        if subscription_expires and isinstance(subscription_expires, datetime) and subscription_expires.tzinfo is None:
            subscription_expires = subscription_expires.replace(tzinfo=timezone.utc)

        if is_paid and subscription_expires and subscription_expires < now:
            logger.info(f"User {user_doc['email']} subscription has expired. Setting paid status to false.")
            # Update user's paid status to false with sanitized query
            filter_query = sanitize_find_query({"_id": user_doc["_id"]})
            update_query = sanitize_update_query({"$set": {"paid": False, "subscription_expires": None}})
            await db.users.update_one(filter_query, update_query)
            # Update the user object to reflect the change
            is_paid = False
            subscription_expires = None

        # Return subscription details
        return {
            "planType": "Premium" if is_paid else "Basic",
            "status": "Active" if is_paid else "Free",
            "startDate": user_doc.get("subscription_start_date"),
            "subscriptionEndDate": subscription_expires,
            "billingCycle": user_doc.get("billing_cycle", "Annual")
        }
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error fetching subscription details: {str(e)}", exc_info=True)
        server_error(f"Failed to fetch subscription details: {str(e)}")
