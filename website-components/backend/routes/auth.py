from fastapi import APIRout<PERSON>, Request, HTTPException, Depends, Response
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
import logging
import jwt
import os
import secrets
from datetime import datetime, timedelta, timezone
import uuid
from typing import Optional
from pydantic import BaseModel, EmailStr

from database import get_db
from services.auth_service import (
    get_password_hash,
    verify_password,
    create_access_token,
    create_refresh_token,
    get_current_user,
    SECRET_KEY,
    ALGORITHM,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    MAX_FAILED_LOGIN_ATTEMPTS,
    ACCOUNT_LOCKOUT_MINUTES,
    PROGRESSIVE_LOCKOUT,
    SESSION_CONFLICT_RESOLUTION,
    ENFORCE_SINGLE_SESSION,
    create_tokens,
    verify_token,
    check_account_lockout,
    update_user_last_access,
    get_user_active_session,
    clear_all_user_sessions
)
from services.email_service import (
    generate_otp,
    get_otp_expiry,
    send_verification_email,
    send_password_reset_email
)
from utils.password_validation import validate_password
from utils.input_validator import validate_auth_input, validate_registration_input, validate_otp_input, raise_validation_error
from motor.motor_asyncio import AsyncIOMotorDatabase

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# OAuth2 password bearer for Swagger UI
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

def set_csrf_token_cookie(response: Response, is_production: bool = False) -> str:
    """
    Set CSRF token cookie for double-submit pattern.

    Args:
        response: FastAPI Response object
        is_production: Whether running in production

    Returns:
        The generated CSRF token
    """
    csrf_token = secrets.token_hex(32)
    cookie_name = "csrf_token"
    cookie_max_age = 3600  # 1 hour

    samesite_setting = "none" if is_production else "lax"
    domain_setting = ".ncrpmatrix.in" if is_production else None

    response.set_cookie(
        key=cookie_name,
        value=csrf_token,
        max_age=cookie_max_age,
        httponly=False,  # Must be readable by JavaScript for double-submit pattern
        samesite=samesite_setting,
        secure=is_production,
        path="/",
        domain=domain_setting
    )

    # Also set in response headers for client convenience
    response.headers["X-CSRF-Token"] = csrf_token

    return csrf_token

# Models
class UserCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    designation: Optional[str] = None

class UserResponse(BaseModel):
    email: str
    first_name: str
    last_name: str
    designation: Optional[str] = None
    email_verified: bool = False
    paid: bool = False
    subscription_start_date: Optional[datetime] = None
    subscription_expires: Optional[datetime] = None
    complaint_count: int = 0
    has_template: bool = False
    two_step_enabled: bool = False

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: Optional[dict] = None

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class EmailRequest(BaseModel):
    email: EmailStr

class PasswordReset(BaseModel):
    password: str

class TwoStepVerificationRequest(BaseModel):
    email: EmailStr
    password: str

class TwoStepVerificationOTPRequest(BaseModel):
    email: EmailStr
    otp: str

class TwoStepSettingUpdate(BaseModel):
    enabled: bool

class VerifyOTPRequest(BaseModel):
    email: EmailStr
    otp: str

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate, db: AsyncIOMotorDatabase = Depends(get_db)):
    """Register a new user with comprehensive input validation"""
    # Validate input using enhanced validation
    is_valid, sanitized_data, validation_errors = validate_registration_input(
        user_data.email,
        user_data.password,
        user_data.first_name,
        user_data.last_name,
        user_data.designation
    )
    if not is_valid:
        raise_validation_error(validation_errors)

    # Check if email already exists
    existing_user = await db.users.find_one({"email": sanitized_data["email"]})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Create user document with sanitized data and 3-day free trial
    now = datetime.now(timezone.utc)
    trial_expires = now + timedelta(days=3)  # 3-day free trial

    user = {
        "email": sanitized_data["email"],
        "hashed_password": get_password_hash(sanitized_data["password"]),
        "first_name": sanitized_data["first_name"],
        "last_name": sanitized_data["last_name"],
        "designation": sanitized_data["designation"],
        "email_verified": False,
        "created_at": now,
        "paid": True,  # Grant access during trial period
        "subscription_start_date": now,
        "subscription_expires": trial_expires,
        "failed_login_attempts": 0,
        "account_locked_until": None,
        "two_step_enabled": False,  # Default to disabled
        "active": True  # Set user as active by default
    }

    # Insert user
    result = await db.users.insert_one(user)
    if not result.inserted_id:
        raise HTTPException(status_code=500, detail="Failed to create user")

    # Generate OTP and store it immediately
    otp = generate_otp()
    otp_expiry = get_otp_expiry()
    await db.users.update_one(
        {"_id": result.inserted_id},
        {"$set": {
            "verification_otp": otp,
            "verification_otp_expires": otp_expiry
        }}
    )

    # Create response with requires_verification flag and trial info
    response_data = {
        "id": str(result.inserted_id),
        "email": sanitized_data["email"],
        "first_name": sanitized_data["first_name"],
        "last_name": sanitized_data["last_name"],
        "designation": sanitized_data["designation"],
        "email_verified": False,
        "paid": True,  # User has access during trial
        "subscription_start_date": now,
        "subscription_expires": trial_expires,
        "requires_verification": True
    }

    # Send verification email asynchronously (don't wait for it)
    import asyncio
    asyncio.create_task(send_verification_email(
        email=sanitized_data["email"],
        otp=otp,
        first_name=sanitized_data["first_name"]
    ))

    return UserResponse(**response_data)

@router.post("/login")
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    response: Response = Response(),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """Login user with enhanced input validation and security"""
    try:
        # Validate input
        is_valid, sanitized_data, validation_errors = validate_auth_input(
            form_data.username, form_data.password
        )
        if not is_valid:
            raise_validation_error(validation_errors)

        # Check if account is locked
        is_locked, lockout_until = await check_account_lockout(db, sanitized_data["email"])
        if is_locked:
            lockout_msg = "Account temporarily locked"
            if lockout_until:
                remaining_time = int((lockout_until - datetime.now(timezone.utc)).total_seconds() / 60)
                if remaining_time > 0:
                    lockout_msg += f" for {remaining_time} minutes"
            raise HTTPException(status_code=423, detail=lockout_msg)

        # Get user
        user = await db.users.find_one({"email": sanitized_data["email"]})
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Verify password
        if not await verify_password(sanitized_data["password"], user["hashed_password"]):
            # Increment failed attempts
            current_attempts = user.get("failed_login_attempts", 0) + 1
            await db.users.update_one(
                {"email": sanitized_data["email"]},
                {
                    "$set": {
                        "failed_login_attempts": current_attempts,
                        "last_failed_login": datetime.now(timezone.utc)
                    }
                }
            )
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Check if two-step verification is enabled
        if user.get("two_step_enabled", False):
            # Generate OTP for two-step verification
            otp = generate_otp()
            otp_expiry = get_otp_expiry(minutes=5)  # Short expiry for login OTP

            # Update user with login OTP and reset failed attempts
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": {
                    "login_otp": otp,
                    "login_otp_expires": otp_expiry,
                    "failed_login_attempts": 0,  # Reset failed attempts on successful password verification
                    "last_failed_login": None
                }}
            )

            # Send OTP email asynchronously (don't wait for it)
            import asyncio
            asyncio.create_task(send_verification_email(
                email=user["email"],
                otp=otp,
                first_name=user["first_name"],
                is_login=True  # Indicate this is for login
            ))

            # Return response indicating two-step verification is required
            return {
                "message": "Verification code sent to your email",
                "email": user["email"],
                "requires_verification": True
            }

        # Reset failed attempts on successful login (for non-two-step users)
        await db.users.update_one(
            {"email": sanitized_data["email"]},
            {
                "$set": {
                    "failed_login_attempts": 0,
                    "last_login": datetime.now(timezone.utc)
                }
            }
        )

        # Generate a unique session ID for this login
        session_id = str(uuid.uuid4())

        # Get browser fingerprint for session tracking
        user_agent = request.headers.get("User-Agent", "")
        client_ip = request.client.host if request.client else "unknown"
        browser_fingerprint = f"{user_agent}:{client_ip}"

        # Check for existing active sessions BEFORE allowing login
        if ENFORCE_SINGLE_SESSION:
            active_session = await get_user_active_session(db, user["_id"])

            if active_session:
                active_session_id = active_session["session_id"]
                active_fingerprint = active_session.get("browser_fingerprint", "")

                # Determine if this is a different browser/device
                is_different_browser = (active_fingerprint != browser_fingerprint)

                # Only trigger conflict for different browsers/devices
                if is_different_browser:
                    if SESSION_CONFLICT_RESOLUTION == "TERMINATE_OLD":
                        # Automatically terminate old session and continue with new one
                        pass
                    else:
                        # Return conflict error - require user to resolve
                        raise HTTPException(
                            status_code=409,  # Conflict
                            detail={
                                "message": "Another session is active on a different browser/device. Please resolve the conflict.",
                                "code": "SESSION_CONFLICT",
                                "conflict_type": "DIFFERENT_BROWSER_LOGIN",
                                "active_session": {
                                    "id": active_session_id,
                                    "last_active": active_session["last_updated"].isoformat()
                                        if active_session.get("last_updated") else None
                                },
                                "requires_resolution": True
                            }
                        )
                else:
                    # Same browser - allow login (multiple tabs scenario)
                    pass

        # Create tokens
        access_token, refresh_token = create_tokens(sanitized_data["email"])

        # Update user's active session with browser fingerprint
        await update_user_last_access(
            db, user["_id"], user["email"],
            datetime.now(timezone.utc), session_id, browser_fingerprint
        )

        # Set secure cookies with proper expiration for session persistence
        is_production = os.getenv("ENVIRONMENT") == "production"
        cookie_samesite = "none" if is_production else "lax"
        cookie_domain = ".ncrpmatrix.in" if is_production else None
        cookie_secure = is_production

        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=60 * 60 * 24 * 7,
            domain=cookie_domain
        )
        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=60 * 60 * 24 * 30,
            domain=cookie_domain
        )
        response.set_cookie(
            key="session_id",
            value=session_id,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=60 * 60 * 24 * 7,
            domain=cookie_domain
        )

        # Set CSRF token cookie for double-submit pattern
        set_csrf_token_cookie(response, is_production)

        # Check if user has paid subscription
        is_paid = user.get("paid", False)

        # Check if subscription has expired
        now = datetime.now(timezone.utc)
        subscription_expires = user.get("subscription_expires")
        is_paid = user.get("paid", False)

        # Ensure subscription_expires is timezone-aware for comparison
        if subscription_expires and isinstance(subscription_expires, datetime) and subscription_expires.tzinfo is None:
            subscription_expires = subscription_expires.replace(tzinfo=timezone.utc)

        if is_paid and subscription_expires and subscription_expires < now:
            # Update user's paid status to false
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": {"paid": False, "subscription_expires": None}}
            )
            is_paid = False
            subscription_expires = None

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "user": {
                "id": str(user["_id"]),
                "email": user["email"],
                "first_name": user["first_name"],
                "last_name": user.get("last_name"),
                "designation": user.get("designation"),
                "organization": user.get("organization"),
                "paid": is_paid,
                "email_verified": user.get("email_verified", False),
                "has_template": user.get("has_template", False),
                "two_step_enabled": user.get("two_step_enabled", False),
                "subscription_expires": subscription_expires,
                "session_id": session_id  # Include session ID in response
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/refresh")
async def refresh_token(
    request: Request,
    response: Response = Response(),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """Refresh access token using refresh token from cookies"""
    try:
        # Get refresh token from cookies
        refresh_token_cookie = request.cookies.get("refresh_token")
        if not refresh_token_cookie:
            raise HTTPException(status_code=401, detail="Refresh token missing")

        try:
            # Verify refresh token
            payload = await verify_token(refresh_token_cookie, "refresh")
            user_email = payload.get("sub")

            if not user_email:
                raise HTTPException(status_code=401, detail="Invalid token")
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid refresh token")

        # Get current session ID from request
        current_session_id = request.cookies.get("session_id") or request.headers.get("X-Session-ID")

        # If no session ID in cookies, generate a new one
        if not current_session_id:
            current_session_id = str(uuid.uuid4())

        # Get browser fingerprint for session tracking
        user_agent = request.headers.get("User-Agent", "")
        client_ip = request.client.host if request.client else "unknown"
        browser_fingerprint = f"{user_agent}:{client_ip}"

        # Find user to update session
        user = None
        if db is not None:
            user = await db.users.find_one({"email": user_email})

            if not user:
                raise HTTPException(status_code=401, detail="User not found")

            # If we have a session ID and user, check if it matches the active session
            if user and current_session_id:
                active_session = await get_user_active_session(db, user["_id"])

                # Check for session conflicts with proper browser/device detection
                if active_session:
                    active_session_id = active_session["session_id"]
                    active_fingerprint = active_session.get("browser_fingerprint", "")

                    # Determine if this is a different browser/device
                    is_different_browser = (active_fingerprint != browser_fingerprint)
                    is_different_session = (active_session_id != current_session_id)

                    # Only trigger conflict for different browsers/devices, not different tabs
                    if is_different_session and is_different_browser:
                        if SESSION_CONFLICT_RESOLUTION == "TERMINATE_OLD":
                            # Update to current session
                            await update_user_last_access(
                                db, user["_id"], user_email,
                                datetime.now(timezone.utc), current_session_id, browser_fingerprint
                            )
                        else:
                            # Return conflict error
                            raise HTTPException(
                                status_code=409,  # Conflict
                                detail={
                                    "message": "Another session is active on a different browser/device",
                                    "code": "SESSION_CONFLICT",
                                    "conflict_type": "DIFFERENT_BROWSER",
                                    "active_session": {
                                        "id": active_session_id,
                                        "last_active": active_session["last_updated"].isoformat()
                                            if active_session.get("last_updated") else None
                                    }
                                }
                            )
                    else:
                        # Same browser (multiple tabs allowed) or same session - update last access
                        await update_user_last_access(
                            db, user["_id"], user_email,
                            datetime.now(timezone.utc), current_session_id, browser_fingerprint
                        )

                else:
                    # No active session - create new one
                    await update_user_last_access(
                        db, user["_id"], user_email,
                        datetime.now(timezone.utc), current_session_id, browser_fingerprint
                    )

        # Create new tokens
        access_token, new_refresh_token = create_tokens(user_email)

        # Set secure cookies with proper expiration for session persistence
        is_production = os.getenv("ENVIRONMENT") == "production"
        cookie_samesite = "none" if is_production else "lax"
        cookie_domain = ".ncrpmatrix.in" if is_production else None
        cookie_secure = is_production

        # Set CSRF token cookie for double-submit pattern
        set_csrf_token_cookie(response, is_production)

        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=60 * 60 * 24 * 7,
            domain=cookie_domain
        )
        response.set_cookie(
            key="refresh_token",
            value=new_refresh_token,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=60 * 60 * 24 * 30,
            domain=cookie_domain
        )
        response.set_cookie(
            key="session_id",
            value=current_session_id,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=60 * 60 * 24 * 7,
            domain=cookie_domain
        )

        # Domain is already set above - no need to set again
        # Removing duplicate cookie setting that was causing conflicts

        return TokenResponse(
            access_token=access_token,
            refresh_token=new_refresh_token
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/logout")
async def logout(request: Request, response: Response = Response()):
    """Logout user and clear all session data"""
    try:
        # Get current user to clear session
        try:
            # Don't check for session conflicts during logout
            user = await get_current_user(request, check_session_conflict=False)

            # Clear user's active session in database
            db = get_db()
            if db is not None and user and user.get("_id"):
                # Clear all session data and refresh tokens
                await clear_all_user_sessions(db, user["_id"])
        except Exception as e:
            # Log the error but continue with logout
            pass

        # Clear all cookies with proper domain settings
        is_production = os.getenv("ENVIRONMENT") == "production"
        cookie_domain = ".ncrpmatrix.in" if is_production else None

        # Clear cookies with same settings as when they were set
        response.delete_cookie("access_token", domain=cookie_domain, path="/")
        response.delete_cookie("refresh_token", domain=cookie_domain, path="/")
        response.delete_cookie("session_id", domain=cookie_domain, path="/")
        response.delete_cookie("csrf_token", domain=cookie_domain, path="/")

        return {"message": "Successfully logged out from all devices"}

    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """Get current user info"""
    try:
        # Check if subscription has expired
        now = datetime.now(timezone.utc)
        subscription_expires = current_user.get("subscription_expires")
        is_paid = current_user.get("paid", False)

        # Ensure subscription_expires is timezone-aware for comparison
        if subscription_expires and isinstance(subscription_expires, datetime) and subscription_expires.tzinfo is None:
            subscription_expires = subscription_expires.replace(tzinfo=timezone.utc)

        if is_paid and subscription_expires and subscription_expires < now:
            # Update user's paid status to false
            db = get_db()
            if db is not None:
                await db.users.update_one(
                    {"_id": current_user["_id"]},
                    {"$set": {"paid": False, "subscription_expires": None}}
                )
            is_paid = False
            subscription_expires = None

        # Convert MongoDB _id to string and ensure all required fields are present
        user_data = {
            "id": str(current_user["_id"]),
            "email": current_user["email"],
            "first_name": current_user["first_name"],
            "last_name": current_user.get("last_name", ""),
            "designation": current_user.get("designation"),
            "organization": current_user.get("organization"),
            "email_verified": current_user.get("email_verified", False),
            "paid": is_paid,
            "subscription_start_date": current_user.get("subscription_start_date"),
            "subscription_expires": subscription_expires,
            "complaint_count": current_user.get("complaint_count", 0),
            "has_template": current_user.get("has_template", False),
            "two_step_enabled": current_user.get("two_step_enabled", False)
        }
        return UserResponse(**user_data)
    except Exception as e:
        logger.error(f"Error in get_current_user_info: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/verify-token")
async def verify_token_endpoint(request: Request):
    """Verify if the current token is valid"""
    try:
        token = request.cookies.get("access_token")
        if not token:
            raise HTTPException(status_code=401, detail="Not authenticated")

        await verify_token(token, "access")
        return {"valid": True}

    except HTTPException:
        return {"valid": False}
    except Exception as e:
        logger.error(f"Token verification error: {str(e)}")
        return {"valid": False}

@router.post("/resolve-session-conflict")
async def resolve_session_conflict(
    request: Request,
    action: str,
    response: Response = Response()
):
    """
    Resolve a session conflict

    Args:
        action: Either 'TAKE_OVER' or 'LOGOUT'
    """
    try:
        # Validate action
        if action not in ["TAKE_OVER", "LOGOUT"]:
            raise HTTPException(status_code=400, detail="Invalid action")

        # Get current user
        try:
            # Skip session conflict check to avoid infinite loop
            user = await get_current_user(request, check_session_conflict=False)
        except HTTPException:
            raise HTTPException(status_code=401, detail="Not authenticated")

        # Get current session ID
        current_session_id = request.cookies.get("session_id") or request.headers.get("X-Session-ID")
        if not current_session_id:
            raise HTTPException(status_code=400, detail="No session ID found")

        # Get database connection
        db = get_db()
        if db is None:
            raise HTTPException(status_code=500, detail="Database error")

        if action == "TAKE_OVER":
            # Get browser fingerprint for session tracking
            user_agent = request.headers.get("User-Agent", "")
            client_ip = request.client.host if request.client else "unknown"
            browser_fingerprint = f"{user_agent}:{client_ip}"

            # Update user's active session to current session
            await update_user_last_access(
                db, user["_id"], user["email"],
                datetime.now(timezone.utc), current_session_id, browser_fingerprint
            )
            return {"message": "Session conflict resolved, this is now the active session"}
        else:  # LOGOUT
            # Clear cookies with proper domain settings
            is_production = os.getenv("ENVIRONMENT") == "production"
            cookie_domain = ".ncrpmatrix.in" if is_production else None

            # Clear cookies with same settings as when they were set
            response.delete_cookie("access_token", domain=cookie_domain, path="/")
            response.delete_cookie("refresh_token", domain=cookie_domain, path="/")
            response.delete_cookie("session_id", domain=cookie_domain, path="/")
            response.delete_cookie("csrf_token", domain=cookie_domain, path="/")
            return {"message": "Logged out due to session conflict"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving session conflict: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/cleanup-session")
async def cleanup_session(
    request: Request,
    db=Depends(get_db)
):
    """
    Cleanup session on browser close
    """
    try:
        # Get session data from request body (sent via beacon)
        body = await request.body()
        if body:
            import json
            data = json.loads(body.decode('utf-8'))
            session_id = data.get('session_id')
            reason = data.get('reason', 'unknown')

            logger.info(f"Session cleanup requested: {session_id}, reason: {reason}")

            # Find user with this session and clear it
            if session_id:
                result = await db.users.update_one(
                    {"active_session_id": session_id},
                    {
                        "$unset": {
                            "active_session_id": "",
                            "active_session_updated": "",
                            "browser_fingerprint": ""
                        }
                    }
                )

                if result.modified_count > 0:
                    logger.info(f"Successfully cleaned up session: {session_id}")
                else:
                    logger.warning(f"Session not found for cleanup: {session_id}")

        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error cleaning up session: {str(e)}")
        # Don't raise error for cleanup endpoint
        return {"status": "error", "message": str(e)}

@router.post("/verify-otp")
async def verify_otp(verify_data: VerifyOTPRequest):
    """
    Verify email using OTP with enhanced validation
    """
    try:
        # Validate input using enhanced validation
        is_valid, sanitized_data, validation_errors = validate_otp_input(
            str(verify_data.email), verify_data.otp
        )
        if not is_valid:
            raise_validation_error(validation_errors)

        # Get database connection
        db = get_db()
        if db is None:
            logger.error("Database connection error during OTP verification")
            raise HTTPException(status_code=500, detail="Database connection error")

        # Find user by email using sanitized data
        user = await db.users.find_one({"email": sanitized_data["email"]})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if user is already verified
        if user.get("email_verified", False):
            logger.info(f"OTP verification attempted for already verified user: {user['email']}")
            return {
                "message": "Email is already verified",
                "email_verified": True,
                "success": True
            }

        # Check if OTP is valid
        stored_otp = user.get("verification_otp")
        otp_expires = user.get("verification_otp_expires")

        if not stored_otp or not otp_expires:
            raise HTTPException(status_code=400, detail="OTP not found or expired. Please request a new one.")

        # Check if OTP is expired
        # Convert otp_expires to timezone-aware datetime if it's naive
        if isinstance(otp_expires, datetime) and otp_expires.tzinfo is None:
            otp_expires = otp_expires.replace(tzinfo=timezone.utc)

        # Check if OTP is expired
        now = datetime.now(timezone.utc)

        if now > otp_expires:

            # Clear expired OTP
            await db.users.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "verification_otp": None,
                        "verification_otp_expires": None
                    }
                }
            )

            raise HTTPException(status_code=400, detail="OTP has expired. Please request a new one.")

        # Check if OTP matches using sanitized data
        if sanitized_data["otp"] != stored_otp:
            logger.warning("Invalid OTP attempt")

            # Track failed attempts
            current_attempts = user.get("otp_failed_attempts", 0)

            # Update failed attempts count
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": {"otp_failed_attempts": current_attempts + 1}}
            )

            # If too many failed attempts, invalidate the OTP
            if current_attempts >= 4:  # 5 attempts total (0-indexed)
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {
                        "$set": {
                            "verification_otp": None,
                            "verification_otp_expires": None,
                            "otp_failed_attempts": 0
                        }
                    }
                )
                raise HTTPException(status_code=400, detail="Too many failed attempts. Please request a new OTP.")

            raise HTTPException(status_code=400, detail="Invalid OTP. Please try again.")

        # Update user as verified (but don't log them in automatically)
        update_result = await db.users.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "email_verified": True,
                    "verification_otp": None,
                    "verification_otp_expires": None,
                    "otp_failed_attempts": 0,
                    "active": True
                }
            }
        )

        if update_result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update verification status")

        logger.info("Email verification successful")

        # Return success message without tokens (user needs to login separately)
        return {
            "message": "Email verified successfully! Please login with your credentials.",
            "email_verified": True,
            "success": True
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in OTP verification: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred during verification")

@router.post("/resend-otp")
async def resend_otp(email: str):
    """
    Resend OTP for email verification
    """
    try:
        # Validate email
        if not email or '@' not in email:
            raise HTTPException(status_code=400, detail="Invalid email format")

        # Get database connection
        db = get_db()
        if db is None:
            logger.error("Database connection error during OTP resend")
            raise HTTPException(status_code=500, detail="Database connection error")

        # Find user by email
        user = await db.users.find_one({"email": email})
        if not user:
            logger.warning("OTP resend attempted for non-existent user")
            raise HTTPException(status_code=404, detail="User not found")

        # Check if user is already verified
        if user.get("email_verified", False):
            logger.info("OTP resend attempted for already verified user")
            return {"message": "Email is already verified"}

        # Check for rate limiting - prevent too many OTP requests
        last_otp_request = user.get("last_otp_request")
        now = datetime.now(timezone.utc)

        if last_otp_request:
            # Convert to timezone-aware datetime if it's naive
            if isinstance(last_otp_request, datetime) and last_otp_request.tzinfo is None:
                last_otp_request = last_otp_request.replace(tzinfo=timezone.utc)

            # Check if last request was less than 1 minute ago
            time_diff = (now - last_otp_request).total_seconds()
            if time_diff < 60:  # 1 minute rate limit
                logger.warning(f"Rate limited OTP resend, last request {int(time_diff)} seconds ago")
                raise HTTPException(
                    status_code=429,
                    detail=f"Too many requests. Please wait {60 - int(time_diff)} seconds before requesting another OTP."
                )

        # Generate new OTP
        otp = generate_otp()
        # Use 15 minutes expiry for better security with 500 users
        otp_expiry = get_otp_expiry(minutes=15)

        # Update user with new OTP and reset failed attempts
        update_result = await db.users.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "verification_otp": otp,
                    "verification_otp_expires": otp_expiry,
                    "otp_failed_attempts": 0,
                    "last_otp_request": now
                }
            }
        )

        if update_result.modified_count == 0:
            logger.error("Failed to update OTP for user")
            raise HTTPException(status_code=500, detail="Failed to generate new OTP")

        # Return response immediately - email will be sent asynchronously
        response_message = {"message": "Verification OTP sent successfully"}

        # Send verification email asynchronously (don't wait for it)
        import asyncio
        asyncio.create_task(send_verification_email(
            email=user["email"],
            otp=otp,
            first_name=user["first_name"]
        ))

        logger.info("OTP resent successfully")
        return response_message
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in OTP resend: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred while resending OTP")

@router.post("/forgot-password")
async def forgot_password(email_request: EmailRequest):
    """
    Send password reset email with OTP
    """
    try:
        email = email_request.email

        # Validate email
        if not email or '@' not in email:
            raise HTTPException(status_code=400, detail="Invalid email format")

        # Get database connection
        db = get_db()
        if db is None:
            logger.error("Database connection error during password reset request")
            raise HTTPException(status_code=500, detail="Database connection error")

        # Find user by email
        user = await db.users.find_one({"email": email})
        if not user:
            # Don't reveal if email exists or not for security
            logger.info(f"Password reset requested for non-existent user: {email}")
            return {"message": "If your email is registered, you will receive a password reset code"}

        # Check for rate limiting - prevent too many reset requests
        last_reset_request = user.get("last_reset_request")
        now = datetime.now(timezone.utc)

        if last_reset_request:
            # Convert to timezone-aware datetime if it's naive
            if isinstance(last_reset_request, datetime) and last_reset_request.tzinfo is None:
                last_reset_request = last_reset_request.replace(tzinfo=timezone.utc)

            # Check if last request was less than 5 minutes ago
            time_diff = (now - last_reset_request).total_seconds()
            if time_diff < 300:  # 5 minute rate limit
                logger.warning(f"Rate limited password reset for user: {email}, last request {int(time_diff)} seconds ago")
                # Don't reveal rate limiting to prevent user enumeration
                return {"message": "If your email is registered, you will receive a password reset code"}

        # Generate OTP for password reset
        reset_otp = generate_otp()
        # Use 15 minutes expiry for better security with 500 users
        otp_expiry = get_otp_expiry(minutes=15)

        # Update user with reset OTP info
        await db.users.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "password_reset_otp": reset_otp,
                    "password_reset_otp_expires": otp_expiry,
                    "password_reset_otp_used": False,  # Track if OTP has been used
                    "last_reset_request": now,
                    "reset_otp_failed_attempts": 0  # Track failed attempts
                }
            }
        )

        # Return response immediately - email will be sent asynchronously
        response_message = {"message": "If your email is registered, you will receive a password reset code"}

        # Send password reset email asynchronously (don't wait for it)
        import asyncio
        asyncio.create_task(send_password_reset_email(
            email=email,
            otp=reset_otp,
            first_name=user.get("first_name", "")
        ))

        return response_message

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in password reset request: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@router.post("/reset-password")
async def reset_password(email: str, otp: str, password_data: PasswordReset):
    """
    Reset password using OTP
    """
    try:
        # Validate OTP format
        if not otp:
            raise HTTPException(status_code=400, detail="Reset code is required")

        # Validate email format
        if not email or '@' not in email:
            raise HTTPException(status_code=400, detail="Invalid email format")

        # Validate password
        is_valid, error_messages = validate_password(password_data.password)
        if not is_valid:
            logger.warning(f"Password validation failed during reset: {error_messages}")
            raise HTTPException(
                status_code=400,
                detail={"message": "Password does not meet security requirements", "errors": error_messages}
            )

        # Get database connection
        db = get_db()
        if db is None:
            logger.error("Database connection error during password reset")
            raise HTTPException(status_code=500, detail="Database connection error")

        # Find user by email
        user = await db.users.find_one({"email": email})
        if not user:
            logger.warning("Password reset attempted for non-existent user")
            raise HTTPException(status_code=400, detail="Invalid reset request")

        # Verify OTP matches stored OTP
        stored_otp = user.get("password_reset_otp")
        if not stored_otp or stored_otp != otp:
            # Increment failed attempts
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$inc": {"reset_otp_failed_attempts": 1}}
            )

            # Get updated user to check failed attempts
            updated_user = await db.users.find_one({"_id": user["_id"]})
            failed_attempts = updated_user.get("reset_otp_failed_attempts", 0) if updated_user else 0

            # If too many failed attempts, invalidate the OTP
            if failed_attempts >= 5:
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {
                        "$set": {
                            "password_reset_otp": None,
                            "password_reset_otp_expires": None,
                            "reset_otp_failed_attempts": 0
                        }
                    }
                )
                logger.warning(f"OTP invalidated after {failed_attempts} failed attempts for user: {email}")
                raise HTTPException(status_code=400, detail="Too many failed attempts. Please request a new reset code.")

            logger.warning(f"OTP mismatch during password reset for user: {email}. Attempt {failed_attempts} of 5.")
            raise HTTPException(status_code=400, detail="Invalid reset code")

        # Check if OTP is expired
        reset_expires = user.get("password_reset_otp_expires")
        if not reset_expires:
            logger.warning(f"No reset OTP expiry found for user: {email}")
            raise HTTPException(status_code=400, detail="Reset code has expired")

        # Convert reset_expires to timezone-aware datetime if it's naive
        if isinstance(reset_expires, datetime) and reset_expires.tzinfo is None:
            reset_expires = reset_expires.replace(tzinfo=timezone.utc)

        if reset_expires < datetime.now(timezone.utc):
            logger.warning(f"Expired reset OTP for user: {email}")
            raise HTTPException(status_code=400, detail="Reset code has expired")

        # Check if OTP has already been used
        if user.get("password_reset_otp_used", False):
            logger.warning(f"Used reset OTP attempted again for user: {email}")
            raise HTTPException(status_code=400, detail="Reset code has already been used")

        # Hash new password
        hashed_password = get_password_hash(password_data.password)

        # Update user with new password and mark OTP as used
        update_result = await db.users.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "hashed_password": hashed_password,
                    "password_reset_otp_used": True,
                    "reset_otp_failed_attempts": 0,
                    "password_last_changed": datetime.now(timezone.utc)
                }
            }
        )

        if update_result.modified_count == 0:
            logger.error(f"Failed to update password for user: {email}")
            raise HTTPException(status_code=500, detail="Failed to update password")

        logger.info(f"Password successfully reset for user: {email}")

        # Return success message
        return {"message": "Password has been reset successfully"}

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in password reset: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred during password reset")

@router.post("/two-step/login")
async def two_step_login(login_data: TwoStepVerificationRequest, response: Response = Response(), db_param: AsyncIOMotorDatabase = Depends(get_db)):
    """
    First step of two-step verification login

    This endpoint verifies the user's credentials and sends an OTP for the second step
    """
    try:
        # Get database connection - use the injected db if available
        db = db_param
        if db is None:
            logger.error("Database connection error during two-step login")
            raise HTTPException(status_code=500, detail="Database connection error")

        # Find user by email
        user = await db.users.find_one({"email": login_data.email})
        if not user:
            # Use the same error message for non-existent users to prevent user enumeration
            raise HTTPException(status_code=401, detail="Authentication failed")

        # Check if account is locked
        account_locked_until = user.get("account_locked_until")
        if account_locked_until:
            # Convert to timezone-aware datetime if it's naive
            if isinstance(account_locked_until, datetime) and account_locked_until.tzinfo is None:
                account_locked_until = account_locked_until.replace(tzinfo=timezone.utc)

            # Check if the lockout period has expired
            now = datetime.now(timezone.utc)
            if now < account_locked_until:
                # Calculate remaining lockout time in minutes
                remaining_minutes = round((account_locked_until - now).total_seconds() / 60)
                logger.warning("Two-step login attempt for locked account")
                raise HTTPException(
                    status_code=401,
                    detail=f"Account is temporarily locked due to multiple failed login attempts. "
                           f"Please try again in {remaining_minutes} minutes."
                )
            else:
                # Lockout period has expired, reset failed attempts
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": {"failed_login_attempts": 0, "account_locked_until": None}}
                )
                logger.info("Account lockout period expired")

        # Verify password
        if not await verify_password(login_data.password, user["hashed_password"]):
            # Increment failed login attempts
            current_attempts = user.get("failed_login_attempts", 0) + 1
            last_failed = datetime.now(timezone.utc)

            # Update the failed attempts count
            update_data = {
                "failed_login_attempts": current_attempts,
                "last_failed_login": last_failed
            }

            # Check if we should lock the account
            if current_attempts >= MAX_FAILED_LOGIN_ATTEMPTS:
                # Calculate lockout duration with progressive lockout if enabled
                lockout_minutes = ACCOUNT_LOCKOUT_MINUTES

                # If progressive lockout is enabled, increase lockout time based on previous lockouts
                if PROGRESSIVE_LOCKOUT:
                    # Get previous lockout count
                    previous_lockouts = user.get("account_lockout_count", 0)

                    # Double the lockout time for each previous lockout, up to a maximum of 24 hours
                    if previous_lockouts > 0:
                        # Calculate progressive lockout time: base_time * 2^previous_lockouts
                        lockout_minutes = min(ACCOUNT_LOCKOUT_MINUTES * (2 ** previous_lockouts), 24 * 60)
                        logger.warning(f"Progressive lockout applied for user {user['email']}: {lockout_minutes} minutes")

                    # Increment lockout count
                    update_data["account_lockout_count"] = previous_lockouts + 1

                # Set lockout expiry time
                lockout_until = datetime.now(timezone.utc) + timedelta(minutes=lockout_minutes)
                update_data["account_locked_until"] = lockout_until

                logger.warning(f"Account locked due to {current_attempts} failed login attempts. Locked for {lockout_minutes} minutes.")

            # Update the user record
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": update_data}
            )

            # Log the failed attempt
            logger.warning(f"Failed two-step login attempt {current_attempts}")

            # Use the same error message to prevent user enumeration
            raise HTTPException(status_code=401, detail="Invalid email or password")

        # Check if two-step verification is enabled
        if not user.get("two_step_enabled", False):

            # Create access token
            access_token = create_access_token(
                data={"sub": user["email"]},
                expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            )

            # Create refresh token
            refresh_token = create_refresh_token(
                data={"sub": user["email"]}
            )

            # Store refresh token in database for validation
            refresh_token_jti = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM]).get("jti")

            # Store refresh token info in database
            await db.user_refresh_tokens.insert_one({
                "user_id": user["_id"],
                "token": refresh_token,
                "jti": refresh_token_jti,
                "created_at": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + timedelta(days=7),
                "is_revoked": False
            })

            # Update last login
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": {"last_login": datetime.now(timezone.utc)}}
            )

            # Set HttpOnly cookies for tokens with consistent configuration
            is_production = os.getenv("ENVIRONMENT") == "production"
            cookie_secure = is_production

            # Use consistent configuration with CSRF tokens and other endpoints
            cookie_samesite = "none" if is_production else "lax"
            cookie_domain = ".ncrpmatrix.in" if is_production else None



            # Set CSRF token cookie for double-submit pattern
            set_csrf_token_cookie(response, is_production)

            # Set access token cookie (short-lived)
            response.set_cookie(
                key="access_token",
                value=access_token,
                httponly=True,
                secure=cookie_secure,
                samesite=cookie_samesite,
                max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert minutes to seconds
                path="/",
                domain=cookie_domain
            )

            # Set refresh token cookie (longer-lived)
            response.set_cookie(
                key="refresh_token",
                value=refresh_token,
                httponly=True,
                secure=cookie_secure,
                samesite=cookie_samesite,
                max_age=7 * 24 * 60 * 60,  # 7 days in seconds
                path="/",
                domain=cookie_domain
            )

            # Note: session_id cookie is set in the main login endpoint, not needed here

            # Return token and user data (tokens still included for backward compatibility)
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user["_id"]),
                    "email": user["email"],
                    "first_name": user["first_name"],
                    "last_name": user.get("last_name"),
                    "designation": user.get("designation"),
                    "organization": user.get("organization"),
                    "paid": user.get("paid", True),
                    "email_verified": user.get("email_verified", False),
                    "has_template": user.get("has_template", False),
                    "two_step_enabled": False
                }
            }

        # Generate OTP for two-step verification
        otp = generate_otp()
        otp_expiry = get_otp_expiry(minutes=5)  # Short expiry for login OTP

        # Update user with login OTP
        await db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {
                "login_otp": otp,
                "login_otp_expires": otp_expiry,
                "failed_login_attempts": 0,  # Reset failed attempts on successful password verification
                "last_failed_login": None
            }}
        )

        # Send OTP email asynchronously (don't wait for it)
        import asyncio
        asyncio.create_task(send_verification_email(
            email=user["email"],
            otp=otp,
            first_name=user["first_name"],
            is_login=True  # Indicate this is for login
        ))

        logger.info("Login OTP being sent to user")

        # Return success message
        return {
            "message": "Verification code sent to your email",
            "email": user["email"],
            "requires_verification": True
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in two-step login: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@router.post("/two-step/verify", response_model=TokenResponse)
async def verify_two_step(verify_data: TwoStepVerificationOTPRequest, response: Response = Response()):
    """
    Second step of two-step verification login

    This endpoint verifies the OTP and completes the login process
    """
    try:
        # Get database connection
        db = get_db()
        if db is None:
            raise HTTPException(status_code=500, detail="Database connection error")

        # Find user by email
        user = await db.users.find_one({"email": verify_data.email})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if two-step verification is enabled
        if not user.get("two_step_enabled", False):
            raise HTTPException(status_code=400, detail="Two-step verification is not enabled for this account")

        # Check if OTP exists
        stored_otp = user.get("login_otp")
        otp_expires = user.get("login_otp_expires")

        if not stored_otp or not otp_expires:
            raise HTTPException(status_code=400, detail="No verification code found. Please request a new one.")

        # Check if OTP is expired
        # Convert otp_expires to timezone-aware datetime if it's naive
        if isinstance(otp_expires, datetime) and otp_expires.tzinfo is None:
            otp_expires = otp_expires.replace(tzinfo=timezone.utc)

        now = datetime.now(timezone.utc)
        if now > otp_expires:
            # Clear expired OTP
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": {
                    "login_otp": None,
                    "login_otp_expires": None
                }}
            )

            raise HTTPException(status_code=400, detail="Verification code has expired. Please request a new one.")

        # Check if OTP matches
        if verify_data.otp != stored_otp:

            # Track failed attempts
            current_attempts = user.get("otp_failed_attempts", 0)

            # Update failed attempts count
            await db.users.update_one(
                {"_id": user["_id"]},
                {"$set": {"otp_failed_attempts": current_attempts + 1}}
            )

            # If too many failed attempts, invalidate the OTP
            if current_attempts >= 4:  # 5 attempts total (0-indexed)
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": {
                        "login_otp": None,
                        "login_otp_expires": None,
                        "otp_failed_attempts": 0
                    }}
                )
                raise HTTPException(status_code=400, detail="Too many failed attempts. Please request a new verification code.")

            raise HTTPException(status_code=400, detail="Invalid verification code. Please try again.")

        # Clear OTP after successful verification
        await db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {
                "login_otp": None,
                "login_otp_expires": None,
                "otp_failed_attempts": 0,
                "last_login": datetime.now(timezone.utc)
            }}
        )

        # Create access token
        access_token = create_access_token(
            data={"sub": user["email"]},
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        # Create refresh token
        refresh_token = create_refresh_token(
            data={"sub": user["email"]}
        )

        # Store refresh token in database for validation
        refresh_token_jti = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM]).get("jti")

        # Store refresh token info in database
        await db.user_refresh_tokens.insert_one({
            "user_id": user["_id"],
            "token": refresh_token,
            "jti": refresh_token_jti,
            "created_at": datetime.now(timezone.utc),
            "expires_at": datetime.now(timezone.utc) + timedelta(days=7),
            "is_revoked": False
        })

        # Set HttpOnly cookies for tokens with consistent configuration
        is_production = os.getenv("ENVIRONMENT") == "production"
        cookie_secure = is_production
        cookie_samesite = "none" if is_production else "lax"
        cookie_domain = ".ncrpmatrix.in" if is_production else None

        # Set CSRF token cookie for double-submit pattern
        set_csrf_token_cookie(response, is_production)

        # Set access token cookie (short-lived)
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert minutes to seconds
            path="/",
            domain=cookie_domain
        )

        # Set refresh token cookie (longer-lived)
        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            httponly=True,
            secure=cookie_secure,
            samesite=cookie_samesite,
            max_age=7 * 24 * 60 * 60,  # 7 days in seconds
            path="/",
            domain=cookie_domain
        )

        # Note: session_id cookie is set in the main login endpoint, not needed here

        # Return token and user data (tokens still included for backward compatibility)
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "user": {
                "id": str(user["_id"]),
                "email": user["email"],
                "first_name": user["first_name"],
                "last_name": user.get("last_name"),
                "designation": user.get("designation"),
                "organization": user.get("organization"),
                "paid": user.get("paid", True),
                "email_verified": user.get("email_verified", False),
                "has_template": user.get("has_template", False),
                "two_step_enabled": True
            }
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        pass
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@router.post("/two-step/settings")
async def update_two_step_settings(settings: TwoStepSettingUpdate, request: Request):
    """
    Update two-step verification settings

    This endpoint enables or disables two-step verification for the user
    """
    try:
        # Get current user
        user = await get_current_user(request)

        # Get database connection
        db = get_db()
        if db is None:
            raise HTTPException(status_code=500, detail="Database connection error")

        # Update two-step verification setting
        update_result = await db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {"two_step_enabled": settings.enabled}}
        )

        if update_result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update settings")

        # Return success message
        return {
            "message": f"Two-step verification has been {'enabled' if settings.enabled else 'disabled'}",
            "two_step_enabled": settings.enabled
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors
        pass
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
