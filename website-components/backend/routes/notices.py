from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional, List
import logging
import json
import os
import tempfile
from io import BytesIO
import base64
import zipfile
from datetime import datetime
from bson import ObjectId
from routes.auth import get_current_user
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from services.notice_service import generate_notice_from_template, generate_notice_with_docxtpl
from services.operation_protection_service import track_operation_attempt
from middleware.paid_access_middleware import check_paid_access
from utils.auth_utils import verify_resource_ownership
from utils.input_validator import validate_string, validate_dict, raise_validation_error
from utils.db_sanitizer import sanitize_find_query, sanitize_mongodb_id
from utils.error_utils import validation_error, not_found_error, server_error

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/generate/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def generate_notice(
    complaint_id: str,
    bank: Optional[str] = None,
    points: Optional[str] = None,
    user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Generate a notice document for a specific complaint

    Args:
        complaint_id: ID of the complaint
        bank: Optional bank name to filter transactions
        points: Optional comma-separated list of points to include
        user: Current authenticated user
        db: Database connection

    Returns:
        Streaming response with the generated document
    """
    try:
        # Validate complaint_id
        is_valid_id, sanitized_id, id_error = validate_string(
            complaint_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            validation_error(f"Invalid complaint ID format: {id_error}", "complaint_id")

        # Ensure sanitized_id is not None
        if sanitized_id is None:
            validation_error("Complaint ID cannot be empty", "complaint_id")

        # Validate bank name if provided
        if bank is not None:
            is_valid_bank, sanitized_bank, bank_error = validate_string(
                bank, min_length=1, max_length=100
            )
            if not is_valid_bank:
                validation_error(f"Invalid bank name format: {bank_error}", "bank")
            bank = sanitized_bank

        # Validate points if provided
        if points is not None:
            is_valid_points, sanitized_points, points_error = validate_string(
                points, min_length=1, max_length=500
            )
            if not is_valid_points:
                validation_error(f"Invalid points format: {points_error}", "points")
            points = sanitized_points

        # Extract user ID from user object
        user_id = None
        if isinstance(user, dict):
            user_id = user.get("id") or user.get("_id")
            if user_id and isinstance(user_id, ObjectId):
                user_id = str(user_id)

            # Validate user_id
            if not user_id:
                server_error("User ID not found in user object")
        else:
            server_error("Invalid user object format")

        logger.info(f"User ID for notice generation: {user_id}")

        # Check operation protection - track and limit notice generation attempts
        # Initialize variables with default values
        allowed = True
        reason = None
        cooldown_minutes = None

        if user_id:  # Only check if user_id is valid
            allowed, reason, cooldown_minutes = await track_operation_attempt(
                user_id=str(user_id),  # Ensure user_id is a string
                operation_type="notice_generation",
                resource_id=complaint_id,
                metadata={"bank": bank, "points_count": len(points.split(',')) if points else 0}
            )

            if not allowed:
                logger.warning(f"Notice generation denied for user {user_id}: {reason}")
                error_message = "You have exceeded the notice generation limit. "
                if cooldown_minutes:
                    error_message += f"Please try again in {cooldown_minutes} minutes."
                raise HTTPException(status_code=429, detail=error_message)

        # Verify resource ownership using the validated complaint_id
        # At this point, sanitized_id is guaranteed to be a string due to our validation above
        complaint = await verify_resource_ownership(
            resource_id=complaint_id,  # Use the original ID as verify_resource_ownership has its own validation
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        # If we get here, the complaint exists and belongs to the user

        # Get bank notice data
        bank_notice_data = complaint.get("bank_notice_data")

        # If bank_notice_data is missing but we have transactions, generate it
        if not bank_notice_data and "transactions" in complaint and complaint["transactions"]:
            logger.info(f"No bank_notice_data found, generating from {len(complaint['transactions'])} transactions")
            from services.notice_service import structure_transactions_by_bank
            bank_notice_data = structure_transactions_by_bank(complaint["transactions"])

            # Save the generated bank_notice_data to the complaint
            try:
                await db.complaints.update_one(
                    {"_id": ObjectId(complaint_id)},
                    {"$set": {"bank_notice_data": bank_notice_data}}
                )
                logger.info(f"Saved generated bank_notice_data to complaint {complaint_id}")
            except Exception as e:
                logger.error(f"Error saving generated bank_notice_data: {str(e)}")

        # Parse bank_notice_data if it's a string
        if isinstance(bank_notice_data, str):
            try:
                bank_notice_data = json.loads(bank_notice_data)
            except:
                logger.error(f"Error parsing bank_notice_data JSON for complaint {complaint_id}")
                bank_notice_data = []

        # Get metadata with comprehensive fallbacks
        metadata = {
            "complaint_number": complaint.get("complaint_number") or complaint.get("Acknowledgement No") or "N/A",
            "complainant_name": complaint.get("complainant_name") or complaint.get("Name") or "N/A",
            "total_amount": complaint.get("amount") or complaint.get("Total Fraudulent Amount") or "N/A",
            "date": complaint.get("date") or complaint.get("Date of Complaint") or "N/A",
            "category": complaint.get("category") or "N/A",
            "subcategory": complaint.get("subcategory") or "N/A",
            "COMPLAINT_NUMBER": complaint.get("complaint_number") or complaint.get("Acknowledgement No") or "N/A",
            "COMPLAINT_DATE": complaint.get("date") or complaint.get("Date of Complaint") or "N/A",
            "VICTIM_NAME": complaint.get("complainant_name") or complaint.get("Name") or "N/A",
            "TOTAL_FRAUD_AMOUNT": complaint.get("amount") or complaint.get("Total Fraudulent Amount") or "N/A",
            "CATEGORY": complaint.get("category") or "N/A",
            "SUBCATEGORY": complaint.get("subcategory") or "N/A"
        }

        # Make sure to include CSV data if available
        if "csv_data_base64" in complaint:
            metadata["csv_data_base64"] = complaint["csv_data_base64"]

        # Add all other fields from complaint to metadata
        for key, value in complaint.items():
            if key not in metadata and key != "_id" and key != "bank_notice_data" and key != "notice_docx_base64":
                metadata[key] = value

        # Get user's template if available
        user_template = None

        # First try to get from users collection
        if user_id:
            user_doc = await db.users.find_one({"_id": ObjectId(user_id)})
            if user_doc and user_doc.get("has_template") and "notice_template" in user_doc:
                # Convert base64 template to BytesIO
                try:
                    template_bytes = base64.b64decode(user_doc["notice_template"])
                    user_template = BytesIO(template_bytes)
                    logger.info(f"Using user template from users collection for {user_id}")
                except Exception as e:
                    logger.error(f"Error decoding user template: {str(e)}")
                    user_template = None

        # If not found, try templates collection (legacy)
        if user_template is None and user_id:
            template_doc = await db.templates.find_one({"user_id": user_id})
            if template_doc and "template_content" in template_doc:
                # Convert base64 template to BytesIO
                try:
                    template_bytes = base64.b64decode(template_doc["template_content"])
                    user_template = BytesIO(template_bytes)
                    logger.info(f"Using user template from templates collection for {user_id}")
                except Exception as e:
                    logger.error(f"Error decoding template: {str(e)}")
                    user_template = None

        # Parse selected points if provided
        selected_points = None
        if points:
            selected_points = points.split(',')

        # Make sure CSV data is included in metadata if available
        if "csv_data_base64" not in metadata and "csv_data_base64" in complaint:
            metadata["csv_data_base64"] = complaint["csv_data_base64"]
            logger.info("Added CSV data to metadata")

        # Generate the notice document using docxtpl
        # Ensure bank_notice_data is never None to avoid type errors
        safe_bank_notice_data = bank_notice_data if bank_notice_data is not None else {}

        docx_bytes = generate_notice_with_docxtpl(
            bank_notice_data=safe_bank_notice_data,
            metadata=metadata,
            template_content=user_template if user_template is not None else BytesIO(),
            bank_name=bank if bank is not None else "",
            selected_points=selected_points if selected_points is not None else []
        )
        logger.info(f"Successfully generated notice for complaint {complaint_id}")

        # Customize filename if bank is specified
        bank_suffix = f"_{bank}" if bank else ""
        filename = f"Notice_{complaint.get('complaint_number', 'Unknown')}{bank_suffix}.docx"

        return StreamingResponse(
            docx_bytes,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error generating notice: {str(e)}", exc_info=True)
        server_error(f"Error generating notice: {str(e)}")

@router.get("/download/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def download_notice(
    complaint_id: str,
    bank: Optional[str] = None,
    points: Optional[str] = None,
    user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Download the pre-generated notice document for a specific complaint

    Args:
        complaint_id: ID of the complaint
        bank: Optional bank name to filter transactions
        points: Optional comma-separated list of points to include
        user: Current authenticated user
        db: Database connection

    Returns:
        Streaming response with the document
    """
    try:
        # Validate complaint_id
        is_valid_id, sanitized_id, id_error = validate_string(
            complaint_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            validation_error(f"Invalid complaint ID format: {id_error}", "complaint_id")

        # Ensure sanitized_id is not None
        if sanitized_id is None:
            validation_error("Complaint ID cannot be empty", "complaint_id")

        # Validate bank name if provided
        if bank is not None:
            is_valid_bank, sanitized_bank, bank_error = validate_string(
                bank, min_length=1, max_length=100
            )
            if not is_valid_bank:
                validation_error(f"Invalid bank name format: {bank_error}", "bank")
            bank = sanitized_bank

        # Validate points if provided
        if points is not None:
            is_valid_points, sanitized_points, points_error = validate_string(
                points, min_length=1, max_length=500
            )
            if not is_valid_points:
                validation_error(f"Invalid points format: {points_error}", "points")
            points = sanitized_points

        # Extract user ID from user object
        user_id = None
        if isinstance(user, dict):
            user_id = user.get("id") or user.get("_id")
            if user_id and isinstance(user_id, ObjectId):
                user_id = str(user_id)

            # Validate user_id
            if not user_id:
                server_error("User ID not found in user object")
        else:
            server_error("Invalid user object format")

        logger.info(f"User ID for notice download: {user_id}")

        # Verify resource ownership
        complaint = await verify_resource_ownership(
            resource_id=complaint_id,
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        # If we get here, the complaint exists and belongs to the user

        # Always generate the notice on the fly instead of storing it
        logger.info(f"Generating notice on the fly for complaint {complaint_id}")

        # Parse selected points if provided
        selected_points = points.split(',') if points else None

        # Get user's template if available
        user_template = None
        user_doc = None
        # User ID should already be extracted above, no need to extract again

        logger.info(f"Looking for user template with user_id: {user_id}")

        if user_id:
            user_doc = await db.users.find_one({"_id": ObjectId(user_id)})
        if user_doc and user_doc.get("has_template") and user_doc.get("notice_template"):
            # Decode the template
            template_content = base64.b64decode(user_doc["notice_template"])
            user_template = BytesIO(template_content)

        # Get transaction data
        if "transactions" not in complaint:
            raise HTTPException(status_code=400, detail="No transaction data found in complaint")

        # Structure transactions by bank
        metadata = complaint.get("metadata", {})

        # Structure transactions by bank
        from services.notice_service import structure_transactions_by_bank
        bank_notice_data = structure_transactions_by_bank(complaint["transactions"])

        # Generate the notice document
        docx_bytes = generate_notice_with_docxtpl(
            bank_notice_data=bank_notice_data,
            metadata=metadata,
            template_content=user_template if user_template is not None else BytesIO(),
            bank_name=bank if bank is not None else "",
            selected_points=selected_points if selected_points is not None else []
        )

        # Customize filename if bank is specified
        bank_suffix = f"_{bank}" if bank else ""
        filename = f"Notice_{complaint.get('complaint_number', 'Unknown')}{bank_suffix}.docx"

        # Return the document as a downloadable file
        return StreamingResponse(
            BytesIO(docx_bytes.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error downloading notice: {str(e)}", exc_info=True)
        server_error(f"Error downloading notice: {str(e)}")

@router.get("/generate-batch/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def generate_batch_notices(
    complaint_id: str,
    banks: Optional[str] = None,
    points: Optional[str] = None,
    user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Generate a batch of notices for multiple banks and return as a ZIP file

    Args:
        complaint_id: ID of the complaint
        banks: Comma-separated list of bank names
        points: Optional comma-separated list of points to include
        user: Current authenticated user
        db: Database connection

    Returns:
        Streaming response with the generated ZIP file
    """
    try:
        # Validate complaint_id
        is_valid_id, sanitized_id, id_error = validate_string(
            complaint_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            validation_error(f"Invalid complaint ID format: {id_error}", "complaint_id")

        # Ensure sanitized_id is not None
        if sanitized_id is None:
            validation_error("Complaint ID cannot be empty", "complaint_id")

        # Validate banks parameter
        if banks is not None:
            is_valid_banks, sanitized_banks, banks_error = validate_string(
                banks, min_length=1, max_length=500
            )
            if not is_valid_banks:
                validation_error(f"Invalid banks format: {banks_error}", "banks")
            banks = sanitized_banks

        # Validate points if provided
        if points is not None:
            is_valid_points, sanitized_points, points_error = validate_string(
                points, min_length=1, max_length=500
            )
            if not is_valid_points:
                validation_error(f"Invalid points format: {points_error}", "points")
            points = sanitized_points

        # Parse banks parameter
        bank_list = banks.split(',') if banks else []

        if not bank_list:
            validation_error("No banks specified", "banks")

        # Extract user ID from user object
        user_id = None
        if isinstance(user, dict):
            user_id = user.get("id") or user.get("_id")
            if user_id and isinstance(user_id, ObjectId):
                user_id = str(user_id)

            # Validate user_id
            if not user_id:
                server_error("User ID not found in user object")
        else:
            server_error("Invalid user object format")

        # Check operation protection - track and limit batch notice generation attempts
        # This is more resource-intensive than single notice generation
        if user_id:  # Only check if user_id is valid
            allowed, reason, cooldown_minutes = await track_operation_attempt(
                user_id=str(user_id),  # Ensure user_id is a string
                operation_type="batch_download",
                resource_id=complaint_id,
                metadata={
                    "banks_count": len(bank_list),
                    "banks": banks,
                    "points_count": len(points.split(',')) if points else 0
                }
            )

            if not allowed:
                logger.warning(f"Batch notice generation denied for user {user_id}: {reason}")
                error_message = "You have exceeded the batch notice generation limit. "
                if cooldown_minutes:
                    error_message += f"Please try again in {cooldown_minutes} minutes."
                raise HTTPException(status_code=429, detail=error_message)

        # Create a ZIP file in memory
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:

            # Generate a notice for each bank
            for bank_name in bank_list:
                # Extract user ID from user object
                user_id = None
                if isinstance(user, dict):
                    user_id = user.get("id") or user.get("_id")
                    if user_id and isinstance(user_id, ObjectId):
                        user_id = str(user_id)

                    # Validate user_id
                    if not user_id:
                        server_error("User ID not found in user object")
                else:
                    server_error("Invalid user object format")

                logger.info(f"User ID for batch notice generation: {user_id}")

                # Verify resource ownership
                complaint = await verify_resource_ownership(
                    resource_id=complaint_id,
                    user_id=user_id,
                    collection_name="complaints",
                    db=db
                )

                # If we get here, the complaint exists and belongs to the user

                # Get bank notice data
                bank_notice_data = complaint.get("bank_notice_data")
                if isinstance(bank_notice_data, str):
                    try:
                        bank_notice_data = json.loads(bank_notice_data)
                    except:
                        logger.error(f"Error parsing bank_notice_data JSON for complaint {complaint_id}")
                        bank_notice_data = []

                # Get metadata with comprehensive fallbacks
                metadata = {
                    "complaint_number": complaint.get("complaint_number") or complaint.get("Acknowledgement No") or "N/A",
                    "complainant_name": complaint.get("complainant_name") or complaint.get("Name") or "N/A",
                    "total_amount": complaint.get("amount") or complaint.get("Total Fraudulent Amount") or "N/A",
                    "date": complaint.get("date") or complaint.get("Date of Complaint") or "N/A",
                    "category": complaint.get("category") or "N/A",
                    "subcategory": complaint.get("subcategory") or "N/A",
                    "COMPLAINT_NUMBER": complaint.get("complaint_number") or complaint.get("Acknowledgement No") or "N/A",
                    "COMPLAINT_DATE": complaint.get("date") or complaint.get("Date of Complaint") or "N/A",
                    "VICTIM_NAME": complaint.get("complainant_name") or complaint.get("Name") or "N/A",
                    "TOTAL_FRAUD_AMOUNT": complaint.get("amount") or complaint.get("Total Fraudulent Amount") or "N/A",
                    "CATEGORY": complaint.get("category") or "N/A",
                    "SUBCATEGORY": complaint.get("subcategory") or "N/A"
                }

                # Make sure to include CSV data if available
                if "csv_data_base64" in complaint:
                    metadata["csv_data_base64"] = complaint["csv_data_base64"]

                # Add all other fields from complaint to metadata
                for key, value in complaint.items():
                    if key not in metadata and key != "_id" and key != "bank_notice_data" and key != "notice_docx_base64":
                        metadata[key] = value

                # Get user's template if available
                user_template = None

                # First try to get from users collection
                if user_id:
                    user_doc = await db.users.find_one({"_id": ObjectId(user_id)})
                    if user_doc and user_doc.get("has_template") and "notice_template" in user_doc:
                        # Convert base64 template to BytesIO
                        try:
                            template_bytes = base64.b64decode(user_doc["notice_template"])
                            user_template = BytesIO(template_bytes)
                            logger.info(f"Using user template from users collection for {user_id}")
                        except Exception as e:
                            logger.error(f"Error decoding user template: {str(e)}")
                            user_template = None

                # If not found, try templates collection (legacy)
                if user_template is None and user_id:
                    template_doc = await db.templates.find_one({"user_id": user_id})
                    if template_doc and "template_content" in template_doc:
                        # Convert base64 template to BytesIO
                        try:
                            template_bytes = base64.b64decode(template_doc["template_content"])
                            user_template = BytesIO(template_bytes)
                            logger.info(f"Using user template from templates collection for {user_id}")
                        except Exception as e:
                            logger.error(f"Error decoding template: {str(e)}")
                            user_template = None

                # Parse selected points if provided
                selected_points = None
                if points:
                    selected_points = points.split(',')

                # Make sure CSV data is included                     bank_notice_data=safe_bank_notice_data,in metadata if available
                if "csv_data_base64" not in metadata and "csv_data_base64" in complaint:
                    metadata["csv_data_base64"] = complaint["csv_data_base64"]
                    logger.info("Added CSV data to metadata for batch generation")

                # Generate the notice document using docxtpl
                # Ensure bank_notice_data is never None to avoid type errors
                safe_bank_notice_data = bank_notice_data if bank_notice_data is not None else {}

                docx_bytes = generate_notice_with_docxtpl(
                    bank_notice_data=safe_bank_notice_data,
                    metadata=metadata,
                    template_content=user_template if user_template is not None else BytesIO(),
                    bank_name=bank_name,
                    selected_points=selected_points if selected_points is not None else []
                )
                logger.info(f"Successfully generated notice for bank {bank_name}")

                # Add to ZIP file
                filename = f"Notice_{complaint.get('complaint_number', 'Unknown')}_{bank_name}.docx"
                zip_file.writestr(filename, docx_bytes.getvalue())

        # Reset buffer position
        zip_buffer.seek(0)

        # Return the ZIP file
        # Get a default complaint number from the last processed complaint if available
        complaint_number = "Unknown"

        return StreamingResponse(
            zip_buffer,
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=Notices_{complaint_number}.zip"}
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error generating batch notices: {str(e)}", exc_info=True)
        server_error(f"Error generating batch notices: {str(e)}")


