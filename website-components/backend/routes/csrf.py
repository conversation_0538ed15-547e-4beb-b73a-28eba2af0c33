"""
CSRF token endpoint for getting CSRF tokens with enhanced security.
"""
import logging
import secrets
import time
import hashlib
from fastapi import APIRouter, Request, Response, HTTPException
from typing import Dict, Any
from collections import defaultdict
from datetime import datetime, timedelta

# Setup logging
logger = logging.getLogger(__name__)

# Rate limiting storage (in production, use Redis)
request_counts = defaultdict(list)
token_cache = {}  # Cache tokens to prevent unnecessary regeneration

# Security constants
MAX_REQUESTS_PER_MINUTE = 10
MAX_REQUESTS_PER_HOUR = 60
TOKEN_CACHE_DURATION = 300  # 5 minutes
SUSPICIOUS_THRESHOLD = 20  # Requests per minute that trigger security alert

# Create router
router = APIRouter(
    prefix="/csrf",
    tags=["csrf"],
)

def get_client_identifier(request: Request) -> str:
    """
    Get a unique identifier for the client for rate limiting.
    Uses IP + User-Agent hash for better uniqueness while preserving privacy.
    """
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "")

    # Create a hash of IP + User-Agent for privacy
    identifier_string = f"{client_ip}:{user_agent}"
    return hashlib.sha256(identifier_string.encode()).hexdigest()[:16]

def is_rate_limited(client_id: str) -> tuple[bool, str]:
    """
    Check if client is rate limited and return reason if so.
    """
    now = datetime.now()
    minute_ago = now - timedelta(minutes=1)
    hour_ago = now - timedelta(hours=1)

    # Clean old entries
    request_counts[client_id] = [
        req_time for req_time in request_counts[client_id]
        if req_time > hour_ago
    ]

    # Count requests in last minute and hour
    minute_requests = len([
        req_time for req_time in request_counts[client_id]
        if req_time > minute_ago
    ])
    hour_requests = len(request_counts[client_id])

    # Check limits
    if minute_requests >= MAX_REQUESTS_PER_MINUTE:
        return True, f"Too many requests per minute: {minute_requests}/{MAX_REQUESTS_PER_MINUTE}"

    if hour_requests >= MAX_REQUESTS_PER_HOUR:
        return True, f"Too many requests per hour: {hour_requests}/{MAX_REQUESTS_PER_HOUR}"

    # Check for suspicious activity
    if minute_requests >= SUSPICIOUS_THRESHOLD:
        logger.warning(f"Suspicious CSRF token request activity from {client_id}: {minute_requests} requests/minute")

    return False, ""

def get_cached_token(client_id: str) -> str | None:
    """
    Get cached token if still valid.
    """
    if client_id in token_cache:
        token_data = token_cache[client_id]
        if time.time() - token_data['created'] < TOKEN_CACHE_DURATION:
            return token_data['token']
        else:
            # Clean expired token
            del token_cache[client_id]
    return None

def cache_token(client_id: str, token: str) -> None:
    """
    Cache token for the client.
    """
    token_cache[client_id] = {
        'token': token,
        'created': time.time()
    }

@router.get("/token")
async def get_csrf_token(request: Request, response: Response) -> Dict[str, Any]:
    """
    Get a CSRF token for protecting against CSRF attacks with enhanced security.

    This endpoint implements:
    - Rate limiting per client
    - Token caching to prevent unnecessary regeneration
    - Suspicious activity detection
    - Client fingerprinting for better security

    Args:
        request: The incoming request
        response: The outgoing response

    Returns:
        A dictionary containing the CSRF token

    Raises:
        HTTPException: If rate limited or other security violations
    """
    # Get client identifier for rate limiting
    client_id = get_client_identifier(request)

    # Check rate limiting
    is_limited, reason = is_rate_limited(client_id)
    if is_limited:
        logger.warning(f"CSRF token request rate limited for client {client_id}: {reason}")
        raise HTTPException(
            status_code=429,
            detail={
                "error": "Rate limit exceeded",
                "message": "Too many CSRF token requests. Please try again later.",
                "retry_after": 60
            },
            headers={"Retry-After": "60"}
        )

    # Record this request
    request_counts[client_id].append(datetime.now())

    # Check for cached token first
    cached_token = get_cached_token(client_id)
    if cached_token:
        logger.info(f"Returning cached CSRF token for client {client_id}")
        csrf_token = cached_token
    else:
        # Generate a new CSRF token with additional entropy
        timestamp = str(int(time.time()))
        user_agent = request.headers.get("User-Agent", "")[:50]  # Limit length
        client_ip = request.client.host if request.client else "unknown"

        # Create base token with additional entropy
        base_token = secrets.token_hex(32)
        entropy_data = f"{base_token}:{timestamp}:{client_ip}:{user_agent}"

        # Hash the entropy data for the final token
        csrf_token = hashlib.sha256(entropy_data.encode()).hexdigest()

        cache_token(client_id, csrf_token)
        logger.info(f"Generated new CSRF token for client {client_id}")

    # Set the CSRF token cookie
    cookie_name = "csrf_token"
    cookie_max_age = 3600  # 1 hour

    # Enhanced security logging (avoid logging sensitive headers in production)
    import os
    is_production = os.getenv("ENVIRONMENT") == "production"

    if not is_production:
        logger.debug(f"CSRF token request from client {client_id}")
        logger.debug(f"Request headers: {dict(request.headers)}")
    else:
        # In production, only log essential information
        logger.info(f"CSRF token request from client {client_id}")

    # Set the cookie with enhanced security settings
    samesite_setting = "none" if is_production else "lax"
    domain_setting = ".ncrpmatrix.in" if is_production else None
    response.set_cookie(
        key=cookie_name,
        value=csrf_token,
        max_age=cookie_max_age,
        httponly=False,  # Must be readable by JavaScript for double-submit pattern
        samesite=samesite_setting,
        secure=is_production,
        path="/",
        domain=domain_setting
    )

    # Set security headers to prevent token leakage
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, private"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    # Set the token in response headers for client convenience
    response.headers["X-CSRF-Token"] = csrf_token

    # Set in alternate headers for better compatibility
    alternate_headers = [
        "x-csrf-token",
        "x-xsrf-token",
        "csrf-token",
        "xsrf-token"
    ]

    for header_name in alternate_headers:
        response.headers[header_name] = csrf_token

    # Security logging
    logger.info(f"CSRF token provided to client {client_id} (token: {csrf_token[:8]}...)")

    # Add security metadata to response
    return {
        "csrf_token": csrf_token,
        "expires_in": cookie_max_age,
        "algorithm": "double-submit-cookie",
        "security_note": "Include this token in X-CSRF-Token header for state-changing requests"
    }
