from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Request
import logging
import base64
from datetime import datetime, timezone
from routes.auth import get_current_user
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
# We don't need verify_resource_ownership for user's own resources
from models import UserProfileUpdate
from utils.input_validator import validate_string, validate_file
from utils.html_sanitizer import sanitize_text
from utils.db_sanitizer import sanitize_find_query, sanitize_update_query
from utils.error_utils import validation_error, not_found_error, server_error

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/users/template")
async def upload_template(
    request: Request,
    template: UploadFile = File(...),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """Upload a notice template for the user"""
    try:
        # Log request headers for debugging
        logger.info(f"Template upload request headers: {dict(request.headers)}")
        logger.info(f"Template upload request cookies: {request.cookies}")

        # Enhanced CSRF token validation logging
        csrf_cookie = request.cookies.get("csrf_token")
        csrf_header = request.headers.get("X-CSRF-Token")

        # Log all headers for comprehensive debugging
        logger.info("All request headers:")
        for header_name, header_value in request.headers.items():
            if any(token_name in header_name.lower() for token_name in ["csrf", "xsrf", "token"]):
                logger.info(f"Header {header_name}: {header_value[:8]}...")
            else:
                logger.info(f"Header {header_name}: {header_value}")

        # Check for CSRF token in cookie
        if csrf_cookie:
            logger.info(f"CSRF cookie found: {csrf_cookie[:8]}... (length: {len(csrf_cookie)})")
        else:
            logger.warning("No CSRF cookie found in request - this will cause CSRF validation to fail")

        # Check for CSRF token in primary header
        if csrf_header:
            logger.info(f"CSRF header found: {csrf_header[:8]}... (length: {len(csrf_header)})")

            # Validate that cookie and header match
            if csrf_cookie and csrf_header == csrf_cookie:
                logger.info("CSRF validation passed: cookie and header match")
            elif csrf_cookie:
                logger.warning("CSRF validation may fail: cookie and header do not match")
                logger.warning(f"Cookie: {csrf_cookie[:8]}..., Header: {csrf_header[:8]}...")
        else:
            # Check alternate headers
            alternate_headers = [
                "x-csrf-token",
                "x-xsrf-token",
                "csrf-token",
                "xsrf-token"
            ]

            found_alternate = False
            for header_name in alternate_headers:
                if header_value := request.headers.get(header_name):
                    found_alternate = True
                    logger.info(f"CSRF token found in alternate header {header_name}: {header_value[:8]}... (length: {len(header_value)})")

                    # Validate that cookie and alternate header match
                    if csrf_cookie and header_value == csrf_cookie:
                        logger.info(f"CSRF validation passed: cookie and {header_name} match")
                    elif csrf_cookie:
                        logger.warning(f"CSRF validation may fail: cookie and {header_name} do not match")
                        logger.warning(f"Cookie: {csrf_cookie[:8]}..., {header_name}: {header_value[:8]}...")

            if not found_alternate:
                logger.warning("No CSRF header found in any header - this will cause CSRF validation to fail")

        # Validate user object
        if not isinstance(user, dict):
            server_error("Invalid user object format")

        # Get user ID - handle both formats (_id or id)
        user_id = None
        if "_id" in user:
            user_id = str(user["_id"])
            logger.info(f"Using _id from user object: {user_id}")
        elif "id" in user:
            user_id = user["id"]
            logger.info(f"Using id from user object: {user_id}")
        else:
            # Log the user object for debugging
            logger.error(f"User object missing ID field: {user}")
            server_error("User ID not found in user object")

        # Validate user_id
        is_valid_id, _, id_error = validate_string(
            user_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            server_error(f"Invalid user ID format: {id_error}")

        # Validate template file
        allowed_content_types = [
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/octet-stream'  # Fallback for some clients
        ]
        allowed_extensions = ['.docx']

        is_valid_file, file_error = validate_file(
            template,
            max_size_mb=5.0,  # 5MB max size
            allowed_content_types=allowed_content_types,
            allowed_extensions=allowed_extensions
        )

        if not is_valid_file:
            if file_error is None:
                file_error = "Invalid file"
            validation_error(file_error, "template")

        # Read file content
        content = await template.read()
        logger.info(f"Read template file: {template.filename}, size: {len(content)} bytes")

        # Encode file content to base64
        encoded_content = base64.b64encode(content).decode('utf-8')

        # For user's own profile, we don't need ownership verification
        # Just fetch the user document directly since we already have the authenticated user
        query = sanitize_find_query({"_id": ObjectId(user_id)})
        user_doc = await db.users.find_one(query)

        if not user_doc:
            logger.error(f"User document not found for ID: {user_id}")
            not_found_error("User not found", "user", user_id)

        has_existing_template = user_doc and user_doc.get("has_template", False)
        logger.info(f"User has existing template: {has_existing_template}")

        # Update user document with template
        filter_query = sanitize_find_query({"_id": ObjectId(user_id)})
        update_query = sanitize_update_query({
            "$set": {
                "notice_template": encoded_content,
                "has_template": True,
                "template_updated_at": datetime.now(timezone.utc),
                "template_filename": sanitize_text(template.filename)
            }
        })

        result = await db.users.update_one(filter_query, update_query)

        if result.modified_count == 0:
            logger.warning(f"No document was modified when updating user {user_id}")
            # If no document was modified but the user exists, it might be that the template is the same
            # We'll return success anyway since we confirmed the user exists
            if user_doc:
                message = "Template updated successfully"
                return {"success": True, "message": message}
            else:
                not_found_error("User not found", "user", user_id)

        message = "Template replaced successfully" if has_existing_template else "Template uploaded successfully"
        return {"success": True, "message": message}
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error uploading template: {e}", exc_info=True)
        server_error(f"Failed to upload template: {str(e)}")

@router.get("/users/template")
async def get_template(user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    """Get the user's notice template"""
    try:
        # Validate user object
        if not isinstance(user, dict):
            server_error("Invalid user object format")

        # Get user ID - handle both formats (_id or id)
        user_id = None
        if "_id" in user:
            user_id = str(user["_id"])
        elif "id" in user:
            user_id = user["id"]
        else:
            # Log the user object for debugging
            logger.error(f"User object missing ID field: {user}")
            server_error("User ID not found in user object")

        # Validate user_id
        is_valid_id, _, id_error = validate_string(
            user_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            server_error(f"Invalid user ID format: {id_error}")

        logger.info(f"Getting template for user ID: {user_id}")

        # For user's own template, we don't need ownership verification
        # Just fetch the user document directly with sanitized query
        query = sanitize_find_query({"_id": ObjectId(user_id)})
        user_doc = await db.users.find_one(query)

        if not user_doc:
            logger.error(f"User document not found for ID: {user_id}")
            not_found_error("User not found", "user", user_id)

        # Check if user has a template
        if not user_doc.get("has_template", False) or not user_doc.get("notice_template"):
            logger.info(f"User {user_id} has no template")
            return {"has_template": False, "template": None}

        logger.info(f"Retrieved template for user {user_id}")

        # Sanitize the filename before returning
        template_filename = sanitize_text(user_doc.get("template_filename", "template.docx"))

        return {
            "has_template": True,
            "template": user_doc["notice_template"],
            "template_filename": template_filename,
            "updated_at": user_doc.get("template_updated_at")
        }
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error retrieving template: {e}", exc_info=True)
        server_error(f"Failed to retrieve template: {str(e)}")

@router.delete("/users/template")
async def delete_template(user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    """Delete the user's notice template"""
    try:
        # Validate user object
        if not isinstance(user, dict):
            server_error("Invalid user object format")

        # Get user ID - handle both formats (_id or id)
        user_id = None
        if "_id" in user:
            user_id = str(user["_id"])
            logger.info(f"Using _id from user object: {user_id}")
        elif "id" in user:
            user_id = user["id"]
            logger.info(f"Using id from user object: {user_id}")
        else:
            # Log the user object for debugging
            logger.error(f"User object missing ID field: {user}")
            server_error("User ID not found in user object")

        # Validate user_id
        is_valid_id, _, id_error = validate_string(
            user_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            server_error(f"Invalid user ID format: {id_error}")

        # For user's own template, we don't need ownership verification
        # Just fetch the user document directly with sanitized query
        query = sanitize_find_query({"_id": ObjectId(user_id)})
        user_doc = await db.users.find_one(query)

        if not user_doc:
            logger.error(f"User document not found for ID: {user_id}")
            not_found_error("User not found", "user", user_id)

        if not user_doc.get("has_template", False):
            logger.warning(f"User {user_id} has no template to delete")
            return {"success": True, "message": "No template exists to delete"}

        # Update user document to remove template with sanitized queries
        filter_query = sanitize_find_query({"_id": ObjectId(user_id)})
        update_query = sanitize_update_query({
            "$set": {
                "has_template": False
            },
            "$unset": {
                "notice_template": "",
                "template_updated_at": "",
                "template_filename": ""
            }
        })

        result = await db.users.update_one(filter_query, update_query)

        if result.modified_count == 0:
            logger.warning(f"No document was modified when deleting template for user {user_id}")
            # If we confirmed the user exists above, but nothing was modified,
            # it might be a database consistency issue
            return {"success": True, "message": "No changes were made"}

        logger.info(f"Template deleted successfully for user {user_id}")
        return {"success": True, "message": "Template deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error deleting template: {e}", exc_info=True)
        server_error(f"Failed to delete template: {str(e)}")

@router.get("/users/profile")
async def get_user_profile(user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    """Get the user's profile information"""
    try:
        # Validate user object
        if not isinstance(user, dict):
            logger.error("Invalid user object format")
            raise HTTPException(status_code=500, detail="Invalid user object format")

        # Get user ID - handle both formats (_id or id)
        user_id = None
        if "_id" in user:
            user_id = str(user["_id"])
            logger.info(f"Using _id from user object: {user_id}")
        elif "id" in user:
            user_id = user["id"]
            logger.info(f"Using id from user object: {user_id}")
        else:
            # Log the user object for debugging
            logger.error(f"User object missing ID field: {user}")
            raise HTTPException(status_code=500, detail="User ID not found in user object")

        # Validate user_id - simplified validation
        if not user_id or not isinstance(user_id, str):
            logger.error(f"Invalid user ID format: {user_id}")
            raise HTTPException(status_code=500, detail="Invalid user ID format")

        # For user's own profile, we don't need ownership verification
        # Just fetch the user document directly
        try:
            query = {"_id": ObjectId(user_id)}
            user_doc = await db.users.find_one(query)
        except Exception as e:
            logger.error(f"Error querying database: {str(e)}")
            raise HTTPException(status_code=500, detail="Database error")

        if not user_doc:
            logger.error(f"User document not found for ID: {user_id}")
            raise HTTPException(status_code=404, detail="User not found")

        # Return user profile information with safe defaults
        return {
            "id": str(user_doc["_id"]),
            "email": user_doc.get("email", ""),
            "firstName": user_doc.get("first_name", ""),
            "lastName": user_doc.get("last_name", ""),
            "designation": user_doc.get("designation", ""),
            "organization": user_doc.get("organization", ""),
            "emailVerified": bool(user_doc.get("email_verified", False)),
            "hasTemplate": bool(user_doc.get("has_template", False)),
            "twoStepEnabled": bool(user_doc.get("two_step_enabled", False)),
            "paid": bool(user_doc.get("paid", False)),
            "subscription_start_date": user_doc.get("subscription_start_date"),
            "subscription_expires": user_doc.get("subscription_expires"),
            "complaint_count": user_doc.get("complaint_count", 0)
        }
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error retrieving user profile: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/users/profile")
async def update_user_profile(
    profile_data: UserProfileUpdate,
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """Update the user's profile information"""
    try:
        # Validate user object
        if not isinstance(user, dict):
            server_error("Invalid user object format")

        # Get user ID - handle both formats (_id or id)
        user_id = None
        if "_id" in user:
            user_id = str(user["_id"])
            logger.info(f"Using _id from user object: {user_id}")
        elif "id" in user:
            user_id = user["id"]
            logger.info(f"Using id from user object: {user_id}")
        else:
            # Log the user object for debugging
            logger.error(f"User object missing ID field: {user}")
            server_error("User ID not found in user object")

        # Validate user_id
        is_valid_id, _, id_error = validate_string(
            user_id, min_length=1, max_length=50, pattern=r'^[0-9a-zA-Z_\-]+$'
        )
        if not is_valid_id:
            server_error(f"Invalid user ID format: {id_error}")

        # For user's own profile, we don't need ownership verification
        # Just fetch the user document directly with sanitized query
        query = sanitize_find_query({"_id": ObjectId(user_id)})
        user_doc = await db.users.find_one(query)

        if not user_doc:
            logger.error(f"User document not found for ID: {user_id}")
            not_found_error("User not found", "user", user_id)

        # Prepare update data with sanitized values
        update_data = {}
        if profile_data.firstName is not None:
            # Validate and sanitize firstName
            is_valid_first_name, sanitized_first_name, first_name_error = validate_string(
                profile_data.firstName, min_length=0, max_length=100
            )
            if not is_valid_first_name:
                if first_name_error is None:
                    first_name_error = "Invalid first name"
                validation_error(first_name_error, "firstName")
            update_data["first_name"] = sanitized_first_name

        if profile_data.lastName is not None:
            # Validate and sanitize lastName
            is_valid_last_name, sanitized_last_name, last_name_error = validate_string(
                profile_data.lastName, min_length=0, max_length=100
            )
            if not is_valid_last_name:
                if last_name_error is None:
                    last_name_error = "Invalid last name"
                validation_error(last_name_error, "lastName")
            update_data["last_name"] = sanitized_last_name

        if profile_data.designation is not None:
            # Validate and sanitize designation
            is_valid_designation, sanitized_designation, designation_error = validate_string(
                profile_data.designation, min_length=0, max_length=200
            )
            if not is_valid_designation:
                if designation_error is None:
                    designation_error = "Invalid designation"
                validation_error(designation_error, "designation")
            update_data["designation"] = sanitized_designation

        if profile_data.organization is not None:
            # Validate and sanitize organization
            is_valid_organization, sanitized_organization, organization_error = validate_string(
                profile_data.organization, min_length=0, max_length=200
            )
            if not is_valid_organization:
                if organization_error is None:
                    organization_error = "Invalid organization"
                validation_error(organization_error, "organization")
            update_data["organization"] = sanitized_organization

        # Add updated timestamp
        update_data["profile_updated_at"] = datetime.now(timezone.utc)

        # Update user document with sanitized queries
        filter_query = sanitize_find_query({"_id": ObjectId(user_id)})
        update_query = sanitize_update_query({"$set": update_data})

        result = await db.users.update_one(filter_query, update_query)

        if result.modified_count == 0:
            logger.warning(f"No document was modified when updating profile for user {user_id}")
            # If we confirmed the user exists above, but nothing was modified,
            # it might be that the profile data is the same
            return {"success": True, "message": "No changes were made to profile"}

        logger.info(f"Profile updated successfully for user {user_id}")
        return {"success": True, "message": "Profile updated successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}", exc_info=True)
        server_error(f"Failed to update user profile: {str(e)}")
