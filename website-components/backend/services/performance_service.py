import asyncio
import logging
import time
import psutil
import os
from typing import Dict, Any, Optional, Callable
from concurrent.futures import Thread<PERSON>oolExecutor
from functools import wraps
import threading
from contextlib import contextmanager

# Configure logging
logger = logging.getLogger(__name__)

# Thread pool for CPU-intensive operations
# Size based on CPU cores but capped for memory efficiency
CPU_CORES = os.cpu_count() or 4
THREAD_POOL_SIZE = min(CPU_CORES * 2, 8)  # Max 8 threads to prevent memory issues
cpu_thread_pool = ThreadPoolExecutor(max_workers=THREAD_POOL_SIZE, thread_name_prefix="cpu_worker")

# Performance metrics
performance_metrics = {
    "active_requests": 0,
    "total_requests": 0,
    "avg_response_time": 0.0,
    "peak_memory_usage": 0,
    "active_threads": 0,
    "database_connections": 0,
    "file_operations": 0,
    "last_updated": time.time()
}

# Thread-safe counter for active requests
request_counter_lock = threading.Lock()

class PerformanceMonitor:
    """Monitor and track application performance metrics"""
    
    def __init__(self):
        self.request_times = []
        self.max_request_history = 1000  # Keep last 1000 requests for averaging
    
    def start_request(self):
        """Mark the start of a request"""
        with request_counter_lock:
            performance_metrics["active_requests"] += 1
            performance_metrics["total_requests"] += 1
        return time.time()
    
    def end_request(self, start_time: float):
        """Mark the end of a request and update metrics"""
        end_time = time.time()
        request_duration = end_time - start_time
        
        with request_counter_lock:
            performance_metrics["active_requests"] = max(0, performance_metrics["active_requests"] - 1)
            
            # Update average response time
            self.request_times.append(request_duration)
            if len(self.request_times) > self.max_request_history:
                self.request_times.pop(0)
            
            performance_metrics["avg_response_time"] = sum(self.request_times) / len(self.request_times)
            performance_metrics["last_updated"] = end_time
    
    def update_system_metrics(self):
        """Update system-level performance metrics"""
        try:
            # Memory usage
            process = psutil.Process()
            memory_info = process.memory_info()
            performance_metrics["peak_memory_usage"] = max(
                performance_metrics["peak_memory_usage"],
                memory_info.rss / 1024 / 1024  # Convert to MB
            )
            
            # Thread count
            performance_metrics["active_threads"] = threading.active_count()
            
        except Exception as e:
            logger.warning(f"Error updating system metrics: {e}")

# Global performance monitor instance
perf_monitor = PerformanceMonitor()

@contextmanager
def monitor_performance(operation_name: str):
    """Context manager to monitor performance of operations"""
    start_time = time.time()

    with request_counter_lock:
        performance_metrics["active_requests"] += 1

    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time

        with request_counter_lock:
            performance_metrics["active_requests"] = max(0, performance_metrics["active_requests"] - 1)

            # Log slow operations
            if duration > 2.0:
                logger.warning(f"Slow operation detected: {operation_name} took {duration:.2f}s")

def monitor_performance_decorator(func):
    """Decorator to monitor function performance"""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = perf_monitor.start_request()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            perf_monitor.end_request(start_time)
            perf_monitor.update_system_metrics()
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = perf_monitor.start_request()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            perf_monitor.end_request(start_time)
            perf_monitor.update_system_metrics()
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

async def run_in_thread_pool(func: Callable, *args, **kwargs):
    """
    Run CPU-intensive operations in a thread pool to avoid blocking the event loop
    
    Args:
        func: The function to run
        *args: Arguments for the function
        **kwargs: Keyword arguments for the function
    
    Returns:
        The result of the function
    """
    loop = asyncio.get_event_loop()
    
    # Increment file operations counter
    performance_metrics["file_operations"] += 1
    
    try:
        result = await loop.run_in_executor(cpu_thread_pool, func, *args, **kwargs)
        return result
    except Exception as e:
        logger.error(f"Error in thread pool execution: {e}")
        raise
    finally:
        # Decrement file operations counter
        performance_metrics["file_operations"] = max(0, performance_metrics["file_operations"] - 1)

async def process_large_file_async(file_path: str, processor_func: Callable, chunk_size: int = 1000):
    """
    Process large files in chunks to avoid memory issues
    
    Args:
        file_path: Path to the file to process
        processor_func: Function to process each chunk
        chunk_size: Number of rows to process at once
    
    Returns:
        List of processed results
    """
    results = []
    
    try:
        # Use thread pool for file reading
        def read_file_chunk(start_row: int, end_row: int):
            # This would be implemented based on file type (CSV, Excel, etc.)
            # For now, this is a placeholder
            return processor_func(file_path, start_row, end_row)
        
        # Process in chunks
        current_row = 0
        while True:
            chunk_result = await run_in_thread_pool(
                read_file_chunk, 
                current_row, 
                current_row + chunk_size
            )
            
            if not chunk_result:  # No more data
                break
                
            results.extend(chunk_result)
            current_row += chunk_size
            
            # Yield control to allow other operations
            await asyncio.sleep(0.01)
    
    except Exception as e:
        logger.error(f"Error processing large file {file_path}: {e}")
        raise
    
    return results

def get_performance_metrics() -> Dict[str, Any]:
    """Get current performance metrics"""
    perf_monitor.update_system_metrics()
    
    # Add system information
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        return {
            **performance_metrics,
            "system": {
                "cpu_usage_percent": cpu_percent,
                "memory_usage_percent": memory.percent,
                "available_memory_mb": memory.available / 1024 / 1024,
                "total_memory_mb": memory.total / 1024 / 1024
            },
            "thread_pool": {
                "max_workers": THREAD_POOL_SIZE,
                "active_file_operations": performance_metrics["file_operations"]
            }
        }
    except Exception as e:
        logger.warning(f"Error getting system metrics: {e}")
        return performance_metrics

async def optimize_database_query(query_func: Callable, *args, **kwargs):
    """
    Optimize database queries with connection pooling awareness
    
    Args:
        query_func: The database query function
        *args: Arguments for the query
        **kwargs: Keyword arguments for the query
    
    Returns:
        Query result
    """
    start_time = time.time()
    
    try:
        # Increment database connection counter
        performance_metrics["database_connections"] += 1
        
        result = await query_func(*args, **kwargs)
        
        query_time = time.time() - start_time
        if query_time > 1.0:  # Log slow queries
            logger.warning(f"Slow database query detected: {query_time:.2f}s")
        
        return result
    
    except Exception as e:
        logger.error(f"Database query error: {e}")
        raise
    finally:
        # Decrement database connection counter
        performance_metrics["database_connections"] = max(0, performance_metrics["database_connections"] - 1)

class ResourceLimiter:
    """Limit resource usage to prevent server overload"""
    
    def __init__(self, max_concurrent_operations: int = 10):
        self.max_concurrent_operations = max_concurrent_operations
        self.semaphore = asyncio.Semaphore(max_concurrent_operations)
    
    async def limit_operation(self, operation_func: Callable, *args, **kwargs):
        """
        Limit concurrent operations to prevent server overload
        
        Args:
            operation_func: The operation to limit
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
        
        Returns:
            Operation result
        """
        async with self.semaphore:
            return await operation_func(*args, **kwargs)

# Global resource limiters for different operation types
file_upload_limiter = ResourceLimiter(max_concurrent_operations=5)  # Max 5 concurrent file uploads
graph_generation_limiter = ResourceLimiter(max_concurrent_operations=3)  # Max 3 concurrent graph generations
notice_generation_limiter = ResourceLimiter(max_concurrent_operations=8)  # Max 8 concurrent notice generations

async def cleanup_performance_resources():
    """Clean up performance monitoring resources"""
    try:
        # Shutdown thread pool
        cpu_thread_pool.shutdown(wait=True)
        logger.info("Performance monitoring resources cleaned up")
    except Exception as e:
        logger.error(f"Error cleaning up performance resources: {e}")
