import os
import jwt
import uuid
import logging
from typing import Optional, Tu<PERSON>, Dict, Any
from datetime import datetime, timedelta, timezone
from fastapi import HTT<PERSON>Exception, Request
from passlib.context import CryptContext
from dotenv import load_dotenv
from database import get_db

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Get secret key from environment variables
SECRET_KEY = os.getenv("SECRET_KEY", "")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY must be set in environment variables")

# JWT settings
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days for access token (persist until explicit logout)
REFRESH_TOKEN_EXPIRE_DAYS = 30    # 30 days for refresh token

# Security settings
MAX_FAILED_LOGIN_ATTEMPTS = 5
ACCOUNT_LOCKOUT_MINUTES = 15
PROGRESSIVE_LOCKOUT = True

# Session settings
ENFORCE_SINGLE_SESSION = True     # Only allow one active session per user across different browsers/devices
ALLOW_MULTIPLE_TABS = True        # Allow multiple tabs in the same browser
SESSION_CONFLICT_RESOLUTION = "NOTIFY"  # Options: "TERMINATE_OLD", "NOTIFY"
SESSION_TIMEOUT_MINUTES = 60 * 24 * 7  # Session persists until explicit logout or browser closure

# Password hashing
pwd_context = CryptContext(
    schemes=["argon2", "bcrypt"],
    deprecated="auto",
    argon2__time_cost=3,  # Number of iterations
    argon2__memory_cost=65536,  # Memory usage in KiB
    argon2__parallelism=4,  # Number of parallel threads
    argon2__hash_len=32  # Length of the hash in bytes
)

def get_password_hash(password: str) -> str:
    """Hash a password using Argon2"""
    return pwd_context.hash(password)

async def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash and migrate if needed"""
    is_valid = pwd_context.verify(plain_password, hashed_password)

    # If password is valid and using old hash scheme, rehash it
    if is_valid and pwd_context.needs_update(hashed_password):
        try:
            # Get database connection
            db = get_db()
            if db is None:
                logger.error("Database connection error during password rehash")
                return is_valid

            # Find user by hashed password
            user = await db.users.find_one({"hashed_password": hashed_password})
            if user:
                # Generate new hash
                new_hash = get_password_hash(plain_password)
                # Update user's password hash
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": {"hashed_password": new_hash}}
                )
                logger.info("Successfully migrated password hash")
        except Exception as e:
            logger.error(f"Error migrating password hash: {str(e)}")

    return is_valid

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create an access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "type": "access", "jti": str(uuid.uuid4())})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create a refresh token"""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh", "jti": str(uuid.uuid4())})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_tokens(user_email: str) -> Tuple[str, str]:
    """Create both access and refresh tokens"""
    access_token = create_access_token({"sub": user_email})
    refresh_token = create_refresh_token({"sub": user_email})
    return access_token, refresh_token

async def blacklist_token(token: str, token_type: str) -> bool:
    """
    Token blacklisting functionality removed for simplicity
    This function now just returns True without doing anything
    """
    # Just log the attempt but don't actually blacklist
    logger.info(f"Token blacklisting disabled - would have blacklisted a {token_type} token")
    return True

async def verify_token(token: str, token_type: str = "access") -> dict:
    """Verify a token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # Verify token type
        if payload.get("type") != token_type:
            raise HTTPException(status_code=401, detail=f"Invalid token type: expected {token_type}")

        # Token blacklisting check removed for simplicity

        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

async def get_current_user(request: Request, check_session_conflict=True) -> dict:
    """
    Get the current user from the request with enhanced session management

    Args:
        request: The request object
        check_session_conflict: Whether to check for session conflicts

    Returns:
        dict: User object

    Raises:
        HTTPException: If authentication fails
    """
    # Try to get token from cookies first (preferred method)
    token = request.cookies.get("access_token")

    # If not in cookies, try Authorization header (for backward compatibility)
    if not token:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.replace("Bearer ", "")

    if not token:
        logger.warning("Authentication required: No access token found in cookies or Authorization header.")
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )

    try:
        payload = await verify_token(token, "access")
        user_email = payload.get("sub")

        if not user_email:
            logger.warning("Invalid token: missing subject")
            raise HTTPException(status_code=401, detail="Authentication failed")

        db = get_db()
        if db is None:
            logger.error("Database connection error in get_current_user.")
            raise HTTPException(status_code=500, detail="Database connection error")

        user = await db.users.find_one({"email": user_email})
        if not user:
            logger.warning("User not found from token")
            raise HTTPException(status_code=401, detail="Authentication failed")

        # Get current session ID from request
        current_session_id = request.cookies.get("session_id") or request.headers.get("X-Session-ID")

        # Create browser fingerprint for session tracking (simplified but effective)
        user_agent = request.headers.get("User-Agent", "")
        client_ip = request.client.host if request.client else "unknown"
        browser_fingerprint = f"{user_agent}:{client_ip}"

        logger.debug(f"get_current_user: Token present: {bool(token)}, Session ID present: {bool(current_session_id)}")

        # Enhanced session conflict checking for single-session enforcement
        if ENFORCE_SINGLE_SESSION and check_session_conflict and current_session_id:
            active_session = await get_user_active_session(db, user["_id"])

            # Check for session conflicts with proper browser/device detection
            if active_session:
                active_session_id = active_session["session_id"]
                active_fingerprint = active_session.get("browser_fingerprint", "")

                # Determine if this is a different browser/device
                is_different_browser = (active_fingerprint != browser_fingerprint)
                is_different_session = (active_session_id != current_session_id)

                # Only trigger conflict for different browsers/devices, not different tabs
                if is_different_session and is_different_browser:
                    if SESSION_CONFLICT_RESOLUTION == "TERMINATE_OLD":
                        # Automatically terminate old session and continue with new one
                        await update_user_last_access(
                            db, user["_id"], user_email,
                            datetime.now(timezone.utc), current_session_id, browser_fingerprint
                        )
                        logger.info("Terminated old session from different browser/device")
                    else:
                        # Notify about session conflict from different browser/device
                        logger.warning("Session conflict detected: different browser/device")
                        raise HTTPException(
                            status_code=409,  # Conflict
                            detail={
                                "message": "Another session is active on a different browser/device",
                                "code": "SESSION_CONFLICT",
                                "conflict_type": "DIFFERENT_BROWSER",
                                "active_session": {
                                    "id": active_session_id,
                                    "last_active": active_session["last_updated"].isoformat()
                                        if active_session.get("last_updated") else None
                                }
                            }
                        )
                else:
                    # Same browser (multiple tabs allowed) or same session - update last access
                    await update_user_last_access(
                        db, user["_id"], user_email,
                        datetime.now(timezone.utc), current_session_id, browser_fingerprint
                    )
                    if is_different_session and not is_different_browser:
                        logger.debug("Multiple tabs detected - allowed")
            else:
                # No active session - create new one
                await update_user_last_access(
                    db, user["_id"], user_email,
                    datetime.now(timezone.utc), current_session_id, browser_fingerprint
                )
                logger.debug("Created new session for user")
        else:
            # Just update the last access time without session management
            await update_user_last_access(db, user["_id"], user_email, datetime.now(timezone.utc))

        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=500, detail="Authentication failed")

async def update_user_last_access(db, user_id, _user_email, timestamp, session_id=None, browser_fingerprint=None):
    """
    Update user's last access time and session information

    Args:
        db: Database connection
        user_id: User ID
        _user_email: User email (unused, kept for compatibility)
        timestamp: Current timestamp
        session_id: Current session ID (optional)
        browser_fingerprint: Browser fingerprint for session tracking (optional)
    """
    try:
        update_data = {"last_access": timestamp}

        # If session_id is provided, update active session
        if session_id:
            update_data["active_session_id"] = session_id
            update_data["active_session_updated"] = timestamp

            # Store browser fingerprint for session conflict detection
            if browser_fingerprint:
                update_data["browser_fingerprint"] = browser_fingerprint

        await db.users.update_one(
            {"_id": user_id},
            {"$set": update_data}
        )
    except Exception as e:
        logger.error(f"Failed to update user session: {str(e)}")

async def get_user_active_session(db, user_id):
    """
    Get user's active session information, automatically cleaning up stale sessions

    Args:
        db: Database connection
        user_id: User ID

    Returns:
        dict: Session information or None if no active session
    """
    try:
        user = await db.users.find_one(
            {"_id": user_id},
            {"active_session_id": 1, "active_session_updated": 1, "browser_fingerprint": 1}
        )

        if user and "active_session_id" in user:
            # Check if session is stale (older than 7 days)
            session_timeout_hours = 7 * 24  # 7 days
            stale_threshold = datetime.now(timezone.utc) - timedelta(hours=session_timeout_hours)

            last_updated = user.get("active_session_updated")
            if last_updated:
                if isinstance(last_updated, str):
                    last_updated = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                elif isinstance(last_updated, datetime) and last_updated.tzinfo is None:
                    last_updated = last_updated.replace(tzinfo=timezone.utc)

                # If session is stale, clean it up
                if last_updated < stale_threshold:
                    logger.info(f"Cleaning up stale session for user {user_id}")
                    await db.users.update_one(
                        {"_id": user_id},
                        {
                            "$unset": {
                                "active_session_id": "",
                                "active_session_updated": "",
                                "browser_fingerprint": ""
                            }
                        }
                    )
                    return None

            return {
                "session_id": user["active_session_id"],
                "last_updated": user.get("active_session_updated"),
                "browser_fingerprint": user.get("browser_fingerprint", "")
            }
        return None
    except Exception as e:
        logger.error(f"Error getting user active session: {str(e)}")
        return None

async def clear_user_session(db, user_id):
    """
    Clear user's active session and all related data

    Args:
        db: Database connection
        user_id: User ID
    """
    try:
        # Clear all session-related fields
        await db.users.update_one(
            {"_id": user_id},
            {"$unset": {
                "active_session_id": "",
                "active_session_updated": "",
                "browser_fingerprint": "",
                "last_access": "",
                "login_otp": "",
                "login_otp_expires": "",
                "otp_failed_attempts": "",
                "last_failed_login": "",
                "failed_login_attempts": "",
                "account_locked_until": ""
            }}
        )

        # Clear all refresh tokens for this user
        await db.user_refresh_tokens.delete_many({"user_id": user_id})

        logger.info("Cleared all session data and refresh tokens for user")
        return True
    except Exception as e:
        logger.error(f"Error clearing user session: {str(e)}")
        return False

async def clear_all_user_sessions(db, user_id):
    """
    Clear all sessions and tokens for a user across all devices
    This is useful for forced logout from all devices

    Args:
        db: Database connection
        user_id: User ID
    """
    try:
        # Clear all session-related fields
        await db.users.update_one(
            {"_id": user_id},
            {"$unset": {
                "active_session_id": "",
                "active_session_updated": "",
                "browser_fingerprint": "",
                "last_access": "",
                "login_otp": "",
                "login_otp_expires": "",
                "otp_failed_attempts": "",
                "last_failed_login": "",
                "failed_login_attempts": "",
                "account_locked_until": ""
            }}
        )

        # Clear all refresh tokens
        await db.user_refresh_tokens.delete_many({"user_id": user_id})

        logger.info("Cleared all sessions and tokens for user")
        return True
    except Exception as e:
        logger.error(f"Error clearing all user sessions: {str(e)}")
        return False

async def check_account_lockout(db, user_email: str) -> Tuple[bool, Optional[datetime]]:
    """Check if account is locked out"""
    user = await db.users.find_one({"email": user_email})
    if not user:
        return False, None

    failed_attempts = user.get("failed_login_attempts", 0)
    lockout_until = user.get("account_locked_until")

    if lockout_until:
        # Convert lockout_until to timezone-aware datetime if it's naive
        if isinstance(lockout_until, datetime) and lockout_until.tzinfo is None:
            lockout_until = lockout_until.replace(tzinfo=timezone.utc)

        if datetime.now(timezone.utc) < lockout_until:
            return True, lockout_until

    if failed_attempts >= MAX_FAILED_LOGIN_ATTEMPTS:
        lockout_minutes = ACCOUNT_LOCKOUT_MINUTES
        if PROGRESSIVE_LOCKOUT:
            lockout_minutes *= (2 ** (failed_attempts - MAX_FAILED_LOGIN_ATTEMPTS))

        lockout_until = datetime.now(timezone.utc) + timedelta(minutes=lockout_minutes)
        await db.users.update_one(
            {"email": user_email},
            {"$set": {"account_locked_until": lockout_until}}
        )
        return True, lockout_until

    return False, None
