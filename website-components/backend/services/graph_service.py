from typing import Dict, <PERSON>, Any, Tu<PERSON>, Union
import logging

def sync_metadata_to_complaint(complaint_id: str, metadata: Dict[str, Any], db) -> bool:
    """
    Helper function to sync metadata from graph_data to complaint model's metadata field.
    This ensures dashboard and summary pages have access to the metadata.

    Args:
        complaint_id: The complaint ID
        metadata: The metadata to sync
        db: Database connection

    Returns:
        bool: True if sync was successful, False otherwise
    """
    logger = logging.getLogger(__name__)
    try:
        import copy
        from bson import ObjectId

        # Try to find the complaint by ID or complaint number
        query = {}
        try:
            # Check if complaint_id is a valid ObjectId
            query = {"_id": ObjectId(complaint_id)}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id}

        # Update the complaint's metadata field
        result = db.complaints.update_one(
            query,
            {"$set": {"metadata": copy.deepcopy(metadata)}}
        )

        if result.modified_count > 0:
            logger.info(f"Successfully synced metadata to complaint {complaint_id} with keys: {', '.join(metadata.keys())}")
            return True
        else:
            logger.warning(f"No complaint found or no changes made when syncing metadata for {complaint_id}")
            return False

    except Exception as e:
        logger.error(f"Error syncing metadata to complaint {complaint_id}: {e}")
        return False

def generate_graph_data(transactions: List[Dict[str, Any]], metadata: Dict[str, Any], max_layer: int = 7) -> Dict[str, Any]:
    """
    Generate graph data from transactions for frontend visualization.

    Args:
        transactions: List of transaction objects
        metadata: Dictionary containing complaint metadata
        max_layer: Maximum layer to include in the graph (0-7)

    Returns:
        Dictionary containing transactions and metadata
    """
    # Filter transactions by max_layer - include layer 0 transactions too
    filtered_transactions = []
    for txn in transactions:
        if "layer" in txn:
            layer_value = txn.get("layer", 0)
            # Convert layer value to int if it's a string
            if isinstance(layer_value, str):
                try:
                    layer_value = int(layer_value)
                except (ValueError, TypeError):
                    # If conversion fails, use default layer 0
                    layer_value = 0
            # Only include transactions with layer <= max_layer
            if layer_value <= max_layer:
                filtered_transactions.append(txn)

    # Ensure all transactions have a layer value
    for txn in filtered_transactions:
        if "layer" not in txn:
            txn["layer"] = 0

    return {
        "transactions": filtered_transactions,
        "metadata": metadata
    }

def process_csv_data(csv_data: str, metadata: Dict[str, Any] | None = None) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """
    Process CSV data and generate transactions and graph data

    Args:
        csv_data: CSV data - can be base64 encoded or raw CSV string
        metadata: Optional metadata to include with graph data

    Returns:
        Tuple of (transactions, graph_data)
    """
    import base64
    import logging
    from services.csv_service import parse_csv_to_transactions, base64_to_csv
    import copy
    import json
    import traceback
    import re

    logger = logging.getLogger(__name__)

    # Make a deep copy of the metadata to avoid modifying the original
    if metadata is not None:
        # Create a deep copy to ensure we don't modify the original
        metadata_copy = copy.deepcopy(metadata)
        logger.info(f"Using provided metadata: {metadata_copy}")
    else:
        # Default metadata if not provided
        metadata_copy = {
            "complainant_name": "CSV Import",
            "complaint_number": "CSV-IMPORT",
            "date": "",
            "total_amount": "",
            # Explicitly set empty address fields for CSV imports
            "address": {
                "house_no": "",
                "street": "",
                "colony": "",
                "village_town_city": "",
                "tehsil": "",
                "district": "",
                "state": "",
                "country": "",
                "police_station": "",
                "pin_code": ""
            }
        }
        logger.info("Using default metadata with empty address fields")

    # Convert the input to CSV data
    # Check if the data is base64 encoded
    is_base64 = False
    csv_data_str = ""

    try:
        # Try to decode as base64
        if isinstance(csv_data, str):
            # Check if it looks like base64
            if re.match(r'^[A-Za-z0-9+/]+={0,2}$', csv_data):
                try:
                    decoded_data = base64.b64decode(csv_data).decode('utf-8')
                    is_base64 = True
                    csv_data_str = decoded_data
                    logger.info("Successfully decoded base64 data")
                except:
                    logger.warning("Data matched base64 pattern but could not be decoded")
                    is_base64 = False
    except Exception as e:
        logger.error(f"Error checking if data is base64: {str(e)}")
        is_base64 = False

    # If not base64, check if it's raw CSV
    if not is_base64:
        if isinstance(csv_data, str) and ',' in csv_data and ('Layer' in csv_data or 'Txn Type' in csv_data):
            logger.info("Input appears to be raw CSV data, using as-is")
            csv_data_str = csv_data
        else:
            # Try base64_to_csv as a fallback
            logger.info("Trying base64_to_csv as fallback")
            csv_data_str = base64_to_csv(csv_data)

            if not csv_data_str:
                # Last resort - just use the input as-is
                logger.warning("All conversion attempts failed, using input as-is")
                csv_data_str = csv_data if isinstance(csv_data, str) else str(csv_data)

    # Parse CSV to transactions
    try:
        transactions = parse_csv_to_transactions(csv_data_str)
        logger.info(f"Parsed {len(transactions)} transactions from CSV data")

        # Ensure all transactions have consistent structure
        for txn in transactions:
            # Ensure layer is present and valid
            if 'layer' not in txn or txn['layer'] is None:
                txn['layer'] = 0

            # Ensure fraud_type is present
            if 'fraud_type' not in txn or not txn['fraud_type']:
                txn['fraud_type'] = 'banking_upi'

            # Ensure txn_type is present
            if 'txn_type' not in txn:
                # Try to use type as fallback
                if 'type' in txn and txn['type']:
                    txn['txn_type'] = txn['type']
                else:
                    txn['txn_type'] = 'Unknown'

            # For backward compatibility, ensure type is also present
            if 'type' not in txn or not txn['type']:
                txn['type'] = txn['txn_type']

            # Ensure date is present and is a string
            if 'date' not in txn or txn['date'] is None:
                txn['date'] = ''
            elif not isinstance(txn['date'], str):
                txn['date'] = str(txn['date'])

            # Ensure amount is present and is a string
            if 'amount' not in txn or txn['amount'] is None:
                txn['amount'] = ''
            elif not isinstance(txn['amount'], str):
                txn['amount'] = str(txn['amount'])

            # Set isMainTransaction flag for 'Money Transfer to' transactions
            if txn.get('txn_type', '').lower() == 'money transfer to':
                txn['isMainTransaction'] = True
            else:
                txn['isMainTransaction'] = False

        # Generate graph data - use the metadata exactly as provided
        graph_data = generate_graph_data(transactions, metadata_copy)
        logger.info("Generated graph data with preserved metadata")

        # Ensure graph_data is a dictionary
        if isinstance(graph_data, str):
            try:
                graph_data = json.loads(graph_data)
                logger.info("Converted graph_data string to dictionary")
            except Exception as e:
                logger.error(f"Error converting graph_data string to dictionary: {str(e)}")
                graph_data = {"metadata": metadata_copy, "transactions": transactions}

        # IMPORTANT: Ensure metadata is properly stored in graph_data
        # This is the source of truth for metadata that will be synced to complaint model
        if isinstance(graph_data, dict):
            graph_data["metadata"] = metadata_copy
            logger.info(f"Ensured metadata is stored in graph_data with keys: {list(metadata_copy.keys())}")

        # Return both transactions and graph_data
        # The calling function will handle copying metadata to the separate field
        logger.info(f"Returning graph_data with metadata keys: {list(graph_data.get('metadata', {}).keys())}")
        return transactions, graph_data
    except Exception as e:
        logger.error(f"Error processing CSV data: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return empty data instead of raising an exception to make the function more robust
        return [], {"metadata": metadata_copy or {}, "transactions": []}
