"""
Operation Protection Service

This service provides brute force protection for resource-intensive operations
beyond authentication, such as notice generation, batch downloads, and data uploads.

It tracks operation attempts and applies progressive penalties for excessive usage.
"""

import logging
import os
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Tuple, Optional
from database import get_db

# Setup logging
logger = logging.getLogger(__name__)

# Operation protection settings
MAX_OPERATION_ATTEMPTS = {
    "notice_generation": int(os.getenv("MAX_NOTICE_GENERATION_ATTEMPTS", "20")),  # 20 attempts per hour
    "batch_download": int(os.getenv("MAX_BATCH_DOWNLOAD_ATTEMPTS", "10")),       # 10 attempts per hour
    "data_upload": int(os.getenv("MAX_DATA_UPLOAD_ATTEMPTS", "5")),              # 5 attempts per hour
}

# Timeframes for tracking attempts (in minutes)
OPERATION_TIMEFRAMES = {
    "notice_generation": int(os.getenv("NOTICE_GENERATION_TIMEFRAME", "60")),    # 60 minutes
    "batch_download": int(os.getenv("BATCH_DOWNLOAD_TIMEFRAME", "60")),          # 60 minutes
    "data_upload": int(os.getenv("DATA_UPLOAD_TIMEFRAME", "60")),                # 60 minutes
}

# Cooldown periods for operations (in minutes)
OPERATION_COOLDOWNS = {
    "notice_generation": int(os.getenv("NOTICE_GENERATION_COOLDOWN", "10")),     # 10 minutes
    "batch_download": int(os.getenv("BATCH_DOWNLOAD_COOLDOWN", "15")),           # 15 minutes
    "data_upload": int(os.getenv("DATA_UPLOAD_COOLDOWN", "30")),                 # 30 minutes
}

# Progressive cooldown multiplier
PROGRESSIVE_COOLDOWN = os.getenv("PROGRESSIVE_OPERATION_COOLDOWN", "true").lower() == "true"

async def track_operation_attempt(
    user_id: str,
    operation_type: str,
    resource_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Tuple[bool, Optional[str], Optional[int]]:
    """
    Track an operation attempt and check if it should be allowed.

    Args:
        user_id: ID of the user performing the operation
        operation_type: Type of operation (notice_generation, batch_download, data_upload)
        resource_id: Optional ID of the resource being operated on
        metadata: Optional metadata about the operation

    Returns:
        Tuple containing:
        - allowed: Whether the operation is allowed
        - reason: Reason for denial if not allowed
        - cooldown_minutes: Cooldown period in minutes if not allowed
    """
    if operation_type not in MAX_OPERATION_ATTEMPTS:
        logger.warning(f"Unknown operation type: {operation_type}")
        return True, None, None

    try:
        db = get_db()
        if db is None:
            logger.error("Database connection error when tracking operation attempt")
            return True, None, None  # Allow operation on DB error to prevent blocking legitimate users

        # Get current time
        now = datetime.now(timezone.utc)

        # Check if user is in cooldown period
        cooldown_query = {
            "user_id": user_id,
            "operation_type": operation_type,
            "cooldown_until": {"$gt": now}
        }

        cooldown_record = await db.operation_cooldowns.find_one(cooldown_query)
        if cooldown_record:
            # User is in cooldown period
            cooldown_until = cooldown_record["cooldown_until"]

            # Convert cooldown_until to timezone-aware datetime if it's naive
            if isinstance(cooldown_until, datetime) and cooldown_until.tzinfo is None:
                cooldown_until = cooldown_until.replace(tzinfo=timezone.utc)

            remaining_minutes = round((cooldown_until - now).total_seconds() / 60)

            logger.warning(
                f"Operation {operation_type} denied for user {user_id} due to cooldown. "
                f"Remaining: {remaining_minutes} minutes"
            )

            return False, "Operation rate limit exceeded", remaining_minutes

        # Calculate timeframe start
        timeframe_minutes = OPERATION_TIMEFRAMES.get(operation_type, 60)
        timeframe_start = now - timedelta(minutes=timeframe_minutes)

        # Count recent attempts
        attempts_query = {
            "user_id": user_id,
            "operation_type": operation_type,
            "timestamp": {"$gte": timeframe_start}
        }

        attempt_count = await db.operation_attempts.count_documents(attempts_query)

        # Record this attempt
        attempt_record = {
            "user_id": user_id,
            "operation_type": operation_type,
            "timestamp": now,
            "resource_id": resource_id,
            "metadata": metadata or {}
        }

        await db.operation_attempts.insert_one(attempt_record)

        # Check if max attempts exceeded
        max_attempts = MAX_OPERATION_ATTEMPTS.get(operation_type, 10)
        if attempt_count >= max_attempts:
            # Calculate cooldown period
            base_cooldown = OPERATION_COOLDOWNS.get(operation_type, 15)

            # Apply progressive cooldown if enabled
            cooldown_minutes = base_cooldown
            if PROGRESSIVE_COOLDOWN:
                # Get previous cooldown count
                previous_cooldowns = await db.operation_cooldowns.count_documents({
                    "user_id": user_id,
                    "operation_type": operation_type
                })

                # Double the cooldown time for each previous cooldown, up to a maximum of 24 hours
                if previous_cooldowns > 0:
                    # Calculate progressive cooldown: base_time * 2^previous_cooldowns
                    cooldown_minutes = min(base_cooldown * (2 ** previous_cooldowns), 24 * 60)
                    logger.warning(
                        f"Progressive cooldown applied for user {user_id} on {operation_type}: "
                        f"{cooldown_minutes} minutes"
                    )

            # Set cooldown period
            cooldown_until = now + timedelta(minutes=cooldown_minutes)
            cooldown_record = {
                "user_id": user_id,
                "operation_type": operation_type,
                "cooldown_until": cooldown_until,
                "created_at": now
            }

            # First, remove any existing cooldowns for this user and operation type
            await db.operation_cooldowns.delete_many({
                "user_id": user_id,
                "operation_type": operation_type
            })

            # Then insert the new cooldown record
            await db.operation_cooldowns.insert_one(cooldown_record)

            logger.warning(
                f"User {user_id} exceeded {operation_type} rate limit with {attempt_count} attempts. "
                f"Cooldown set for {cooldown_minutes} minutes."
            )

            return False, f"Operation rate limit exceeded", cooldown_minutes

        # Operation is allowed
        return True, None, None

    except Exception as e:
        logger.error(f"Error tracking operation attempt: {str(e)}")
        return True, None, None  # Allow operation on error to prevent blocking legitimate users

async def get_user_operation_status(
    user_id: str,
    operation_type: str
) -> Dict[str, Any]:
    """
    Get the current operation status for a user.

    Args:
        user_id: ID of the user
        operation_type: Type of operation

    Returns:
        Dictionary containing operation status information
    """
    try:
        db = get_db()
        if db is None:
            logger.error("Database connection error when getting operation status")
            return {"status": "unknown", "error": "Database connection error"}

        # Get current time
        now = datetime.now(timezone.utc)

        # Check if user is in cooldown period
        cooldown_query = {
            "user_id": user_id,
            "operation_type": operation_type,
            "cooldown_until": {"$gt": now}
        }

        # Find the cooldown record with the latest expiry time
        cooldown_records = await db.operation_cooldowns.find(cooldown_query).sort("cooldown_until", -1).to_list(length=1)

        if cooldown_records and len(cooldown_records) > 0:
            # User is in cooldown period - use the record with the latest expiry
            cooldown_record = cooldown_records[0]
            cooldown_until = cooldown_record["cooldown_until"]

            # Convert cooldown_until to timezone-aware datetime if it's naive
            if isinstance(cooldown_until, datetime) and cooldown_until.tzinfo is None:
                cooldown_until = cooldown_until.replace(tzinfo=timezone.utc)

            remaining_minutes = round((cooldown_until - now).total_seconds() / 60)

            return {
                "status": "cooldown",
                "remaining_minutes": remaining_minutes,
                "cooldown_until": cooldown_until.isoformat()
            }

        # Calculate timeframe start
        timeframe_minutes = OPERATION_TIMEFRAMES.get(operation_type, 60)
        timeframe_start = now - timedelta(minutes=timeframe_minutes)

        # Count recent attempts
        attempts_query = {
            "user_id": user_id,
            "operation_type": operation_type,
            "timestamp": {"$gte": timeframe_start}
        }

        attempt_count = await db.operation_attempts.count_documents(attempts_query)
        max_attempts = MAX_OPERATION_ATTEMPTS.get(operation_type, 10)

        return {
            "status": "active",
            "attempts": attempt_count,
            "max_attempts": max_attempts,
            "remaining_attempts": max_attempts - attempt_count,
            "timeframe_minutes": timeframe_minutes,
            "reset_at": (timeframe_start + timedelta(minutes=timeframe_minutes)).isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting operation status: {str(e)}")
        return {"status": "error", "error": str(e)}

async def cleanup_old_operation_records():
    """
    Clean up old operation records to prevent database bloat.
    This should be called periodically from a scheduled task.
    """
    try:
        db = get_db()
        if db is None:
            logger.error("Database connection error when cleaning up operation records")
            return False

        # Get current time
        now = datetime.now(timezone.utc)

        # Clean up expired cooldowns
        expired_cooldowns_result = await db.operation_cooldowns.delete_many({
            "cooldown_until": {"$lt": now}
        })

        # Clean up duplicate cooldowns (keep only the latest one for each user/operation type)
        # Get all distinct user_id/operation_type combinations
        distinct_combinations = await db.operation_cooldowns.aggregate([
            {
                "$group": {
                    "_id": {
                        "user_id": "$user_id",
                        "operation_type": "$operation_type"
                    },
                    "count": {"$sum": 1}
                }
            },
            {
                "$match": {
                    "count": {"$gt": 1}  # Only get combinations with more than one record
                }
            }
        ]).to_list(length=100)

        # For each combination with duplicates, keep only the latest cooldown record
        for combo in distinct_combinations:
            user_id = combo["_id"]["user_id"]
            operation_type = combo["_id"]["operation_type"]

            # Find all records for this combination, sorted by cooldown_until descending
            records = await db.operation_cooldowns.find({
                "user_id": user_id,
                "operation_type": operation_type
            }).sort("cooldown_until", -1).to_list(length=100)

            # Keep the first record (latest cooldown_until) and delete the rest
            if len(records) > 1:
                # Get IDs of all but the first record
                delete_ids = [record["_id"] for record in records[1:]]
                if delete_ids:
                    await db.operation_cooldowns.delete_many({"_id": {"$in": delete_ids}})

        # Clean up old attempts (older than 7 days)
        old_attempts_cutoff = now - timedelta(days=7)
        old_attempts_result = await db.operation_attempts.delete_many({
            "timestamp": {"$lt": old_attempts_cutoff}
        })

        logger.info(
            f"Cleaned up {expired_cooldowns_result.deleted_count} expired cooldowns and "
            f"{old_attempts_result.deleted_count} old operation attempts"
        )

        return True

    except Exception as e:
        logger.error(f"Error cleaning up operation records: {str(e)}")
        return False
