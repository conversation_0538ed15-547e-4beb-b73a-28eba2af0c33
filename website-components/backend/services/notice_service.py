"""
Notice generation service using python-docx.
This file contains all notice generation functions used in the application.
"""

from datetime import datetime
import logging
import pandas as pd
import base64
import csv
from io import BytesIO, String<PERSON>
from typing import Dict, List, Any, Union
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os
from cryptography.fernet import <PERSON><PERSON>t
from docxtpl import DocxTemplate

logger = logging.getLogger(__name__)

def clean_account_number(account):
    """Clean account numbers by removing prefixes and handling nulls."""
    if not account or pd.isna(account):
        return ""

    # Convert to string and lowercase for case-insensitive replacement
    account_str = str(account)
    account_lower = account_str.lower()

    # Check for common prefixes
    prefixes = [
        "a/c no.-:", "a/c no.:", "a/c no.", "a/c",
        "A/C No.-:", "A/C No.:", "A/C No.", "A/C"
    ]

    # Replace all prefixes
    for prefix in prefixes:
        if prefix.lower() in account_lower:
            account_str = account_str.replace(prefix, "")

    # Remove standalone "-:" prefix that might appear at the beginning
    if account_str.startswith("-:"):
        account_str = account_str[2:]

    # Also check for other variations of the prefix
    if account_str.startswith("- :"):
        account_str = account_str[3:]
    if account_str.startswith("-: "):
        account_str = account_str[3:]

    return account_str.strip()

def process_complainant_name(metadata: Dict[str, Any]) -> str:
    """Handle base64-encoded or encrypted complainant names."""
    complainant_name = metadata.get('complainant_name') or metadata.get('Name') or 'Complainant'

    if isinstance(complainant_name, str) and len(complainant_name) > 100 and '=' in complainant_name:
        logger.warning(f"Base64 string detected in complainant_name: {complainant_name[:20]}...")
        try:
            decoded_bytes = base64.b64decode(complainant_name)
            decoded_name = decoded_bytes.decode('utf-8')
            logger.info(f"Successfully decoded complainant name: {decoded_name}")

            if decoded_name.startswith('gAAAAA'):
                encryption_key = os.environ.get('ENCRYPTION_KEY')
                if encryption_key:
                    f = Fernet(encryption_key.encode())
                    decrypted_name = f.decrypt(decoded_name.encode()).decode()
                    logger.info(f"Successfully decrypted complainant name: {decrypted_name}")
                    return decrypted_name
                else:
                    logger.warning("No encryption key available")
                    return "Complainant"
            return decoded_name
        except Exception as e:
            logger.error(f"Error decoding/decrypting complainant name: {str(e)}")
            return metadata.get('Name') or metadata.get('name') or "Complainant"
    return complainant_name

def generate_bank_structured_notice(transactions: List[Dict[str, Any]], sender_only: bool = True) -> Dict[str, Any]:
    """
    Structure transaction data by sender bank, separating main and sub-transactions.

    Args:
        transactions: List of transaction dictionaries from Excel/CSV.
        sender_only: If True, only include sender banks.

    Returns:
        Dictionary with bank-structured notice data: {bank_name: {main: [], sub: []}}
    """
    bank_structured_data = {}

    # Identify main transactions (Money Transfer to)
    main_txns = [txn for txn in transactions if txn.get("Txn Type") == "Money Transfer to"]
    main_receiver_accounts = set(txn["Receiver Account"] for txn in main_txns if txn["Receiver Account"])

    for txn in transactions:
        bank_name = txn.get("Sender Bank", "")
        if not bank_name:
            continue

        # Skip if not sender bank and sender_only is True
        if sender_only and not any(txn["Sender Bank"] == bank for bank in bank_structured_data):
            continue

        if bank_name not in bank_structured_data:
            bank_structured_data[bank_name] = {"main": [], "sub": []}

        # Main transaction (Money Transfer to)
        if txn.get("Txn Type") == "Money Transfer to":
            bank_structured_data[bank_name]["main"].append({
                "sender_account": clean_account_number(txn.get("Sender Account", "")),
                "receiver_account": clean_account_number(txn.get("Receiver Account", "")),
                "amount": str(txn.get("Amount", "")),
                "txn_id": txn.get("Txn ID", txn.get("Sender Txn ID", "")),
                "date": txn.get("Txn Date", ""),
                "reference": txn.get("Reference", "N/A")
            })
        # Sub-transaction (e.g., Withdrawal, Hold)
        elif txn.get("Sender Account") in main_receiver_accounts:
            # Get transaction type
            txn_type = txn.get("Txn Type", "Other")

            # Skip sub-transactions with types "Transaction Put on Hold" or "Others" (case-insensitive)
            if txn_type.lower() in ["transaction put on hold", "others"]:
                logger.info(f"Skipping excluded sub-transaction type in bank structured notice: {txn_type}")
                continue

            bank_structured_data[bank_name]["sub"].append({
                "sender_account": clean_account_number(txn.get("Sender Account", "")),
                "type": txn_type,
                "amount": str(txn.get("Amount", "")),
                "txn_id": txn.get("Txn ID", txn.get("Sender Txn ID", "")),
                "date": txn.get("Txn Date", ""),
                "reference": txn.get("Reference", "N/A")
            })

    return bank_structured_data

def generate_notice_from_template(
    bank_notice_data: Union[List[Dict[str, Any]], Dict[str, Any]],
    metadata: Dict[str, Any],
    template_content: Union[BytesIO, None] = None,
    bank_name: Union[str, None] = None,
    selected_points: List[str] = []
) -> BytesIO:
    """
    Generate a notice document using a template.

    Args:
        bank_notice_data: Structured bank notice data
        metadata: Complaint metadata
        template_content: Template document as BytesIO
        bank_name: Optional specific bank to generate notice for
        selected_points: Optional list of selected points to include

    Returns:
        BytesIO object containing the generated document
    """
    logger.info(f"Generating notice for bank: {bank_name}")

    try:
        # Log the inputs for debugging
        logger.info(f"Generating notice with: bank_name={bank_name}, metadata keys={list(metadata.keys())}")
        logger.info(f"Has template: {template_content is not None}")
        logger.info(f"Bank notice data type: {type(bank_notice_data)}")

        # If no template is provided, create a default template
        if template_content is None:
            logger.info("Creating default template")
            doc = Document()

            # Add a title
            title = doc.add_heading('NOTICE', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Add recipient info
            doc.add_paragraph('To,\nThe Branch Manager\nBANK_NAME\n\n')

            # Add subject line
            subject = doc.add_paragraph()
            subject.add_run('Subject: ').bold = True
            subject.add_run('Request for information regarding fraudulent transactions - Complaint No. COMPLAINT_NUMBER')

            # Add salutation
            doc.add_paragraph('Sir/Madam,')

            # Add body text
            body = doc.add_paragraph()
            body.add_run('This is with reference to a cyber fraud complaint (No. COMPLAINT_NUMBER) filed by VICTIM_NAME involving fraudulent transactions amounting to Rs. TOTAL_FRAUD_AMOUNT. ')

            # We've removed the victim's card number logic as requested
            fraud_type = metadata.get('fraud_type', '')
            is_card_fraud = fraud_type == 'card'

            if is_card_fraud:
                logger.info(f"Card fraud type detected in notice generation: {fraud_type}")

            # Add a paragraph for the transaction data
            doc.add_paragraph('The following accounts at your bank have been identified as being involved in this fraud:')

            # Add placeholder for transaction table
            doc.add_paragraph('MAIN_TXN_TABLE')

            # Add placeholder for points
            doc.add_paragraph('\nYou are requested to take the following actions:')
            doc.add_paragraph('SELECTED_BANK_POINTS')

            # Add closing
            doc.add_paragraph('\nPlease treat this matter as urgent and provide the requested information at the earliest.')
            doc.add_paragraph('\nThank you for your cooperation.')

            # Add signature
            closing = doc.add_paragraph('\n\nYours sincerely,\n\n\n')
            closing.add_run('Investigation Officer')

            # Save to BytesIO
            temp_template = BytesIO()
            doc.save(temp_template)
            temp_template.seek(0)
            template_content = temp_template
            logger.info("Created default template since no template was provided")

        # Load the template document
        try:
            doc = Document(template_content)
            logger.info("Successfully loaded template document")
        except Exception as e:
            logger.error(f"Error loading template document: {str(e)}")
            # Fall back to default template
            doc = Document()
            title = doc.add_heading('NOTICE', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            doc.add_paragraph('Error loading template. Using default template.')

        # Extract metadata fields with fallbacks
        complaint_number = metadata.get('complaint_number') or metadata.get('Acknowledgement No') or 'N/A'
        complainant_name = process_complainant_name(metadata)
        total_amount = metadata.get('total_amount') or metadata.get('Total Fraudulent Amount') or 'N/A'
        complaint_date = metadata.get('date') or 'N/A'
        logger.info(f"Using complaint date in notice: {complaint_date}")

        # Define default points if none provided
        point_mapping = {
            "freeze": "1. Put hold on the amount with respect to the above transaction.",
            "details": "2. Provide account details including KYC and AOF form.",
            "statements": "3. Provide account statements form opening to till date.",
            "transactions": "4. Provide update mobile number and email ID.",
            "beneficiary": "5. Provide 63(4)(C) BSA Certificate with branch manager stamp and singature.",
            "video": "6. Provide CCTV footage if transactions were made at branch.",
            "ip": "7. Provide IP logs and device details for online transactions.",
            "reversal": "8. Initiate transaction reversal if funds are available."
        }

        # Use selected points if provided, otherwise use default
        default_points = list(point_mapping.values())
        points_to_use = [point_mapping.get(point_id, f"{i+1}. {point_id}")
                       for i, point_id in enumerate(selected_points)] if selected_points else default_points

        selected_bank_points = "\n".join(points_to_use)

        # Process transactions
        main_transactions = []
        sub_transactions = []

        # Process CSV data if available
        if 'csv_data_base64' in metadata and metadata['csv_data_base64']:
            logger.info("CSV data found in metadata, processing...")
            try:
                # Decode CSV data from base64
                csv_bytes = base64.b64decode(metadata['csv_data_base64'])
                csv_text = csv_bytes.decode('utf-8')
                csv_file = StringIO(csv_text)
                csv_reader = csv.DictReader(csv_file)
                logger.info(f"CSV headers: {csv_reader.fieldnames}")

                # Read all rows from CSV
                all_csv_rows = list(csv_reader)

                # Filter rows by bank if specified
                csv_data_rows = []
                if bank_name:
                    csv_data_rows = [row for row in all_csv_rows if row.get('Receiver Bank') == bank_name]
                    # If no rows found, try with 'receiver_bank' (lowercase) field
                    if not csv_data_rows:
                        csv_data_rows = [row for row in all_csv_rows if row.get('receiver_bank') == bank_name]
                else:
                    csv_data_rows = all_csv_rows

                # Process rows to ensure consistent field names
                for csv_row in csv_data_rows:
                    transaction = {
                        'account_number': clean_account_number(csv_row.get('Receiver Account') or csv_row.get('receiver_account', '')),
                        'date': csv_row.get('Txn Date') or csv_row.get('date', 'N/A'),
                        'amount': csv_row.get('Amount') or csv_row.get('amount', 'N/A'),
                        'txn_id': csv_row.get('Txn ID') or csv_row.get('txn_id', 'N/A'),
                        'reference': csv_row.get('Reference') or csv_row.get('reference', ''),
                        'txn_type': csv_row.get('Transaction Type') or csv_row.get('transaction_type', 'Normal')
                    }

                    # Determine if this is a main transaction or sub-transaction
                    reference = transaction['reference'].lower() if transaction['reference'] else ''
                    txn_type = transaction['txn_type'].lower() if transaction['txn_type'] else ''

                    # Check if this is a sub-transaction based on multiple criteria
                    is_sub_transaction = False

                    if ('sub' in txn_type or 'child' in txn_type or 'withdrawal' in txn_type or
                        'hold' in txn_type or 'sub-transaction' in txn_type):
                        is_sub_transaction = True

                    if ('withdrawal' in reference or 'withdrwal' in reference or 'hold' in reference or
                        'cash out' in reference or 'atm' in reference):
                        is_sub_transaction = True

                    # Check if this is a sub-transaction with excluded type
                    if is_sub_transaction:
                        # Skip sub-transactions with types "Transaction Put on Hold" or "Others" (case-insensitive)
                        txn_type = transaction.get('txn_type', '').strip()
                        if txn_type.lower() in ["transaction put on hold", "others"]:
                            logger.info(f"Skipping excluded sub-transaction type from CSV: {txn_type}")
                            continue
                        sub_transactions.append(transaction)
                    else:
                        main_transactions.append(transaction)

            except Exception as e:
                logger.error(f"Error processing CSV data: {str(e)}")

        # If no CSV data or CSV processing failed, fall back to bank_notice_data
        if not main_transactions and bank_notice_data:
            logger.info(f"No transactions from CSV, falling back to bank_notice_data")
            # Filter bank data if specific bank is requested
            filtered_bank_data = bank_notice_data
            if bank_name and isinstance(bank_notice_data, dict) and bank_name in bank_notice_data:
                filtered_bank_data = {bank_name: bank_notice_data[bank_name]}
                logger.info(f"Filtered bank data for {bank_name}")

            try:
                # Handle different data structures
                if isinstance(filtered_bank_data, dict):
                    for bank, accounts in filtered_bank_data.items():
                        if bank_name and bank != bank_name:
                            continue

                        if isinstance(accounts, dict):
                            for account_number, account_data in accounts.items():
                                if isinstance(account_data, dict) and 'transfers' in account_data:
                                    for transfer in account_data['transfers']:
                                        main_transactions.append({
                                            'account_number': clean_account_number(account_number),
                                            'date': transfer.get('date', 'N/A'),
                                            'amount': transfer.get('amount', 'N/A'),
                                            'txn_id': transfer.get('txn_id', 'N/A'),
                                            'reference': transfer.get('reference', '')
                                        })
                elif isinstance(filtered_bank_data, list):
                    for item in filtered_bank_data:
                        if isinstance(item, dict):
                            main_transactions.append({
                                'account_number': clean_account_number(item.get('account_number', 'N/A')),
                                'date': item.get('date', 'N/A'),
                                'amount': item.get('amount', 'N/A'),
                                'txn_id': item.get('txn_id', 'N/A'),
                                'reference': item.get('reference', '')
                            })
            except Exception as e:
                logger.error(f"Error processing bank notice data: {str(e)}")

        # Create main transaction table
        if main_transactions:
            logger.info(f"Creating main transaction table with {len(main_transactions)} transactions")
            # Find the paragraph with MAIN_TXN_TABLE placeholder to insert table after it
            table_position = None
            for i, paragraph in enumerate(doc.paragraphs):
                if 'MAIN_TXN_TABLE' in paragraph.text:
                    table_position = i
                    logger.info(f"Found MAIN_TXN_TABLE placeholder at paragraph {i}")
                    break

            if table_position is None:
                logger.warning("MAIN_TXN_TABLE placeholder not found in template")

            # Create the table
            main_txn_table = doc.add_table(rows=1, cols=5)
            main_txn_table.style = 'Table Grid'

            # Add headers
            header_cells = main_txn_table.rows[0].cells
            header_cells[0].text = 'Account Number'
            header_cells[1].text = 'Transaction Date'
            header_cells[2].text = 'Amount (Rs.)'
            header_cells[3].text = 'Transaction ID'
            header_cells[4].text = 'Reference / Remarks'

            # Make headers bold
            for cell in header_cells:
                for paragraph in cell.paragraphs:
                    paragraph_runs = paragraph.runs
                    if paragraph_runs:
                        for run in paragraph_runs:
                            run.bold = True
                    else:
                        # If no runs exist, create a new run with the text and make it bold
                        run = paragraph.add_run(paragraph.text)
                        paragraph.text = ""
                        run.bold = True

            # Add data rows
            for row_data in main_transactions:
                row_cells = main_txn_table.add_row().cells
                row_cells[0].text = row_data.get('account_number', 'N/A')
                row_cells[1].text = row_data.get('date', 'N/A')
                row_cells[2].text = row_data.get('amount', 'N/A')
                row_cells[3].text = row_data.get('txn_id', 'N/A')
                row_cells[4].text = row_data.get('reference', '')

            # Move the table to the correct position if needed
            if table_position is not None:
                # This is a workaround since python-docx doesn't have direct table positioning
                # We'll ensure the table appears in the document flow at the right place
                logger.info(f"Positioned table after paragraph {table_position}")

        # Create sub-transaction tables if needed
        if sub_transactions:
            # Group sub-transactions by type
            sub_txn_by_type = {}
            for txn in sub_transactions:
                txn_type = txn.get('txn_type', '').strip() or 'Additional Transaction'

                # Skip sub-transactions with types "Transaction Put on Hold" or "Others" (case-insensitive)
                if txn_type.lower() in ["transaction put on hold", "others"]:
                    logger.info(f"Skipping excluded sub-transaction type in table creation: {txn_type}")
                    continue

                if txn_type not in sub_txn_by_type:
                    sub_txn_by_type[txn_type] = []
                sub_txn_by_type[txn_type].append(txn)

            # Create a table for each type
            for txn_type, txns in sub_txn_by_type.items():
                # Add a heading for this transaction type
                subheading = doc.add_paragraph()
                subheading_run = subheading.add_run(f"{txn_type}")
                subheading_run.bold = True

                # Create the table
                table = doc.add_table(rows=1, cols=5)
                table.style = 'Table Grid'

                # Add headers
                header_cells = table.rows[0].cells
                header_cells[0].text = 'Account Number'
                header_cells[1].text = 'Transaction Date'
                header_cells[2].text = 'Amount (Rs.)'
                header_cells[3].text = 'Transaction ID'
                header_cells[4].text = 'Reference'

                # Make headers bold
                for cell in header_cells:
                    for paragraph in cell.paragraphs:
                        paragraph_runs = paragraph.runs
                        if paragraph_runs:
                            for run in paragraph_runs:
                                run.bold = True
                        else:
                            # If no runs exist, create a new run with the text and make it bold
                            run = paragraph.add_run(paragraph.text)
                            paragraph.text = ""
                            run.bold = True

                # Add data rows
                for row_data in txns:
                    row_cells = table.add_row().cells
                    row_cells[0].text = row_data.get('account_number', 'N/A')
                    row_cells[1].text = row_data.get('date', 'N/A')
                    row_cells[2].text = row_data.get('amount', 'N/A')
                    row_cells[3].text = row_data.get('txn_id', 'N/A')
                    row_cells[4].text = row_data.get('reference', '')

        # Replace placeholders in the document
        for paragraph in doc.paragraphs:
            # Store original text to check if we need to replace
            original_text = paragraph.text

            # Replace placeholders
            if 'BANK_NAME' in original_text:
                # We need to preserve formatting, so we replace runs instead of paragraph text
                for run in paragraph.runs:
                    if 'BANK_NAME' in run.text:
                        run.text = run.text.replace('BANK_NAME', bank_name or 'Unknown Bank')

            if 'COMPLAINT_NUMBER' in original_text:
                for run in paragraph.runs:
                    if 'COMPLAINT_NUMBER' in run.text:
                        run.text = run.text.replace('COMPLAINT_NUMBER', complaint_number)

            if 'VICTIM_NAME' in original_text:
                for run in paragraph.runs:
                    if 'VICTIM_NAME' in run.text:
                        run.text = run.text.replace('VICTIM_NAME', complainant_name)

            if 'COMPLAINANT_NAME' in original_text:
                for run in paragraph.runs:
                    if 'COMPLAINANT_NAME' in run.text:
                        run.text = run.text.replace('COMPLAINANT_NAME', complainant_name)

            if 'TOTAL_FRAUD_AMOUNT' in original_text:
                for run in paragraph.runs:
                    if 'TOTAL_FRAUD_AMOUNT' in run.text:
                        run.text = run.text.replace('TOTAL_FRAUD_AMOUNT', total_amount)

            if 'SELECTED_BANK_POINTS' in original_text:
                for run in paragraph.runs:
                    if 'SELECTED_BANK_POINTS' in run.text:
                        run.text = run.text.replace('SELECTED_BANK_POINTS', selected_bank_points)

            # For MAIN_TXN_TABLE, we need to handle it differently
            if 'MAIN_TXN_TABLE' in original_text:
                # Just clear the text in this paragraph, the table is added separately
                paragraph.text = ''

        # Save the document to a BytesIO object
        output = BytesIO()
        doc.save(output)
        output.seek(0)
        return output

    except Exception as e:
        logger.error(f"Error generating notice: {str(e)}")
        return BytesIO()

def structure_transactions_by_bank(transactions: List[Dict[str, Any]]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
    """
    Structure transactions by bank, separating main and sub transactions.

    Args:
        transactions: List of transaction dictionaries

    Returns:
        Dictionary with bank-structured data in the format:
        {
            "Bank Name": {
                "main": [list of main transactions],
                "sub": [list of sub transactions]
            }
        }
    """
    # Create bank-structured format
    bank_structured_data = {}

    # Log the first transaction to see all available fields
    if transactions and len(transactions) > 0:
        logger.info(f"Sample transaction keys: {list(transactions[0].keys())}")
        logger.info(f"Sample transaction: {transactions[0]}")

    for txn in transactions:
        # Determine transaction type (main or sub)
        txn_type = txn.get("txn_type", txn.get("type", "")).lower()
        is_main_transaction = "money transfer to" in txn_type

        # Log transaction type for debugging
        logger.debug(f"Transaction type: '{txn.get('txn_type', txn.get('type', ''))}', is_main_transaction: {is_main_transaction}")

        # Get bank name (use receiver bank, or sender bank for sub-transactions if receiver bank is missing)
        if "receiver_bank" in txn and txn["receiver_bank"]:
            bank_name = txn["receiver_bank"]
        elif "sender_bank" in txn and txn["sender_bank"] and not is_main_transaction:
            bank_name = txn["sender_bank"]
        elif "bank_name" in txn and txn["bank_name"]:
            bank_name = txn["bank_name"]
        else:
            # Skip transactions without a bank
            logger.warning(f"Skipping transaction without bank: {txn}")
            continue

        # Initialize bank entry if not exists
        if bank_name not in bank_structured_data:
            bank_structured_data[bank_name] = {
                "main": [],
                "sub": []
            }

        # Check for reference field in different possible names
        reference_value = None

        # First, check the most common field names
        reference_field_names = ['reference', 'remarks', 'receiver_info', 'additional_remarks', 'description', 'narration']
        for field in reference_field_names:
            if field in txn and txn[field]:
                reference_value = txn[field]
                logger.debug(f"Found reference in field '{field}': {reference_value}")
                break

        # If still no reference, check for any field containing 'ref' or 'remark'
        if not reference_value:
            for key, value in txn.items():
                if ('ref' in key.lower() or 'remark' in key.lower() or 'narration' in key.lower() or 'desc' in key.lower()) and value:
                    reference_value = value
                    logger.debug(f"Found reference in field '{key}': {reference_value}")
                    break

        # Log all transaction fields to help diagnose the issue
        logger.info(f"Transaction fields: {list(txn.keys())}")
        logger.info(f"Reference value: {reference_value}")

        # Create transaction object with required fields
        if is_main_transaction:
            # Main transaction
            main_txn = {
                'date': txn.get('date', 'N/A'),
                'receiver_account': txn.get('receiver_account', txn.get('account_no', 'N/A')),
                'amount': txn.get('amount', 'N/A'),
                'txn_id': txn.get('txn_id', txn.get('transaction_id', txn.get('receiver_transaction_id', 'N/A'))),
                'reference': reference_value or 'N/A',
                'receiver_info': reference_value or 'N/A',  # Add receiver_info field for new code
                'txn_type': 'Money Transfer to'  # Add txn_type field for consistency
            }
            logger.info(f"Adding main transaction with reference: {reference_value}")
            bank_structured_data[bank_name]["main"].append(main_txn)
        else:
            # Get transaction type
            txn_type = txn.get('txn_type', txn.get('type', 'N/A'))

            # Skip sub-transactions with types "Transaction Put on Hold" or "Others" (case-insensitive)
            if txn_type.lower() in ["transaction put on hold", "others"]:
                logger.info(f"Skipping excluded sub-transaction type: {txn_type}")
                continue

            # Sub transaction
            sub_txn = {
                'date': txn.get('date', 'N/A'),
                'type': txn_type,  # Keep for backward compatibility
                'txn_type': txn_type,  # Add txn_type field for new code
                'amount': txn.get('amount', 'N/A'),
                'sender_transaction_id': txn.get('txn_id', txn.get('transaction_id', txn.get('sender_transaction_id', 'N/A'))),
                'reference': reference_value or 'N/A',
                'receiver_info': reference_value or 'N/A'  # Add receiver_info field for new code
            }
            logger.info(f"Adding sub transaction with reference: {reference_value}")
            bank_structured_data[bank_name]["sub"].append(sub_txn)

    # Log summary of structured data
    for bank, data in bank_structured_data.items():
        logger.info(f"Bank {bank}: {len(data['main'])} main transactions, {len(data['sub'])} sub transactions")
        if data['main']:
            logger.info(f"Sample main transaction: {data['main'][0]}")
        if data['sub']:
            logger.info(f"Sample sub transaction: {data['sub'][0]}")

    return bank_structured_data

def inspect_template(template_content: BytesIO) -> None:
    """
    Inspect a template file to help diagnose issues.

    Args:
        template_content: Template document as BytesIO
    """
    try:
        from docx import Document

        # Make a copy of the template content
        template_copy = BytesIO(template_content.getvalue())
        template_content.seek(0)  # Reset the original

        # Load the document
        doc = Document(template_copy)

        # Check for tables
        table_count = len(doc.tables)
        logger.info(f"Template has {table_count} tables")

        # Check for paragraphs with Jinja2 syntax
        jinja_paragraphs = 0
        for para in doc.paragraphs:
            if '{{' in para.text or '{%' in para.text:
                jinja_paragraphs += 1
                logger.info(f"Jinja2 paragraph: {para.text[:100]}...")

        logger.info(f"Template has {jinja_paragraphs} paragraphs with Jinja2 syntax")

        # Check for Jinja2 syntax in tables
        for i, table in enumerate(doc.tables):
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        if '{{' in para.text or '{%' in para.text:
                            logger.info(f"Table {i+1} has Jinja2 syntax: {para.text[:100]}...")

    except Exception as e:
        logger.error(f"Error inspecting template: {str(e)}")

def generate_notice_with_docxtpl(
    bank_notice_data: Union[List[Dict[str, Any]], Dict[str, Any]],
    metadata: Dict[str, Any],
    template_content: Union[BytesIO, None] = None,
    bank_name: Union[str, None] = None,
    selected_points: List[str] = []
) -> BytesIO:
    """
    Generate a notice document using docxtpl (DocxTemplate).

    Args:
        bank_notice_data: Structured bank notice data from bank_notice_service.py
        metadata: Complaint metadata
        template_content: Template document as BytesIO
        bank_name: Optional specific bank to generate notice for
        selected_points: Optional list of selected points to include

    Returns:
        BytesIO object containing the generated document
    """
    logger.info(f"Generating notice with docxtpl for bank: {bank_name}")

    try:
        # Define default points if none provided
        # New format with main points and sub-points using bullets instead of numbers
        point_mapping = {
            # Account Holder Information
            "account_info_name": "• Full name with registered and communication address of the bank account holder.",
            "account_info_contact": "• Contact number and email address linked to the account/wallet.",
            "account_info_digital": "• Digital footprints of account opening journey if the account is opened digitally.",
            "account_info_kyc": "• Account Opening Form (AOF), KYC documents, and suspicious activity flags.",

            # Linked Accounts and Cards
            "linked_accounts_wallet": "• Details of linked bank account, credit/debit cards or UPI/VPAs with wallet.",
            "linked_accounts_same_kyc": "• Details of bank accounts opened with same KYC and details in you bank.",
            "linked_accounts_card": "• ATM-cum-debit card details (card number, issue date, delivery address).",

            # IP and Device Records
            "ip_device_addresses": "• IP addresses and login/logout history used for net banking or account access.",
            "ip_device_details": "• Login/Logout IP address along with Device IDs and operating systems of devices used for account activity, geolocation coordinates (latitude/longitude) for transaction.",

            # Authentication and Access Logs
            "auth_logs_otp": "• Mobile numbers and email used for OTP or authentication during transactions and logins.",
            "auth_logs_history": "• Mobile number or email changing history of the account.",

            # Beneficiary and Transaction History
            "beneficiary_receiver": "• Receiver's name, account number/VPA and IFSC code for fund transfers.",
            "beneficiary_history": "• Complete transaction history from account opening/creation to till date.",
            "beneficiary_flags": "• Internal flags and NCRP complaints received about the account/Wallet.",

            # Wallet/Bank account status and Balance
            "wallet_status_current": "• Current status (active, frozen), frozen amount, and last available balance.",
            "wallet_status_hold": "• Put an hold on the amount mentioned in above transactions.",

            # E-commerce or Delivery Information
            "ecommerce_recipient": "• Recepient's name, contact number, courier details, tracking number.",
            "ecommerce_address": "• Delivery/shipping address, Merchant details (name, address).",
            "ecommerce_history": "• Any order history of the same user with address, product and contact details.",

            # ATM CCTV footage
            "atm_cctv_footage": "• CCTV footage (external or Internal) of the ATM mentioned above of same day of transaction in case the ATM doesn't belong to your bank then provide the point of contact.",
            "atm_cctv_credentials": "• Debit/Credit or Card less transaction credentials used for the transaction.",

            # BSA Certification
            "bsa_certificate": "• Section 63(4)(C) BSA certificate to authenticate electronic records.",

            # Legacy points for backward compatibility
            "freeze": "• Put hold on the amount with respect to the above transaction.",
            "details": "• Provide account details including KYC and AOF form.",
            "statements": "• Provide account statements form opening to till date.",
            "transactions": "• Provide update mobile number and email ID.",
            "beneficiary": "• Provide 63(4)(C) BSA Certificate with branch manager stamp and singature.",
            "video": "• Provide CCTV footage if transactions were made at branch.",
            "ip": "• Provide IP logs and device details for online transactions.",
            "reversal": "• Initiate transaction reversal if funds are available."
        }

        # Main point categories for grouping
        main_point_categories = {
            "account_info": "Account Holder Information:",
            "linked_accounts": "Linked Accounts and Cards:",
            "ip_device": "IP and Device Records:",
            "auth_logs": "Authentication and Access Logs:",
            "beneficiary": "Beneficiary and Transaction History:",
            "wallet_status": "Wallet/Bank account status and Balance:",
            "ecommerce": "E-commerce or Delivery Information:",
            "atm_cctv": "ATM CCTV footage:",
            "bsa": "BSA Certification:"
        }

        # Group selected points by main category
        grouped_points = {}

        # Process selected points
        if selected_points:
            for point_id in selected_points:
                # Check if this is a main point or sub-point
                if point_id in main_point_categories:
                    # This is a main point, skip it as we'll handle sub-points
                    continue

                # This is a sub-point, find its main category
                main_category = None
                for main_id in main_point_categories:
                    if point_id.startswith(main_id + "_"):
                        main_category = main_id
                        break

                # If we found a main category, add the point to that group
                if main_category:
                    if main_category not in grouped_points:
                        grouped_points[main_category] = []

                    # Add the point text if it exists in the mapping
                    if point_id in point_mapping:
                        grouped_points[main_category].append(point_mapping[point_id])
                else:
                    # This is a legacy point or unknown point, add it to a special group
                    if "legacy" not in grouped_points:
                        grouped_points["legacy"] = []

                    # Add the point text if it exists in the mapping, otherwise use the ID with a bullet
                    grouped_points["legacy"].append(point_mapping.get(point_id, f"• {point_id}"))
        else:
            # If no points selected, use all points grouped by category
            for point_id, point_text in point_mapping.items():
                # Find the main category for this point
                main_category = None
                for main_id in main_point_categories:
                    if point_id.startswith(main_id + "_"):
                        main_category = main_id
                        break

                # If we found a main category, add the point to that group
                if main_category:
                    if main_category not in grouped_points:
                        grouped_points[main_category] = []
                    grouped_points[main_category].append(point_text)

        # Build the final text with main categories and their points
        points_text = []
        for main_id, points in grouped_points.items():
            if points:  # Only add categories that have points
                if main_id in main_point_categories:
                    # Add the main category heading
                    points_text.append(f"\n{main_point_categories[main_id]}")
                    # Add all points in this category
                    points_text.extend(points)
                else:
                    # This is the legacy group, just add the points without a heading
                    points_text.extend(points)

        # Join all the points with newlines
        selected_bank_points = "\n".join(points_text)

        # Extract metadata fields with fallbacks
        complaint_number = metadata.get('complaint_number') or metadata.get('Acknowledgement No') or 'N/A'
        complaint_date = metadata.get('date') or metadata.get('Date of Complaint') or 'N/A'
        complainant_name = process_complainant_name(metadata)
        total_fraud_amount = metadata.get('total_amount') or metadata.get('Total Fraudulent Amount') or 'N/A'

        # Get main and sub transactions from the bank_notice_data
        main_transactions = []
        sub_transactions = []

        # Check if bank_notice_data is already structured by bank
        if isinstance(bank_notice_data, dict):
            # If a specific bank is requested, filter the data
            if bank_name and bank_name in bank_notice_data:
                # Only process the specified bank
                if "main" in bank_notice_data[bank_name] and "sub" in bank_notice_data[bank_name]:
                    # Data is already in the expected format
                    main_transactions = bank_notice_data[bank_name]["main"]
                    sub_transactions = bank_notice_data[bank_name]["sub"]
                    logger.info(f"Using pre-structured data for bank {bank_name}: {len(main_transactions)} main, {len(sub_transactions)} sub")
            elif not bank_name:
                # Process all banks if no specific bank is requested
                for bank, data in bank_notice_data.items():
                    if isinstance(data, dict) and "main" in data and "sub" in data:
                        main_transactions.extend(data["main"])
                        sub_transactions.extend(data["sub"])
                logger.info(f"Combined all banks: {len(main_transactions)} main, {len(sub_transactions)} sub")

        # If no transactions found, try to structure them from raw transactions
        if not main_transactions and not sub_transactions and "transactions" in metadata:
            logger.info("No structured data found, structuring from raw transactions")
            transactions = metadata["transactions"]
            structured_data = structure_transactions_by_bank(transactions)

            # If a specific bank is requested, filter the data
            if bank_name and bank_name in structured_data:
                main_transactions = structured_data[bank_name]["main"]
                sub_transactions = structured_data[bank_name]["sub"]
                logger.info(f"Structured data for bank {bank_name}: {len(main_transactions)} main, {len(sub_transactions)} sub")
            elif not bank_name:
                # Process all banks if no specific bank is requested
                for bank, data in structured_data.items():
                    main_transactions.extend(data["main"])
                    sub_transactions.extend(data["sub"])
                logger.info(f"Structured and combined all banks: {len(main_transactions)} main, {len(sub_transactions)} sub")

        logger.info(f"Prepared data for template: {len(main_transactions)} main transactions, {len(sub_transactions)} sub transactions")

        # Format transactions for table if needed
        # Some templates might expect transactions in a specific format for tables
        formatted_main_transactions = []
        for txn in main_transactions:
            # Log the transaction to help diagnose issues
            logger.info(f"Formatting main transaction: {txn}")

            # Clean account number
            receiver_account = clean_account_number(txn.get('receiver_account', 'N/A'))

            formatted_txn = {
                'date': txn.get('date', 'N/A'),
                'receiver_account': receiver_account,
                'amount': txn.get('amount', 'N/A'),
                'txn_id': txn.get('txn_id', 'N/A'),
                'reference': txn.get('reference', 'N/A'),
                'receiver_info': txn.get('receiver_info', txn.get('reference', 'N/A')),  # Add receiver_info field
                'txn_type': 'Money Transfer to'  # Add txn_type field for consistency
            }

            # Log the formatted transaction
            logger.info(f"Formatted main transaction: {formatted_txn}")
            formatted_main_transactions.append(formatted_txn)

        formatted_sub_transactions = []
        for txn in sub_transactions:
            # Log the transaction to help diagnose issues
            logger.info(f"Formatting sub transaction: {txn}")

            # Get transaction type
            txn_type = txn.get('type', 'N/A')

            # Skip sub-transactions with types "Transaction Put on Hold" or "Others" (case-insensitive)
            if txn_type.lower() in ["transaction put on hold", "others"]:
                logger.info(f"Skipping excluded sub-transaction type: {txn_type}")
                continue

            # Clean account number if present
            sender_account = clean_account_number(txn.get('sender_account', 'N/A'))

            formatted_txn = {
                'date': txn.get('date', 'N/A'),
                'type': txn_type,  # Keep 'type' for backward compatibility with templates
                'txn_type': txn_type,  # Add txn_type field for new templates
                'amount': txn.get('amount', 'N/A'),
                'sender_transaction_id': txn.get('sender_transaction_id', 'N/A'),
                'reference': txn.get('reference', 'N/A'),
                'receiver_info': txn.get('receiver_info', txn.get('reference', 'N/A')),  # Add receiver_info field
                'sender_account': sender_account
            }

            # Log the formatted transaction
            logger.info(f"Formatted sub transaction: {formatted_txn}")
            formatted_sub_transactions.append(formatted_txn)

        # Use current date for current date placeholder
        current_date = datetime.now().strftime('%d/%m/%Y')

        # Check if this is a card fraud case
        fraud_type = metadata.get('fraud_type', '')
        is_card_fraud = fraud_type == 'card'

        # We've removed the victim's card number logic as requested
        if is_card_fraud:
            logger.info(f"Card fraud type detected in docxtpl notice generation: {fraud_type}")

        # Create context for template rendering
        context = {
            'bank_name': bank_name or 'Unknown Bank',
            'complaint_number': complaint_number,
            'complaint_date': complaint_date,
            'complainant_name': complainant_name,
            'total_fraud_amount': total_fraud_amount,
            'main_transactions': formatted_main_transactions,
            'sub_transactions': formatted_sub_transactions,
            'selected_bank_points': selected_bank_points,
            'current_date': current_date,
            'is_card_fraud': is_card_fraud
            # Removed victim_card_number as requested
        }

        # If no template is provided, create a default template
        if template_content is None:
            logger.warning("No template provided, creating a default template")
            # Create a default template with a simple structure
            doc = Document()
            doc.add_heading('NOTICE', 0)
            doc.add_paragraph('To,\nThe Branch Manager\n{{ bank_name }}\n\n')
            doc.add_paragraph('Subject: Request for information regarding fraudulent transactions - Complaint No. {{ complaint_number }}')
            doc.add_paragraph('Sir/Madam,')
            doc.add_paragraph('This is with reference to a cyber fraud complaint (No. {{ complaint_number }}) filed by {{ complainant_name }} involving fraudulent transactions amounting to Rs. {{ total_fraud_amount }}.')

            # Removed victim's credit card information section as requested

            # Add a paragraph for main transactions
            doc.add_paragraph('{% if main_transactions %}')
            doc.add_paragraph('Main Transactions (Money Transfers):')

            # Add a table for main transactions
            table = doc.add_table(rows=1, cols=5)
            table.style = 'Table Grid'

            # Add header row
            header_cells = table.rows[0].cells
            header_cells[0].text = 'Date'
            header_cells[1].text = 'Account/Card No/UPI'
            header_cells[2].text = 'Amount (₹)'
            header_cells[3].text = 'Transaction ID'
            header_cells[4].text = 'Reference'

            # Add placeholder for transaction rows
            doc.add_paragraph('{% for txn in main_transactions %}')

            # Add a row for each transaction
            row_cells = table.add_row().cells
            row_cells[0].text = '{{ txn.date }}'
            row_cells[1].text = '{{ txn.receiver_account }}'
            row_cells[2].text = '{{ txn.amount }}'
            row_cells[3].text = '{{ txn.txn_id }}'
            row_cells[4].text = '{{ txn.receiver_info }}'

            doc.add_paragraph('{% endfor %}')
            doc.add_paragraph('{% else %}')
            doc.add_paragraph('Main Transactions: No money transfer transactions recorded.')
            doc.add_paragraph('{% endif %}')

            # Add a paragraph for sub transactions
            doc.add_paragraph('{% if sub_transactions %}')
            doc.add_paragraph('Sub-Transactions (Withdrawals, Holds, etc.):')

            # Add a table for sub transactions
            sub_table = doc.add_table(rows=1, cols=5)
            sub_table.style = 'Table Grid'

            # Add header row
            sub_header_cells = sub_table.rows[0].cells
            sub_header_cells[0].text = 'Date'
            sub_header_cells[1].text = 'Type of sub txn'
            sub_header_cells[2].text = 'Amount (₹)'
            sub_header_cells[3].text = 'Transaction ID'
            sub_header_cells[4].text = 'Reference'

            # Add placeholder for transaction rows
            doc.add_paragraph('{% for txn in sub_transactions %}')

            # Add a row for each transaction
            sub_row_cells = sub_table.add_row().cells
            sub_row_cells[0].text = '{{ txn.date }}'
            sub_row_cells[1].text = '{{ txn.txn_type }}'
            sub_row_cells[2].text = '{{ txn.amount }}'
            sub_row_cells[3].text = '{{ txn.sender_transaction_id }}'
            sub_row_cells[4].text = '{{ txn.receiver_info }}'

            doc.add_paragraph('{% endfor %}')
            doc.add_paragraph('{% else %}')
            doc.add_paragraph('Sub-Transactions: No withdrawals or holds recorded.')
            doc.add_paragraph('{% endif %}')

            # Add selected points
            doc.add_paragraph('Hence please provide the given information & do the needful:')
            doc.add_paragraph('{{ selected_bank_points }}')

            # Save to BytesIO
            temp_template = BytesIO()
            doc.save(temp_template)
            temp_template.seek(0)
            template_content = temp_template

        # Inspect the template to help diagnose issues
        if template_content:
            inspect_template(template_content)
            template_content.seek(0)  # Reset position after inspection

        # Load the template with DocxTemplate
        try:
            # Validate template content
            if not template_content or template_content.getbuffer().nbytes == 0:
                logger.warning("Empty template content provided, creating default template")
                # Create a default template
                doc = Document()
                doc.add_heading('NOTICE', 0)
                doc.add_paragraph('Default template - original template was empty or invalid')

                # Save to BytesIO
                temp_output = BytesIO()
                doc.save(temp_output)
                temp_output.seek(0)
                template_content = temp_output

            # Check if template content is valid docx
            try:
                # Try to load the template to validate it
                Document(template_content)
                # Reset position after validation
                template_content.seek(0)
            except Exception as template_error:
                logger.error(f"Invalid template document: {str(template_error)}")
                # Create a default template
                doc = Document()
                doc.add_heading('NOTICE', 0)
                doc.add_paragraph(f'Error loading template: {str(template_error)}')

                # Save to BytesIO
                temp_output = BytesIO()
                doc.save(temp_output)
                temp_output.seek(0)
                template_content = temp_output

            # Load the template with DocxTemplate
            template = DocxTemplate(template_content)
            logger.info("Successfully loaded template with DocxTemplate")

            # Log the context keys and some sample data
            logger.info(f"Context keys: {list(context.keys())}")
            if main_transactions:
                logger.info(f"Sample main transaction: {main_transactions[0]}")
            if sub_transactions:
                logger.info(f"Sample sub transaction: {sub_transactions[0]}")

            # Sanitize context data to prevent template injection
            sanitized_context = {}
            for key, value in context.items():
                if isinstance(value, str):
                    # Remove any potentially harmful Jinja2 constructs
                    sanitized_value = value.replace("{%", "").replace("%}", "").replace("{{", "").replace("}}", "")
                    sanitized_context[key] = sanitized_value
                else:
                    sanitized_context[key] = value

            # Render the template with the sanitized context
            template.render(sanitized_context)
            logger.info("Template rendered successfully")
        except Exception as e:
            logger.error(f"Error rendering template: {str(e)}")
            # Create a default template with error message
            doc = Document()
            doc.add_heading('NOTICE - ERROR', 0)
            doc.add_paragraph(f'Error rendering template: {str(e)}')

            # Add basic information
            doc.add_paragraph(f"Complaint Number: {metadata.get('complaint_number', 'N/A')}")
            doc.add_paragraph(f"Complainant Name: {metadata.get('complainant_name', 'N/A')}")
            doc.add_paragraph(f"Date: {metadata.get('date', 'N/A')}")

            # Save to BytesIO
            error_output = BytesIO()
            doc.save(error_output)
            error_output.seek(0)

            # Return the error template instead of raising an exception
            return error_output

        # Save the rendered template to a BytesIO object
        output = BytesIO()
        template.save(output)
        output.seek(0)

        return output

    except Exception as e:
        logger.error(f"Error generating notice with docxtpl: {str(e)}")
        return BytesIO()

# Aliases for backward compatibility
generate_notice_with_simple_approach = generate_notice_from_template
generate_notice_with_jinja_template = generate_notice_with_docxtpl
