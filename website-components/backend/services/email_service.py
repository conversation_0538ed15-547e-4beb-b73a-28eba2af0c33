import os
import smtplib
import secrets  # Using secrets instead of random for better security
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor

# Note: Environment variables are loaded by main.py

logger = logging.getLogger(__name__)

# Thread pool for sending emails asynchronously
email_thread_pool = ThreadPoolExecutor(max_workers=4)

# Hostinger SMTP Configuration
HOSTINGER_SMTP_HOST = os.getenv("HOSTINGER_SMTP_HOST", "smtp.hostinger.com")
HOSTINGER_SMTP_PORT = int(os.getenv("HOSTINGER_SMTP_PORT", "465"))
HOSTINGER_SMTP_USERNAME = os.getenv("HOSTINGER_SMTP_USERNAME")
HOSTINGER_SMTP_PASSWORD = os.getenv("HOSTINGER_SMTP_PASSWORD")
HOSTINGER_SMTP_USE_SSL = os.getenv("HOSTINGER_SMTP_USE_SSL", "True").lower() == "true"

# Email Configuration
EMAIL_FROM = os.getenv("SENDER_EMAIL")
EMAIL_FROM_NAME = os.getenv("SENDER_NAME", "NCRP Matrix")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3001")

# Validate email configuration
if not EMAIL_FROM:
    logger.error("SENDER_EMAIL environment variable is not set!")
    logger.error(f"Available environment variables: SENDER_EMAIL={os.getenv('SENDER_EMAIL')}, SENDER_NAME={os.getenv('SENDER_NAME')}")
    raise ValueError("SENDER_EMAIL environment variable is required")

# Log the loaded configuration for debugging
logger.info(f"Email configuration loaded: FROM={EMAIL_FROM}, NAME={EMAIL_FROM_NAME}")


def _send_email_sync(to_email: str, subject: str, html_content: str):
    """Synchronous function to send emails using SMTP (runs in a thread)"""
    logger.info(f"Attempting to send email to {to_email} with subject: {subject}")
    logger.info(f"Using sender email: {EMAIL_FROM} (from env: {os.getenv('SENDER_EMAIL', 'NOT_SET')})")

    if not HOSTINGER_SMTP_USERNAME or not HOSTINGER_SMTP_PASSWORD:
        logger.error("SMTP credentials not configured")
        return False

    # Create message
    msg = MIMEMultipart()
    msg['From'] = f"{EMAIL_FROM_NAME} <{EMAIL_FROM}>"
    msg['To'] = to_email
    msg['Subject'] = subject

    # Add HTML content
    msg.attach(MIMEText(html_content, 'html'))

    try:
        # Connect to SMTP server
        logger.info("Connecting to SMTP server...")
        if HOSTINGER_SMTP_USE_SSL:
            server = smtplib.SMTP_SSL(HOSTINGER_SMTP_HOST, HOSTINGER_SMTP_PORT)
        else:
            server = smtplib.SMTP(HOSTINGER_SMTP_HOST, HOSTINGER_SMTP_PORT)
            server.starttls()  # Upgrade to secure connection

        # Login to SMTP server
        server.login(HOSTINGER_SMTP_USERNAME, HOSTINGER_SMTP_PASSWORD)

        # Send email
        server.send_message(msg)
        server.quit()

        logger.info(f"Email sent to {to_email} using SMTP")
        return True
    except Exception as e:
        logger.error(f"Error sending email via SMTP: {str(e)}")
        # Print more details about the exception
        import traceback
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        return False

async def _send_email(to_email: str, subject: str, html_content: str):
    """Asynchronous wrapper for sending emails using a thread pool"""
    logger.info(f"Queueing email to {to_email} with subject: {subject}")

    # Run the synchronous email sending function in a thread pool
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        email_thread_pool,
        lambda: _send_email_sync(to_email, subject, html_content)
    )

    return result


def generate_otp(length=6):
    """
    Generate a random alphanumeric OTP of specified length

    Uses a mix of uppercase letters and digits for better security
    while maintaining readability (excludes similar looking characters)

    Enhanced for production with 500 users:
    - Uses a more secure random generator
    - Ensures a mix of letters and numbers for better security
    - Excludes confusing characters
    """
    # Use the imported secrets module for cryptographically strong random numbers

    # Exclude confusing characters like 0, O, 1, I, etc.
    digits = '23456789'
    letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'

    # Ensure at least 2 digits and 2 letters for better security
    # This makes the OTP harder to guess while keeping it readable
    num_digits = max(2, length // 3)  # At least 2 digits, or 1/3 of length
    num_letters = length - num_digits

    # Generate the components
    otp_digits = ''.join(secrets.choice(digits) for _ in range(num_digits))
    otp_letters = ''.join(secrets.choice(letters) for _ in range(num_letters))

    # Combine and shuffle
    combined = list(otp_digits + otp_letters)
    secrets.SystemRandom().shuffle(combined)

    return ''.join(combined)

def get_otp_expiry(minutes=15):
    """
    Get OTP expiry time (default: 15 minutes from now)

    Reduced from 30 minutes to 15 minutes for better security with 500 users

    Args:
        minutes (int): Number of minutes until OTP expires (default: 15)

    Returns:
        datetime: UTC timestamp when the OTP will expire
    """
    return datetime.now(timezone.utc) + timedelta(minutes=minutes)

async def send_verification_email(email: str, otp: str, first_name: str = "", is_login: bool = False):
    """
    Send email verification or login OTP using SMTP asynchronously

    Args:
        email (str): Recipient email address
        otp (str): One-time password for verification
        first_name (str, optional): Recipient's first name
        is_login (bool, optional): Whether this is for login verification (two-step)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if is_login:
        logger.info(f"Preparing login verification email with OTP for {email}...")
        subject = "Login Verification Code - NCRP Matrix"
        title = "Login Verification"
        message = f"Hello {first_name}, we received a login request for your NCRP Matrix account. Please use the verification code below to complete your login:"
        expiry = "5 minutes"
        note = "If you didn't attempt to login, please change your password immediately."
    else:
        logger.info(f"Preparing email verification OTP for {email}...")
        subject = "Verify your NCRP Matrix account"
        title = f"Welcome {first_name} to NCRP Matrix!"
        message = "Thank you for registering with NCRP Matrix. Please verify your email address using the verification code below:"
        expiry = "15 minutes"
        note = "If you didn't create this account, please ignore this email."

    # Create HTML content
    html_content = f"""
        <html>
        <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div style="background: linear-gradient(135deg, #0891B2 0%, #0369A1 100%); padding: 30px 20px; text-align: center;">
                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">{title}</h1>
                    <p style="color: #E0F2FE; margin: 10px 0 0 0; font-size: 16px;">NCRP Matrix - Professional Cyber Crime Investigation Platform</p>
                </div>
                <div style="padding: 30px 20px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">{message}</p>
                    <div style="background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%); padding: 20px; border-radius: 8px; text-align: center; margin: 25px 0; border: 2px solid #0891B2;">
                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #0369A1; font-weight: 600;">VERIFICATION CODE</p>
                        <div style="font-size: 32px; font-weight: bold; color: #0891B2; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                            {otp}
                        </div>
                    </div>
                    <div style="background-color: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px; color: #92400E;"><strong>Important:</strong> This code contains both letters and numbers. Please enter it exactly as shown above.</p>
                    </div>
                    <p style="font-size: 14px; color: #6B7280;">This verification code will expire in <strong>{expiry}</strong>.</p>
                    <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 25px 0;">
                    <p style="font-size: 14px; color: #6B7280;">{note}</p>
                    <div style="margin-top: 30px;">
                        <p style="margin: 0; font-size: 16px; color: #374151;">Best regards,</p>
                        <p style="margin: 5px 0 0 0; font-size: 16px; color: #0891B2; font-weight: 600;">The NCRP Matrix Team</p>
                    </div>
                </div>
                <div style="background-color: #F9FAFB; padding: 20px; text-align: center; border-top: 1px solid #E5E7EB;">
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #9CA3AF;">This is an automated message, please do not reply to this email.</p>
                    <p style="margin: 0; font-size: 12px; color: #6B7280;">♥ Made with the help of PS Cyber Crime Bhiwani</p>
                </div>
            </div>
        </body>
        </html>
    """

    result = await _send_email(
        to_email=email,
        subject=subject,
        html_content=html_content
    )

    logger.info(f"Verification email send result: {result}")
    return result


async def test_email_configuration(email: str):
    """Test the email configuration by sending a test email asynchronously"""
    logger.info(f"Testing email configuration by sending to {email}")

    html_content = f"""
        <html>
        <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div style="background: linear-gradient(135deg, #0891B2 0%, #0369A1 100%); padding: 30px 20px; text-align: center;">
                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">Email Configuration Test</h1>
                    <p style="color: #E0F2FE; margin: 10px 0 0 0; font-size: 16px;">NCRP Matrix - Professional Cyber Crime Investigation Platform</p>
                </div>
                <div style="padding: 30px 20px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">This is a test email to verify that your NCRP Matrix email configuration is working correctly.</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">If you received this email, your SMTP settings are configured properly.</p>
                    <div style="margin-top: 30px;">
                        <p style="margin: 0; font-size: 16px; color: #374151;">Best regards,</p>
                        <p style="margin: 5px 0 0 0; font-size: 16px; color: #0891B2; font-weight: 600;">The NCRP Matrix Team</p>
                    </div>
                </div>
                <div style="background-color: #F9FAFB; padding: 20px; text-align: center; border-top: 1px solid #E5E7EB;">
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #9CA3AF;">This is an automated message, please do not reply to this email.</p>
                    <p style="margin: 0; font-size: 12px; color: #6B7280;">♥ Made with the help of PS Cyber Crime Bhiwani</p>
                </div>
            </div>
        </body>
        </html>
    """

    result = await _send_email(
        to_email=email,
        subject="NCRP Matrix Email Configuration Test",
        html_content=html_content
    )

    logger.info(f"Test email send result: {result}")
    return result


async def send_password_reset_email(email: str, otp: str, first_name: str = ""):
    """Send password reset OTP using SMTP asynchronously"""
    logger.info(f"Preparing password reset email with OTP for {email}...")

    # Create HTML content
    html_content = f"""
        <html>
        <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden;">
                <div style="background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%); padding: 30px 20px; text-align: center;">
                    <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">Password Reset Request</h1>
                    <p style="color: #FEE2E2; margin: 10px 0 0 0; font-size: 16px;">NCRP Matrix - Professional Cyber Crime Investigation Platform</p>
                </div>
                <div style="padding: 30px 20px;">
                    <p style="font-size: 16px; margin-bottom: 20px;">Hello {first_name},</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">We received a request to reset your NCRP Matrix account password.</p>
                    <p style="font-size: 16px; margin-bottom: 20px;">Please use the verification code below to reset your password:</p>
                    <div style="background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%); padding: 20px; border-radius: 8px; text-align: center; margin: 25px 0; border: 2px solid #DC2626;">
                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #B91C1C; font-weight: 600;">PASSWORD RESET CODE</p>
                        <div style="font-size: 32px; font-weight: bold; color: #DC2626; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                            {otp}
                        </div>
                    </div>
                    <div style="background-color: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px; color: #92400E;"><strong>Important:</strong> This code contains both letters and numbers. Please enter it exactly as shown above.</p>
                    </div>
                    <p style="font-size: 14px; color: #6B7280;">This verification code will expire in <strong>15 minutes</strong>.</p>
                    <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 25px 0;">
                    <div style="background-color: #FEF2F2; border-left: 4px solid #DC2626; padding: 15px; margin: 20px 0; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px; color: #B91C1C;">If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
                    </div>
                    <div style="margin-top: 30px;">
                        <p style="margin: 0; font-size: 16px; color: #374151;">Best regards,</p>
                        <p style="margin: 5px 0 0 0; font-size: 16px; color: #0891B2; font-weight: 600;">The NCRP Matrix Team</p>
                    </div>
                </div>
                <div style="background-color: #F9FAFB; padding: 20px; text-align: center; border-top: 1px solid #E5E7EB;">
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #9CA3AF;">This is an automated message, please do not reply to this email.</p>
                    <p style="margin: 0; font-size: 12px; color: #6B7280;">♥ Made with the help of PS Cyber Crime Bhiwani</p>
                </div>
            </div>
        </body>
        </html>
    """

    result = await _send_email(
        to_email=email,
        subject="Reset Your NCRP Matrix Password",
        html_content=html_content
    )

    logger.info(f"Password reset email send result: {result}")
    return result
