from typing import Dict, List, Any
import csv
import io
import base64
import logging
import re

logger = logging.getLogger(__name__)

def clean_account_number(account_number: str) -> str:
    """
    Clean account number by removing prefixes like 'A/C No.-:'

    Args:
        account_number: Account number string

    Returns:
        Cleaned account number
    """
    if not account_number:
        return ""

    # Convert to lowercase for case-insensitive replacement
    account_lower = account_number.lower()

    # Check if any of the prefixes exist
    if any(prefix in account_lower for prefix in ['a/c no.-:', 'a/c no.:', 'a/c no.', 'a/c']):
        # Replace all variations of the prefix
        result = account_number
        for prefix in ['A/C No.-:', 'A/C No.:', 'A/C No.', 'A/C']:
            result = result.replace(prefix, '')
        # Also try lowercase versions
        for prefix in ['a/c no.-:', 'a/c no.:', 'a/c no.', 'a/c']:
            result = result.replace(prefix, '')
        account_number = result.strip()

    # Remove standalone "-:" prefix that might appear at the beginning
    if account_number.startswith("-:"):
        account_number = account_number[2:]

    # Also check for other variations of the prefix
    if account_number.startswith("- :"):
        account_number = account_number[3:]
    if account_number.startswith("-: "):
        account_number = account_number[3:]

    return account_number.strip()

def format_transaction_for_csv(transaction: Dict[str, Any], is_sub_transaction: bool = False) -> Dict[str, Any]:
    # Note: is_sub_transaction parameter is kept for backward compatibility but no longer used
    """
    Format a transaction dictionary according to the standard header format.
    Simplified to handle only ID-based extraction format with specific fields.

    Header format: Layer | Sender Account | Sender Txn ID | Sender Bank | Receiver Account | Receiver Txn ID | Receiver Bank | Txn Type | Txn Date | Amount

    Args:
        transaction: Transaction dictionary
        is_sub_transaction: Whether this is a sub-transaction (for visual indication)

    Returns:
        Dictionary with standardized keys matching the header format
    """
    # Check if this is from the ID-based extraction (banking_upi)
    is_id_based = "sender_account" in transaction or "receiver_account" in transaction

    # Create a formatted transaction with only the specific fields
    formatted_txn = {
        "Layer": transaction.get("layer", ""),  # Allow empty layer if not found
        "Fraud Type": transaction.get("fraud_type", "banking_upi"),  # Include fraud type, default to banking_upi
        "Txn Type": transaction.get("txn_type", transaction.get("type", "")),  # Use txn_type with fallback to type
        "Txn Date": transaction.get("date", "") or "",  # Ensure date is never None
        "Amount": transaction.get("amount", ""),
        "Reference": ""
    }

    # For the reference column, use the receiver_info field which contains reference information
    if is_id_based and transaction.get("receiver_info"):
        formatted_txn["Reference"] = transaction.get("receiver_info", "")
    elif is_id_based and transaction.get("reference"):  # Fallback to reference for backward compatibility
        formatted_txn["Reference"] = transaction.get("reference", "")
    elif transaction.get("remarks"):  # Legacy fallback
        formatted_txn["Reference"] = transaction.get("remarks", "")

    if is_id_based:
        # This is from the ID-based extraction
        # Get account numbers and clean them up
        sender_account = transaction.get("sender_account", "")
        receiver_account = transaction.get("receiver_account", "")

        # Remove any prefix from account numbers
        sender_account = clean_account_number(sender_account)
        receiver_account = clean_account_number(receiver_account)

        formatted_txn["Sender Account"] = sender_account
        formatted_txn["Sender Bank"] = transaction.get("sender_bank", "")
        formatted_txn["Sender Txn ID"] = transaction.get("sender_transaction_id", "")  # Keep this in the data
        formatted_txn["Receiver Account"] = receiver_account
        formatted_txn["Receiver Bank"] = transaction.get("receiver_bank", "")
        formatted_txn["Txn ID"] = transaction.get("receiver_transaction_id", "")  # Renamed from 'Receiver Txn ID'

        # Add IFSC code to receiver account if available
        if transaction.get("receiver_ifsc"):
            formatted_txn["Receiver Account"] = f"{formatted_txn['Receiver Account']} (IFSC: {transaction.get('receiver_ifsc')})"
    else:
        # For legacy format, use the old logic
        is_credit = transaction.get("is_credit", False)

        # For credit transactions (money coming in), the current account is the receiver
        if is_credit:
            formatted_txn["Receiver Account"] = transaction.get("account_no", "")
            formatted_txn["Receiver Bank"] = transaction.get("bank_name", "")
            formatted_txn["Receiver Txn ID"] = transaction.get("transaction_id", "")
            formatted_txn["Sender Account"] = ""  # May be filled in later if known
            formatted_txn["Sender Bank"] = ""     # May be filled in later if known
            formatted_txn["Sender Txn ID"] = ""   # May be filled in later if known
        else:
            # For debit transactions (money going out), the current account is the sender
            formatted_txn["Sender Account"] = transaction.get("account_no", "")
            formatted_txn["Sender Bank"] = transaction.get("bank_name", "")
            formatted_txn["Sender Txn ID"] = transaction.get("transaction_id", "")
            formatted_txn["Receiver Account"] = ""  # May be filled in later if known
            formatted_txn["Receiver Bank"] = ""     # May be filled in later if known
            formatted_txn["Receiver Txn ID"] = ""   # May be filled in later if known

    # No longer adding visual indicators for sub-transactions as requested by user
    # The transaction type will be displayed as-is without any symbols or prefixes

    return formatted_txn

def generate_csv_from_transactions(transactions: List[Dict[str, Any]]) -> io.StringIO:
    """
    Generate CSV data from transaction list

    Args:
        transactions: List of transaction objects

    Returns:
        StringIO object containing CSV data
    """
    # Define CSV columns - only include the specific fields needed for ID-based extraction
    fieldnames = [
        'Layer',
        'Fraud Type',  # Add fraud type to preserve it during CSV export/import
        'Sender Account',
        'Sender Txn ID',  # We'll keep this in the data but hide it in the UI
        'Sender Bank',
        'Receiver Account',
        'Txn ID',  # Renamed from 'Receiver Txn ID'
        'Receiver Bank',
        'Txn Type',
        'Txn Date',
        'Amount',
        'Reference'
    ]

    # Create CSV in memory
    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()

    # First, organize the transactions by their relationships and sort by layer
    logger.info(f"Organizing {len(transactions)} transactions before generating CSV")
    organized_transactions = organize_transactions_by_relationships(transactions)
    logger.info(f"Organized transactions: {len(organized_transactions)} total (sorted by layer)")

    # We'll use a more sophisticated approach to identify sub-transactions
    # First, identify all main transactions
    main_txns = {}
    for i, txn in enumerate(organized_transactions):
        txn_type = txn.get('txn_type', txn.get('type', '')).lower()  # Use txn_type with fallback to type
        if 'money transfer to' in txn_type:
            # Store the index and receiver account
            receiver_account = txn.get('receiver_account', '').lower()
            receiver_txn_id = txn.get('receiver_transaction_id', '').lower()
            if receiver_account:
                key = f"{receiver_account}_{receiver_txn_id}" if receiver_txn_id else receiver_account
                main_txns[key] = i
                logger.debug(f"Main transaction {i}: {txn_type} - Receiver: {receiver_account}, Txn ID: {receiver_txn_id}, Key: {key}")

    # Now identify which transactions are sub-transactions of which main transactions
    sub_txn_of = {}
    for i, txn in enumerate(organized_transactions):
        txn_type = txn.get('type', '').lower() if txn.get('type') else ''
        if 'money transfer to' not in txn_type:
            sender_account = txn.get('sender_account', '').lower()
            sender_txn_id = txn.get('sender_transaction_id', '').lower()

            logger.debug(f"Checking transaction {i}: {txn_type} - Sender: {sender_account}, Txn ID: {sender_txn_id}")

            # Try to find a matching main transaction
            if sender_account:
                # First try exact match with transaction ID
                if sender_txn_id:
                    key = f"{sender_account}_{sender_txn_id}"
                    logger.debug(f"Trying exact match with key: {key}")
                    if key in main_txns:
                        main_idx = main_txns[key]
                        sub_txn_of[i] = main_idx
                        logger.debug(f"Exact match found! Transaction {i} is a sub-transaction of {main_idx}")
                        continue

                # Then try account-only match
                logger.debug(f"Trying account-only match with: {sender_account}")
                if sender_account in main_txns:
                    main_idx = main_txns[sender_account]
                    sub_txn_of[i] = main_idx
                    logger.debug(f"Account match found! Transaction {i} is a sub-transaction of {main_idx}")
                else:
                    logger.debug(f"No match found for transaction {i}")

    # Log the final matching results
    logger.info(f"Found {len(sub_txn_of)} sub-transactions matched to main transactions")

    # Write transaction rows in the organized order
    for i, txn in enumerate(organized_transactions):
        # Determine if this is a sub-transaction
        is_sub_transaction = i in sub_txn_of

        # Format the transaction for CSV
        row = format_transaction_for_csv(txn, is_sub_transaction)
        writer.writerow(row)

    # Reset buffer position
    output.seek(0)
    return output

def organize_transactions_by_relationships(transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Organize transactions by their relationships.
    Places sub-transactions directly under their parent transactions based on matching criteria.

    For card fraud type, implements a fallback mechanism for Layer 1 transactions:
    - If sender account is empty but sender transaction ID matches receiver account
    - And receiver account is a 16-digit number
    - Then use receiver account as sender account and clear sender transaction ID

    Matching criteria:
    1. Receiver account AND transaction ID of 'Money Transfer to' transactions must match
       the sender account AND transaction ID of sub-transactions
    2. Sub-transactions are placed directly below their main transaction

    Args:
        transactions: List of transaction objects

    Returns:
        List of organized transaction objects
    """
    logger.info(f"Organizing {len(transactions)} transactions by relationships")

    # Create deep copies to avoid modifying the original transactions
    transactions_copy = [transaction.copy() for transaction in transactions]

    # Add a unique ID to each transaction for tracking
    for i, txn in enumerate(transactions_copy):
        txn['_temp_id'] = i

    # Separate transactions into main transactions and sub-transactions
    main_txns = []
    sub_txns = []
    other_txns = []

    for txn in transactions_copy:
        txn_type = txn.get('type', '').lower() if txn.get('type') else ''
        if 'money transfer to' in txn_type:
            main_txns.append(txn)
        elif txn_type:  # Any transaction with a type that's not 'Money Transfer to'
            sub_txns.append(txn)
        else:  # Transactions without a type
            other_txns.append(txn)

    logger.info(f"Found {len(main_txns)} main transactions, {len(sub_txns)} sub-transactions, and {len(other_txns)} other transactions")

    # Final organized list
    organized_transactions = []
    processed_ids = set()

    # Process each main transaction and find its matching sub-transactions
    for main_txn in main_txns:
        if main_txn['_temp_id'] in processed_ids:
            continue

        # Get receiver account and transaction ID from main transaction
        receiver_account = main_txn.get('receiver_account', '').lower()
        receiver_txn_id = main_txn.get('receiver_transaction_id', '').lower()

        # Remove any prefix from receiver account (like "A/C No.-:")
        receiver_account = clean_account_number(receiver_account)

        logger.debug(f"Processing main transaction: Receiver Account {receiver_account}, Receiver Txn ID {receiver_txn_id}")

        # Add main transaction to the result
        organized_transactions.append(main_txn)
        processed_ids.add(main_txn['_temp_id'])

        # Skip if no receiver account or transaction ID
        if not receiver_account or not receiver_txn_id:
            continue

        # Find matching sub-transactions
        matching_sub_txns = []

        # Look for exact matches (both account AND transaction ID)
        for sub_txn in sub_txns:
            if sub_txn['_temp_id'] in processed_ids:
                continue

            sender_account = sub_txn.get('sender_account', '').lower()
            sender_txn_id = sub_txn.get('sender_transaction_id', '').lower()

            # Remove any prefix from sender account for consistent comparison
            sender_account = clean_account_number(sender_account)

            # Check if this is a Layer 1 transaction and card fraud type
            layer = sub_txn.get('layer')
            # Convert layer to string for comparison to handle both numeric and string values
            is_layer_1 = str(layer) == '1'
            fraud_type = sub_txn.get('fraud_type', '')
            is_card_fraud = fraud_type == 'card'

            # Log the layer value for debugging
            logger.debug(f"Transaction layer: {layer}, is_layer_1: {is_layer_1}, fraud_type: {fraud_type}, is_card_fraud: {is_card_fraud}")

            # Fallback mechanism for Layer 1 transactions with card fraud type:
            # If sender account is not present but sender transaction ID matches receiver account
            # and receiver account is a 16-digit numerical value, use it as sender account
            logger.debug(f"Checking fallback conditions: is_card_fraud={is_card_fraud}, is_layer_1={is_layer_1}, sender_account empty={not sender_account or sender_account.strip() == ''}, sender_txn_id={sender_txn_id}")

            if is_card_fraud and is_layer_1 and (not sender_account or sender_account.strip() == '') and sender_txn_id:
                # Clean up receiver account to handle any prefixes
                clean_receiver_account = clean_account_number(receiver_account)

                logger.debug(f"Fallback check: sender_txn_id={sender_txn_id}, clean_receiver_account={clean_receiver_account}, is_digit={clean_receiver_account.isdigit() if clean_receiver_account else False}, length={len(clean_receiver_account) if clean_receiver_account else 0}")

                # Check if sender transaction ID matches cleaned receiver account
                if (sender_txn_id == clean_receiver_account and
                    clean_receiver_account.isdigit() and
                    len(clean_receiver_account) == 16):
                    logger.info(f"Card Fraud Fallback: Using receiver account as sender account for Layer 1 transaction. Receiver account: {clean_receiver_account}")
                    # Use cleaned receiver account as sender account
                    sub_txn['sender_account'] = clean_receiver_account
                    # Clear sender transaction ID as requested
                    sub_txn['sender_transaction_id'] = ''
                    # Update sender_account for matching
                    sender_account = clean_receiver_account
                    sender_txn_id = ''

                    logger.info(f"Fallback applied: Updated sender_account={sender_account}, sender_txn_id={sender_txn_id}")
                else:
                    logger.debug(f"Fallback conditions not met: sender_txn_id={sender_txn_id} != clean_receiver_account={clean_receiver_account} or not 16-digit number")

            # Skip if no sender account or transaction ID after fallback attempt
            if not sender_account:
                continue

            # Check for exact match (account AND transaction ID)
            account_match = sender_account == receiver_account
            txn_id_match = sender_txn_id == receiver_txn_id if sender_txn_id and receiver_txn_id else False

            if account_match and (txn_id_match or not sender_txn_id):
                matching_sub_txns.append(sub_txn)
                processed_ids.add(sub_txn['_temp_id'])
                logger.debug(f"Match found: Account {sender_account}, Txn ID {sender_txn_id}")

        # Sort matching sub-transactions by type
        matching_sub_txns.sort(key=lambda x: x.get('type', ''))

        # Add matching sub-transactions immediately after the main transaction
        organized_transactions.extend(matching_sub_txns)

        logger.info(f"Added {len(matching_sub_txns)} sub-transactions for main transaction with receiver account {receiver_account}")

    # Add remaining sub-transactions
    remaining_sub_txns = [txn for txn in sub_txns if txn['_temp_id'] not in processed_ids]

    # Sort remaining sub-transactions by type
    remaining_sub_txns.sort(key=lambda x: x.get('type', ''))
    organized_transactions.extend(remaining_sub_txns)

    logger.info(f"Added {len(remaining_sub_txns)} remaining sub-transactions")

    # Add other transactions
    for txn in other_txns:
        if txn['_temp_id'] not in processed_ids:
            organized_transactions.append(txn)
            processed_ids.add(txn['_temp_id'])

    # Remove temporary IDs
    for txn in organized_transactions:
        if '_temp_id' in txn:
            del txn['_temp_id']

    # Sort the final organized transactions by layer number
    # First, group transactions by their relationships (main transactions with their sub-transactions)
    transaction_groups = []
    current_group = []

    for txn in organized_transactions:
        txn_type = txn.get('type', '').lower() if txn.get('type') else ''

        # If this is a main transaction and we already have a group, add the current group to our groups list
        if 'money transfer to' in txn_type and current_group:
            transaction_groups.append(current_group)
            current_group = [txn]
        else:
            # Add to the current group (either a sub-transaction or the first main transaction)
            current_group.append(txn)

    # Add the last group if it exists
    if current_group:
        transaction_groups.append(current_group)

    # Log the layers before sorting
    logger.info("Transaction groups before sorting:")
    for i, group in enumerate(transaction_groups):
        if group:
            main_txn = group[0]
            layer = main_txn.get('layer', 'Unknown')
            txn_type = main_txn.get('type', '')
            logger.info(f"  Group {i}: Layer {layer}, Type {txn_type}, Size {len(group)}")

    # Sort the groups by the layer of their main transaction (first transaction in each group)
    def get_layer_value(group):
        if not group:
            return float('inf')

        main_txn = group[0]
        layer = main_txn.get('layer')

        if layer is None:
            return float('inf')

        # Try to convert to float if it's a string
        if isinstance(layer, str):
            try:
                return float(layer)
            except (ValueError, TypeError):
                # If conversion fails, log warning and use infinity (will sort to the end)
                logger.warning(f"Could not convert layer value '{layer}' to float, using infinity for sorting")
                return float('inf')

        # Try to convert to float if it's another type
        try:
            return float(layer)
        except (ValueError, TypeError):
            # If conversion fails, log warning and use infinity (will sort to the end)
            logger.warning(f"Could not convert layer value '{layer}' to float, using infinity for sorting")
            return float('inf')

    transaction_groups.sort(key=get_layer_value)

    # Log the layers after sorting
    logger.info("Transaction groups after sorting:")
    for i, group in enumerate(transaction_groups):
        if group:
            main_txn = group[0]
            layer = main_txn.get('layer', 'Unknown')
            txn_type = main_txn.get('type', '')
            logger.info(f"  Group {i}: Layer {layer}, Type {txn_type}, Size {len(group)}")

    # Flatten the groups back into a single list
    final_organized_transactions = []
    for group in transaction_groups:
        final_organized_transactions.extend(group)

    logger.info(f"Organized transactions: {len(final_organized_transactions)} total (sorted by layer)")
    return final_organized_transactions

def parse_csv_to_transactions(csv_data: str) -> List[Dict[str, Any]]:
    """
    Parse CSV data back into transaction objects.
    Simplified to handle only ID-based extraction format with specific fields.

    Args:
        csv_data: CSV data as string

    Returns:
        List of transaction objects with ID-based format
    """
    transactions = []

    if not csv_data or not csv_data.strip():
        logger.error("Empty CSV data provided")
        return transactions

    try:
        # Clean up any metadata at the top of the CSV
        lines = csv_data.split('\n')

        # If there are lines that look like base64 or dates at the top, remove them
        if lines and (lines[0].startswith('Z0') or
                   re.match(r'^\d{1,2}/\d{1,2}/\d{4}$', lines[0]) or
                   not ',' in lines[0]):
            # Find the first line that looks like a CSV header
            header_index = -1
            for i, line in enumerate(lines):
                if ',' in line and ('Layer' in line or line.startswith('Layer')):
                    header_index = i
                    break

            if header_index > 0:
                logger.info(f"Removing {header_index} non-CSV lines from the beginning of the data")
                csv_data = '\n'.join(lines[header_index:])
            elif header_index == -1:
                logger.warning("Could not find CSV header in the data")
                # Continue anyway, the DictReader will handle it

        # Read CSV from string
        csv_file = io.StringIO(csv_data)
        reader = csv.DictReader(csv_file)

        # Check if reader has any rows
        if not reader.fieldnames:
            logger.error("CSV data has no headers")
            return transactions

        # Log the headers for debugging
        logger.info(f"CSV headers: {reader.fieldnames}")

        # Convert each row to a transaction object
        row_count = 0
        for row in reader:
            row_count += 1
            # Create a transaction object with only the specific fields
            # Note: The transaction date is only for this specific transaction
            # and should not affect the original complaint date
            transaction = {
                'fraud_type': row.get('Fraud Type', 'banking_upi'),  # Use the fraud type from CSV or default to banking_upi
                'txn_type': row.get('Txn Type', ''),  # Use txn_type instead of type
                'date': row.get('Txn Date', '') or '',  # Transaction date only, not complaint date
                'amount': row.get('Amount', ''),
                'sender_account': row.get('Sender Account', ''),
                'sender_bank': row.get('Sender Bank', ''),
                'sender_transaction_id': row.get('Sender Txn ID', ''),
                'receiver_account': row.get('Receiver Account', ''),
                'receiver_bank': row.get('Receiver Bank', ''),
                'receiver_transaction_id': row.get('Txn ID', '')  # Get from 'Txn ID' column (renamed from 'Receiver Txn ID')
            }

            # Handle layer separately to allow for None or empty values
            layer_value = row.get('Layer', '')
            if layer_value:
                try:
                    # Try to convert to integer if it's a numeric string
                    transaction['layer'] = int(layer_value)
                except (ValueError, TypeError):
                    # If conversion fails, keep as string
                    logger.warning(f"Could not convert layer value '{layer_value}' to integer, keeping as string")
                    transaction['layer'] = layer_value
            else:
                # If empty, set to None to indicate unknown layer
                transaction['layer'] = None

            # Extract IFSC code if present in receiver account
            if transaction['receiver_account']:
                try:
                    ifsc_match = re.search(r'IFSC:\s*([A-Z0-9]+)', transaction['receiver_account'])

                    if ifsc_match:
                        transaction['receiver_ifsc'] = ifsc_match.group(1)
                        # Clean up the account number by removing the IFSC part
                        transaction['receiver_account'] = re.sub(r'\s*\(IFSC:.*\)', '', transaction['receiver_account'])
                except Exception as e:
                    logger.warning(f"Error extracting IFSC code: {str(e)}")
                    # Continue without extracting IFSC code

            # Use the reference column from the CSV directly
            # This preserves any cheque numbers and other information
            transaction['reference'] = row.get('Reference', "")
            # Also set receiver_info for backward compatibility
            transaction['receiver_info'] = row.get('Reference', "")

            # Set default values for fields that might be missing
            # This ensures consistent data structure
            if 'bank_name' not in transaction:
                transaction['bank_name'] = transaction.get('receiver_bank', '')

            if 'account_no' not in transaction:
                transaction['account_no'] = transaction.get('receiver_account', '')

            if 'transaction_id' not in transaction:
                transaction['transaction_id'] = transaction.get('receiver_transaction_id', '')

            # Set isMainTransaction flag for 'Money Transfer to' transactions
            if transaction.get('txn_type', '').lower() == 'money transfer to':
                transaction['isMainTransaction'] = True
            else:
                transaction['isMainTransaction'] = False

            # Ensure amount is a string
            if transaction.get('amount') is not None and not isinstance(transaction.get('amount'), str):
                transaction['amount'] = str(transaction.get('amount'))

            # Ensure date is a string
            if transaction.get('date') is not None and not isinstance(transaction.get('date'), str):
                transaction['date'] = str(transaction.get('date'))

            transactions.append(transaction)

        logger.info(f"Successfully parsed {row_count} rows from CSV data")

    except Exception as e:
        logger.error(f"Error parsing CSV data: {str(e)}")
        # Return empty list instead of raising an exception
        return []

    # Return the transactions without organizing them here
    # The organization will be done in generate_csv_from_transactions
    return transactions

def csv_to_base64(csv_data) -> str:
    """
    Convert CSV data to base64 encoded string

    Args:
        csv_data: CSV data (StringIO, string, or bytes)

    Returns:
        Base64 encoded string
    """
    try:
        # Handle different input types
        if isinstance(csv_data, io.StringIO):
            # StringIO object - get the string value and encode to bytes
            csv_bytes = csv_data.getvalue().encode('utf-8')
        elif isinstance(csv_data, str):
            # String - encode to bytes
            csv_bytes = csv_data.encode('utf-8')
        elif isinstance(csv_data, bytes):
            # Already bytes
            csv_bytes = csv_data
        else:
            # Try to convert to string and then to bytes
            csv_bytes = str(csv_data).encode('utf-8')

        # Encode to base64
        base64_data = base64.b64encode(csv_bytes).decode('utf-8')
        logger.info(f"Successfully converted CSV data to base64 (length: {len(base64_data)})")
        return base64_data
    except Exception as e:
        logger.error(f"Error converting CSV data to base64: {str(e)}")
        # Return empty string instead of raising an exception
        return ""

def base64_to_csv(base64_data: str) -> str:
    """
    Convert base64 encoded string to CSV data

    Args:
        base64_data: Base64 encoded string

    Returns:
        CSV data as string
    """
    try:
        # Check if the input is empty
        if not base64_data:
            logger.warning("Empty base64 data provided")
            return ""

        # Remove any whitespace or newlines that might be in the base64 string
        base64_data = base64_data.strip()

        # Check if the data is already in CSV format (contains commas and newlines)
        if ',' in base64_data and '\n' in base64_data:
            logger.info("Data appears to be in CSV format already, returning as-is")
            return base64_data

        # Try to decode the base64 data
        try:
            csv_bytes = base64.b64decode(base64_data)
            csv_text = csv_bytes.decode('utf-8')
            logger.info(f"Successfully decoded base64 data to CSV (length: {len(csv_text)})")
            return csv_text
        except Exception as e:
            logger.error(f"Error decoding base64 data: {str(e)}")

            # If decoding fails, check if the data might be a CSV string already
            if ',' in base64_data:
                logger.warning("Base64 decoding failed but data contains commas, treating as CSV")
                return base64_data

            # If all else fails, return an empty string
            return ""
    except Exception as e:
        logger.error(f"Unexpected error in base64_to_csv: {str(e)}")
        return ""
