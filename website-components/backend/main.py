import logging
import os
import asyncio
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from middleware.robust_session import RobustSessionMiddleware
from middleware.robust_csrf import RobustCSRFMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from routes import complaints, extraction, user_templates, notices, reference_data

from routes.auth import router as auth_router
from routes.user_subscription import router as subscription_router
from routes.security import router as security_router
from routes.analysis import router as analysis_router

# Removed health_router import
# Import database functions when needed to avoid circular imports
from services.auth_service import SECRET_KEY
# Import cleanup_service functions when needed to avoid circular imports
from middleware.logging_middleware import RequestLoggingMiddleware
from middleware.security_middleware import SecurityHeadersMiddleware

# Removed CSRFMiddleware import as we're using RobustCSRFMiddleware instead
from pathlib import Path
from utils.encoders import custom_jsonable_encoder
import fastapi.encoders

# Setup logging
log_level = os.getenv("LOG_LEVEL", "DEBUG").upper()
level = getattr(logging, log_level, logging.DEBUG)
if not isinstance(level, int):
    level = logging.DEBUG
logging.basicConfig(
    level=level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]  # Start with just console logging
)

# Set up log rotation
from utils.log_rotation import setup_log_rotation, cleanup_old_logs

# Define log directory
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)  # Create logs directory if it doesn't exist

# Clean up old logs first
cleanup_old_logs(log_dir, max_age_days=30)

# Set up log rotation with different settings based on environment
if os.getenv("ENVIRONMENT", "development") == "production":
    # Production: 50MB max file size, 20 backups
    setup_log_rotation(log_dir, max_bytes=52428800, backup_count=20)

    # Use structured logger for production
    from utils.structured_logger import get_structured_logger
    logger = get_structured_logger(__name__)
else:
    # Development: 10MB max file size, 5 backups
    setup_log_rotation(log_dir, max_bytes=10485760, backup_count=5)
    logger = logging.getLogger(__name__)

# Patch FastAPI's jsonable_encoder with our custom version that handles ObjectId
fastapi.encoders.jsonable_encoder = custom_jsonable_encoder
logger.info("Patched FastAPI's jsonable_encoder to handle MongoDB ObjectId")

# Load environment variables with production priority
environment = os.getenv("ENVIRONMENT", "development")

# Define environment file paths with production priority
env_paths = []
if environment == "production":
    env_paths = [
        Path('.env.production'),                # Production config first
        Path('backend/.env.production'),        # From project root
        Path(os.path.dirname(__file__) + '/.env.production'),  # Same directory
        Path('.env'),                          # Fallback to development
        Path('backend/.env'),                  # Fallback from project root
        Path(os.path.dirname(__file__) + '/.env')  # Fallback same directory
    ]
else:
    env_paths = [
        Path('.env'),                # Development config first
        Path('backend/.env'),        # From project root
        Path(os.path.dirname(__file__) + '/.env')  # Same directory as this file
    ]

env_loaded = False
env_file_used = None
for env_path in env_paths:
    if env_path.exists():
        logger.info(f"Loading environment from: {env_path.absolute()}")
        load_dotenv(dotenv_path=env_path, verbose=False)  # Disable verbose in production
        env_loaded = True
        env_file_used = str(env_path)
        break

if not env_loaded:
    logger.warning("No .env file found in any of the expected locations")
    if environment == "production":
        logger.info("Production environment - using Coolify environment variables")
        # In production with Coolify, environment variables are injected directly
        # No .env file is needed
    else:
        logger.warning("Development environment - consider creating .env file")
else:
    logger.info(f"Environment loaded from: {env_file_used} for {environment} mode")

# Validate critical environment variables for production
if environment == "production":
    critical_vars = [
        "DATABASE_URL",
        "SECRET_KEY",
        "SESSION_SECRET_KEY",
        "FRONTEND_ORIGIN",
        "PRODUCTION_DOMAIN",
        "ALLOWED_HOSTS"
    ]

    missing_vars = []
    for var in critical_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.critical(f"Missing critical environment variables for production: {missing_vars}")
        raise RuntimeError(f"Production deployment requires these environment variables: {missing_vars}")

    # Validate that production secrets are not using default/development values
    dev_secret = "0dcdb7d75acb4ab68dbe0e1e2b58ab817651e8a26099ad7798e22942c1cd4e72"
    if os.getenv("SECRET_KEY") == dev_secret or os.getenv("SESSION_SECRET_KEY") == dev_secret:
        logger.critical("Production environment is using development secrets - SECURITY RISK!")
        raise RuntimeError("Production must use unique secrets, not development defaults")

    logger.info("✅ Production environment validation passed")

# Background task for scheduled cleanup
async def scheduled_cleanup_task():
    """
    Background task that runs cleanup operations periodically
    """
    # Run every 6 hours
    cleanup_interval = 6 * 60 * 60  # 6 hours in seconds

    while True:
        try:
            logger.info("Running scheduled database cleanup")
            # Import cleanup functions here to avoid circular imports
            from services.cleanup_service import run_cleanup_tasks

            # Call the async cleanup function
            cleanup_result = await run_cleanup_tasks()
            if cleanup_result:
                logger.info(f"Scheduled cleanup completed successfully, next run in {cleanup_interval} seconds")
            else:
                logger.warning(f"Scheduled cleanup completed with issues, next run in {cleanup_interval} seconds")
        except Exception as e:
            logger.error(f"Error in scheduled cleanup: {str(e)}", exc_info=True)
            logger.warning(f"Scheduled cleanup completed with issues, next run in {cleanup_interval} seconds")

        # Wait for the next interval
        await asyncio.sleep(cleanup_interval)

# Lifespan context manager for startup/shutdown events
@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: Create DB indexes
    logger.info("Starting application...")

    # Debug: Print DATABASE_URL from environment
    db_url = os.getenv("DATABASE_URL")
    logger.info(f"DATABASE_URL from environment: {db_url[:20]}..." if db_url else "DATABASE_URL not set")

    # Check database connection
    try:
        # Import check_connection function here to avoid circular imports
        from database import check_connection

        # Always await the connection check to ensure proper async handling
        connection_result = await check_connection()

        # Process the result
        db_connected = connection_result if isinstance(connection_result, bool) else connection_result.get('connected', False)
        if not db_connected:
            logger.error("Failed to connect to database")
            # In production, fail fast
            if os.getenv("ENVIRONMENT") == "production":
                raise RuntimeError("Cannot start application without database connection")
            else:
                # In development, show warning but continue (for easier local development)
                logger.warning("⚠️ Running with limited functionality - database connection failed")
    except Exception as e:
        logger.error(f"Error checking database connection: {str(e)}")
        db_connected = False
        # In production, fail fast
        if os.getenv("ENVIRONMENT") == "production":
            raise RuntimeError(f"Cannot start application: {str(e)}")
        else:
            # In development, show warning but continue
            logger.warning(f"⚠️ Running with limited functionality - database error: {str(e)}")

    # Create database indexes if connected
    if db_connected:
        # Try to create indexes but continue even if there are errors
        try:
            # Import create_indexes function here to avoid circular imports
            from database import create_indexes

            # Always await the function to ensure proper async handling
            index_result = await create_indexes()

            if not index_result:
                logger.warning("Some database indexes could not be created, but continuing startup")
            else:
                logger.info("All database indexes created successfully")
        except Exception as e:
            logger.error(f"Error creating database indexes: {str(e)}")
            logger.warning("Continuing startup despite index creation error")

        # Start the scheduled cleanup task
        # Only start in production or if explicitly enabled
        if os.getenv("ENVIRONMENT") == "production" or os.getenv("ENABLE_CLEANUP") == "true":
            logger.info("Starting scheduled cleanup task")
            # Start the background task
            asyncio.create_task(scheduled_cleanup_task())
        else:
            logger.info("Scheduled cleanup task disabled in development mode")

        # Run an initial cleanup
        try:
            logger.info("Running initial database cleanup")
            from services.cleanup_service import run_cleanup_tasks
            cleanup_result = run_cleanup_tasks()
            if cleanup_result:
                logger.info("Initial cleanup completed successfully")
            else:
                logger.warning("Initial cleanup completed with issues")
        except Exception as e:
            logger.error(f"Error in initial cleanup: {str(e)}", exc_info=True)
            logger.warning("Initial cleanup completed with issues")

    yield

    # Shutdown: Close connections
    logger.info("Shutting down application...")

# Create FastAPI app with optimizations for 100+ concurrent users
app = FastAPI(
    title="NCRP Matrix API",
    description="Backend API for NCRP Matrix application optimized for 100+ concurrent users",
    version="1.0.0",
    lifespan=lifespan,
    # Optimize for concurrent users
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
    # Request size limits for file uploads
    max_request_size=50 * 1024 * 1024,  # 50MB max request size
    # Performance optimizations
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}" if route.tags else route.name,
)

# Add root endpoint to prevent unhandled redirects
@app.get("/")
async def root():
    return {"message": "Welcome to NCRP Matrix API"}

# Performance monitoring endpoint
@app.get("/api/health/performance")
async def get_performance_metrics():
    """Get current performance metrics for monitoring"""
    try:
        from services.performance_service import get_performance_metrics
        from datetime import datetime, timezone
        import psutil

        # Get application metrics
        app_metrics = get_performance_metrics()

        # Get system metrics
        system_metrics = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "active_connections": len(psutil.net_connections()),
        }

        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "application_metrics": app_metrics,
            "system_metrics": system_metrics
        }
    except Exception as e:
        from datetime import datetime, timezone
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# Update CORS settings with enhanced security
FRONTEND_ORIGIN = os.getenv("FRONTEND_ORIGIN", "http://localhost:3001")
PRODUCTION_DOMAIN = os.getenv("PRODUCTION_DOMAIN", "https://api.ncrpmatrix.in")
environment = os.getenv("ENVIRONMENT", "development")

# Configure CORS origins based on environment
if environment == "development":
    # Allow multiple origins in development mode for easier debugging
    allowed_origins = [
        FRONTEND_ORIGIN,
        "http://127.0.0.1:3001",
        "http://localhost:3001",
        "http://localhost:8080",  # Common webpack port
        "http://127.0.0.1:8080"
    ]
    logger.info(f"🔧 CORS configured for development: {len(allowed_origins)} origins allowed")
else:
    # In production, only allow trusted frontend origins
    allowed_origins = [
        "https://app.ncrpmatrix.in",
        "https://ncrpmatrix.in",
        "https://api.ncrpmatrix.in"
    ]

    # Remove duplicates and ensure we have at least one origin
    allowed_origins = list(set(allowed_origins))

    if not allowed_origins:
        logger.critical("No valid CORS origins configured for production!")
        raise RuntimeError("Production requires valid CORS origins")

    logger.info(f"🔒 CORS configured for production: {allowed_origins}")

# Add CORS middleware FIRST to ensure CORS headers are applied before any other middleware
# This is critical for handling preflight requests and ensuring CORS headers are present
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "X-CSRF-Token", "Authorization", "X-Requested-With", "Access-Control-Allow-Origin"],
    max_age=3600  # Cache preflight requests for 60 minutes (increased from 10 minutes)
)

# Log CORS configuration (safely for production)
if environment == "development":
    logger.info(f"CORS middleware configured with origins: {allowed_origins}")
else:
    logger.info(f"CORS middleware configured with {len(allowed_origins)} production origins")
logger.info("CORS middleware configured with credentials allowed")

# Add robust session middleware after CORS but before other middleware
# This ensures that the session is always available for all requests
app.add_middleware(
    RobustSessionMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY") or str(SECRET_KEY),
    max_age=60 * 24 * 7 * 60,  # 7 days in seconds
    same_site="lax",  # Prevents CSRF in modern browsers while allowing normal navigation
    https_only=os.getenv("ENVIRONMENT") == "production",  # Secure cookies in production
    path="/",  # Restrict cookies to our domain only
    cookie_domain=".ncrpmatrix.in" if os.getenv("ENVIRONMENT") == "production" else None,  # Set domain for cross-subdomain
)

# Log session middleware configuration
logger.info("Robust session middleware configured with 7-day session timeout")

# Add security middlewares
ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")
app.add_middleware(TrustedHostMiddleware, allowed_hosts=ALLOWED_HOSTS)

# Add custom middlewares - order is important!
# First add the request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# Then add security headers middleware
app.add_middleware(SecurityHeadersMiddleware)

# Define exempt paths for CSRF middleware
csrf_exempt_paths = [
    "/security/csp-report",  # CSP reports come from browsers and can't include CSRF tokens
    "/csrf/token"
]
app.add_middleware(
    RobustCSRFMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY") or str(SECRET_KEY),
    cookie_name="csrf_token",
    header_name="X-CSRF-Token",
    cookie_max_age=3600,  # 1 hour
    safe_methods=["GET", "HEAD", "OPTIONS"],
    exempt_paths=csrf_exempt_paths,
    same_site="lax",
    https_only=os.getenv("ENVIRONMENT") == "production",
    path="/"
)

logger.info("CSRF middleware configured with double-submit pattern")
logger.info("CSRF cookie is NOT httponly to allow JavaScript to read it for the double-submit pattern")
csrf_log_level = logging.DEBUG if os.getenv("ENVIRONMENT") == "development" else logging.INFO
logging.getLogger("middleware.robust_csrf").setLevel(csrf_log_level)
logger.info("Middleware setup complete with robust CSRF protection")

# Rate limiting middleware disabled for simplicity
"""
if os.getenv("ENVIRONMENT", "development") == "production":
app.add_middleware(
    RobustSessionMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY") or str(SECRET_KEY),
    max_age=60 * 24 * 7 * 60,  # 7 days in seconds
    same_site="lax",  # Default to lax, will be overridden to 'none' if cookie_domain is set and secure
    https_only=os.getenv("ENVIRONMENT") == "production",  # Secure cookies in production
    path="/",
    )
else:
    app.add_middleware(
        RateLimitMiddleware,
        general_rate_limit=1000,
        auth_rate_limit=500,
        window_seconds=60,
        enable_ip_blocking=False
    )
"""

# Include routers
app.include_router(auth_router, prefix="/auth", tags=["Authentication"])
app.include_router(complaints.router, prefix="/complaints", tags=["Complaints"])
app.include_router(extraction.router, prefix="/extraction", tags=["Extraction"])
app.include_router(user_templates.router, tags=["User Templates"])
app.include_router(notices.router, prefix="/notices", tags=["Notices"])
app.include_router(reference_data.router, prefix="/reference", tags=["Reference Data"])

app.include_router(subscription_router, tags=["User Subscription"])
app.include_router(security_router, prefix="/security", tags=["Security"])
app.include_router(analysis_router, prefix="/analysis", tags=["Analysis"])

# Removed health check router inclusion
# Import and include CSRF router
from routes.csrf import router as csrf_router
app.include_router(csrf_router, tags=["CSRF"])

# Import standardized error handling utilities
from utils.error_utils import create_error_response
from utils.cors_utils import add_cors_headers

# Custom exception handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(_: Request, exc: RequestValidationError):
    """
    Handle validation errors with standardized format
    """
    # Clean up validation errors to be more user-friendly
    errors = []
    for error in exc.errors():
        # Extract only necessary information
        errors.append({
            "loc": error.get("loc", []),
            "msg": error.get("msg", "Validation error")
        })

    # Use standardized error response format
    error_response = create_error_response(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message="Validation error",
        error_type="validation_error",
        details={"errors": errors},
        log_error=True,
        log_level="warning"
    )

    # Create response with CORS headers
    response = JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )

    # Add CORS headers to ensure frontend can read the error
    return add_cors_headers(response, _)

# Add a generic exception handler to prevent exposing sensitive information
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    """
    Enhanced global exception handler with standardized error responses

    - Provides better error categorization
    - Includes more detailed logging
    - Handles database connection errors gracefully
    - Prevents sensitive information leakage
    """
    # Log the actual error for debugging with traceback
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)

    # Get request information for better debugging
    request_id = request.headers.get("X-Request-ID", "unknown")
    path = request.url.path
    method = request.method
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("User-Agent", "unknown")

    # Log detailed request information
    logger.error(
        f"Exception occurred in {method} {path} (Request ID: {request_id}, "
        f"IP: {client_ip}, User-Agent: {user_agent[:50]}{'...' if len(user_agent) > 50 else ''})"
    )

    # Common details to include in all error responses
    common_details = {
        "request_id": request_id,
        "path": path,
        "method": method
    }

    # Determine if this is a known error type that we can provide more specific messaging for
    if isinstance(exc, asyncio.TimeoutError):
        error_response = create_error_response(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            message="The request timed out. Please try again with a smaller dataset or contact support.",
            error_type="timeout_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif isinstance(exc, MemoryError):
        error_response = create_error_response(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            message="The request is too large to process. Please try with a smaller dataset.",
            error_type="memory_limit_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif "database" in str(exc).lower() or "mongo" in str(exc).lower():
        # Handle database connection errors
        logger.critical(f"Database error: {str(exc)}")
        error_response = create_error_response(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            message="Database service is currently unavailable. Please try again later.",
            error_type="database_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif "permission" in str(exc).lower() or "access" in str(exc).lower():
        # Handle permission errors
        error_response = create_error_response(
            status_code=status.HTTP_403_FORBIDDEN,
            message="You don't have permission to perform this action.",
            error_type="permission_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif "not found" in str(exc).lower() or "does not exist" in str(exc).lower():
        # Handle not found errors
        error_response = create_error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="The requested resource was not found.",
            error_type="not_found_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=error_response
        )
        return add_cors_headers(response, request)

    # Return a generic error message for all other exceptions
    error_response = create_error_response(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message="An unexpected error occurred. Please try again or contact support if the issue persists.",
        error_type="server_error",
        details=common_details,
        log_error=False  # Already logged above
    )
    response = JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )
    return add_cors_headers(response, request)

# Removed health check endpoint
# Removed CSP Report endpoint
@app.post("/api/csp-report")
async def csp_report(request: Request):
    """
    Endpoint to receive Content Security Policy violation reports.

    This helps identify and fix CSP issues in the application.
    """
    try:
        report_data = await request.json()

        # Log the CSP violation report
        logger.warning(
            "CSP Violation Report",
            extra={
                "csp_report": report_data,
                "user_agent": request.headers.get("User-Agent"),
                "remote_addr": request.client.host if request.client else "unknown"
            }
        )

        return {"status": "received"}
    except Exception as e:
        logger.error(f"Error processing CSP report: {str(e)}")
        return {"status": "error", "message": "Failed to process report"}

# Database health check endpoint (admin only)
@app.get("/admin/db-health")
async def db_health_check(request: Request):
    """
    Detailed database health check endpoint

    This endpoint provides detailed information about the database connection,
    performance metrics, and server status. It's intended for admin use only.
    """
    from database import get_db_metrics
    from datetime import datetime, timezone
    from fastapi import HTTPException

    # Check for admin authorization
    # In a production environment, this should be properly secured
    # For now, we'll use a simple API key check
    api_key = request.headers.get("X-Admin-API-Key")
    admin_api_key = os.getenv("ADMIN_API_KEY")

    if not api_key or api_key != admin_api_key:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin API key required"
        )

    # Get detailed database metrics
    try:
        metrics = await get_db_metrics()
        return {
            "success": True,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "database_metrics": metrics
        }
    except Exception as e:
        logger.error(f"Error getting database metrics: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
