from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

# User Model
class UserCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, max_length=50)
    designation: Optional[str] = Field(None, max_length=100)

class User(BaseModel):
    email: EmailStr
    hashed_password: str
    first_name: str
    last_name: Optional[str] = None
    designation: Optional[str] = None
    created_on: datetime
    last_login: Optional[datetime] = None
    active: bool = True  # Set to True by default
    email_verified: bool = False  # Default to not verified
    verification_otp: Optional[str] = None  # OTP for email verification
    verification_otp_expires: Optional[datetime] = None  # OTP expiry time
    paid: bool = False  # Default to unpaid
    subscription_start_date: Optional[datetime] = None  # When the subscription started
    subscription_expires: Optional[datetime] = None  # When the paid subscription expires
    complaint_count: int = 0  # Track number of complaints
    has_template: bool = False  # Track if user has uploaded a template
    template_filename: Optional[str] = None  # Filename of the uploaded template
    two_step_enabled: bool = False  # Whether two-step verification is enabled
    login_otp: Optional[str] = None  # OTP for two-step verification during login
    login_otp_expires: Optional[datetime] = None  # Login OTP expiry time
    failed_login_attempts: int = 0  # Track failed login attempts
    last_failed_login: Optional[datetime] = None  # Time of last failed login attempt

# Response Models
class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: Optional[str] = None
    designation: Optional[str] = None
    organization: Optional[str] = None
    paid: bool = False
    subscription_start_date: Optional[datetime] = None
    subscription_expires: Optional[datetime] = None
    complaint_count: int = 0
    email_verified: bool = False
    has_template: bool = False
    two_step_enabled: bool = False
    message: Optional[str] = None

class VerifyOTPRequest(BaseModel):
    email: EmailStr
    otp: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str
    user: Dict[str, Any]

class RefreshTokenRequest(BaseModel):
    refresh_token: str

# Complaint Model (Now linked to users)
class Complaint(BaseModel):
    complaint_number: Optional[str] = None
    complainant_name: Optional[str] = None
    amount: Optional[float] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    date: Optional[str] = None  # Changed from datetime to str to handle various date formats
    status: Optional[str] = "Pending"
    user_id: Optional[str] = None  # ✅ Reference to the user who uploaded this complaint
    graph_data: Optional[Any] = None  # Graph data can be string or dict
    bank_notice_data: Optional[dict] = None  # Bank notice data structure
    csv_data_base64: Optional[str] = None  # Base64 encoded CSV file
    notes: Optional[str] = None  # User notes for the complaint
    metadata: Optional[dict] = None  # Separate metadata object copied from graph_data.metadata (for display only - NOT used in updates)
    # Note: summary field removed - all data is now consolidated in metadata

class EmailRequest(BaseModel):
    email: EmailStr

class PasswordReset(BaseModel):
    password: str

class TwoStepVerificationRequest(BaseModel):
    email: EmailStr
    password: str

class TwoStepVerificationOTPRequest(BaseModel):
    email: EmailStr
    otp: str

class TwoStepSettingUpdate(BaseModel):
    enabled: bool

class UserProfileUpdate(BaseModel):
    firstName: Optional[str] = Field(None, max_length=50)
    lastName: Optional[str] = Field(None, max_length=50)
    designation: Optional[str] = Field(None, max_length=100)
    organization: Optional[str] = Field(None, max_length=100)

# ATM and Nodal Officer Models
class ATM(BaseModel):
    atm_id: str = Field(..., description="Unique identifier for the ATM")
    bank_name: str = Field(..., description="Name of the bank")
    address: str = Field(..., description="Physical address of the ATM")
    city: str = Field(..., description="City where ATM is located")
    state: str = Field(..., description="State where ATM is located")
    pincode: Optional[str] = Field(None, description="PIN code of the location")
    latitude: Optional[float] = Field(None, description="Latitude coordinates")
    longitude: Optional[float] = Field(None, description="Longitude coordinates")
    is_active: bool = Field(True, description="Whether the ATM is currently active")

class NodalOfficer(BaseModel):
    id: Optional[str] = Field(None, description="Unique identifier for the nodal officer")
    name: str = Field(..., description="Name of the nodal officer")
    organization: str = Field(..., description="Bank, wallet, or e-commerce platform name")
    organization_type: str = Field(..., description="Type of organization (Bank/Wallet/E-commerce/etc.)")
    designation: Optional[str] = Field(None, description="Designation of the nodal officer")
    email: Optional[EmailStr] = Field(None, description="Email address of the nodal officer")
    phone: Optional[str] = Field(None, description="Contact phone number")
    address: Optional[str] = Field(None, description="Office address")
    state: Optional[str] = Field(None, description="State of jurisdiction")
    region: Optional[str] = Field(None, description="Region of jurisdiction")
    is_active: bool = Field(True, description="Whether the nodal officer is currently active")

class SearchResponse(BaseModel):
    items: List[Dict[str, Any]]
    total: int
    page: int
    limit: int
    total_pages: int
