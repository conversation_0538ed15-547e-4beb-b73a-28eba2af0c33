#!/usr/bin/env python3
"""
Security test script to verify CSP and CSRF token endpoint security measures.
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, List

# Test configuration
BASE_URL = "http://localhost:8000"
CSRF_ENDPOINT = f"{BASE_URL}/csrf/token"

async def test_csrf_rate_limiting():
    """Test CSRF token endpoint rate limiting."""
    print("🔒 Testing CSRF Token Rate Limiting...")
    
    async with aiohttp.ClientSession() as session:
        # Test normal requests (should succeed)
        print("  ✓ Testing normal request rate...")
        for i in range(5):
            async with session.get(CSRF_ENDPOINT) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"    Request {i+1}: ✓ Token received: {data['csrf_token'][:8]}...")
                else:
                    print(f"    Request {i+1}: ❌ Failed with status {response.status}")
        
        # Test rate limiting (should trigger after 10 requests per minute)
        print("  ⚠️  Testing rate limiting (sending rapid requests)...")
        rate_limited = False
        for i in range(15):  # Exceed the limit
            async with session.get(CSRF_ENDPOINT) as response:
                if response.status == 429:
                    print(f"    Request {i+1}: ✓ Rate limited (status 429)")
                    rate_limited = True
                    break
                elif response.status == 200:
                    print(f"    Request {i+1}: Token received")
                else:
                    print(f"    Request {i+1}: Unexpected status {response.status}")
            
            # Small delay to avoid overwhelming
            await asyncio.sleep(0.1)
        
        if rate_limited:
            print("  ✅ Rate limiting is working correctly!")
        else:
            print("  ⚠️  Rate limiting may not be working as expected")

async def test_csrf_token_caching():
    """Test CSRF token caching mechanism."""
    print("\n🔄 Testing CSRF Token Caching...")
    
    async with aiohttp.ClientSession() as session:
        # Get first token
        async with session.get(CSRF_ENDPOINT) as response:
            if response.status == 200:
                data1 = await response.json()
                token1 = data1['csrf_token']
                print(f"  First token: {token1[:8]}...")
            else:
                print(f"  ❌ Failed to get first token: {response.status}")
                return
        
        # Get second token immediately (should be cached)
        async with session.get(CSRF_ENDPOINT) as response:
            if response.status == 200:
                data2 = await response.json()
                token2 = data2['csrf_token']
                print(f"  Second token: {token2[:8]}...")
                
                if token1 == token2:
                    print("  ✅ Token caching is working correctly!")
                else:
                    print("  ⚠️  Tokens are different - caching may not be working")
            else:
                print(f"  ❌ Failed to get second token: {response.status}")

async def test_csrf_security_headers():
    """Test CSRF endpoint security headers."""
    print("\n🛡️  Testing CSRF Security Headers...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(CSRF_ENDPOINT) as response:
            headers = response.headers
            
            # Check for security headers
            security_checks = [
                ("Cache-Control", "no-store"),
                ("Pragma", "no-cache"),
                ("Expires", "0"),
                ("X-CSRF-Token", None),  # Should exist
            ]
            
            for header, expected_value in security_checks:
                if header in headers:
                    if expected_value is None:
                        print(f"  ✓ {header}: Present")
                    elif expected_value in headers[header]:
                        print(f"  ✓ {header}: {headers[header]}")
                    else:
                        print(f"  ⚠️  {header}: {headers[header]} (expected: {expected_value})")
                else:
                    print(f"  ❌ {header}: Missing")

async def test_csp_headers():
    """Test Content Security Policy headers."""
    print("\n🔐 Testing Content Security Policy...")
    
    # Test frontend CSP (if frontend is running)
    frontend_url = "http://localhost:3001"
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(frontend_url) as response:
                headers = response.headers
                
                if "Content-Security-Policy" in headers:
                    csp = headers["Content-Security-Policy"]
                    print(f"  ✓ CSP Header found: {csp[:100]}...")
                    
                    # Check for important CSP directives
                    csp_checks = [
                        "default-src",
                        "script-src",
                        "style-src",
                        "img-src",
                        "connect-src",
                        "object-src 'none'",
                        "frame-ancestors 'none'"
                    ]
                    
                    for directive in csp_checks:
                        if directive in csp:
                            print(f"    ✓ {directive}: Present")
                        else:
                            print(f"    ❌ {directive}: Missing")
                else:
                    print("  ❌ CSP Header not found")
                    
        except aiohttp.ClientError:
            print("  ⚠️  Frontend not accessible - skipping CSP test")

async def test_security_response_format():
    """Test the security of response format."""
    print("\n📋 Testing Response Format Security...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(CSRF_ENDPOINT) as response:
            if response.status == 200:
                data = await response.json()
                
                # Check response structure
                expected_fields = ["csrf_token", "expires_in", "algorithm", "security_note"]
                
                for field in expected_fields:
                    if field in data:
                        print(f"  ✓ {field}: Present")
                    else:
                        print(f"  ❌ {field}: Missing")
                
                # Check token format (should be hex)
                token = data.get("csrf_token", "")
                if len(token) == 64 and all(c in "0123456789abcdef" for c in token):
                    print(f"  ✓ Token format: Valid hex (64 chars)")
                else:
                    print(f"  ⚠️  Token format: {len(token)} chars, may not be secure")

async def main():
    """Run all security tests."""
    print("🔒 NCRP Matrix Security Test Suite")
    print("=" * 50)
    
    tests = [
        test_csrf_rate_limiting,
        test_csrf_token_caching,
        test_csrf_security_headers,
        test_csp_headers,
        test_security_response_format
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            print(f"  ❌ Test failed: {str(e)}")
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    print("\n" + "=" * 50)
    print("🏁 Security tests completed!")
    print("\n📝 Summary:")
    print("- CSRF token endpoint now has rate limiting")
    print("- Token caching prevents unnecessary regeneration")
    print("- Security headers prevent token leakage")
    print("- CSP headers protect against XSS attacks")
    print("- Response format includes security metadata")

if __name__ == "__main__":
    asyncio.run(main())
